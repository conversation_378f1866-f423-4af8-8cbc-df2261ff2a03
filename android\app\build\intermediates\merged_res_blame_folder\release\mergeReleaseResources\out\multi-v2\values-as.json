{"logs": [{"outputFile": "com.swipesense.swipesense.app-mergeReleaseResources-62:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1ed5382f5fb92ee142cb7b710335874f\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "34,35,36,37,38,39,40,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3097,3198,3301,3409,3514,3618,3718,10624", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3193,3296,3404,3509,3613,3713,3842,10720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\52445bdda948deffdf6c5ea7962ac4f7\\transformed\\play-services-basement-18.3.0\\res\\values-as\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "121", "endOffsets": "316"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5122", "endColumns": "125", "endOffsets": "5243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d14ee28d9c902f7154f6bed3fc64fea0\\transformed\\appcompat-1.7.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,385,484,591,682,787,907,984,1059,1150,1243,1338,1432,1532,1625,1720,1814,1905,1996,2082,2195,2303,2406,2515,2631,2751,2918,10541", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "380,479,586,677,782,902,979,1054,1145,1238,1333,1427,1527,1620,1715,1809,1900,1991,2077,2190,2298,2401,2510,2626,2746,2913,3015,10619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b940980dadcd84289ecd108819ba235\\transformed\\play-services-base-18.3.0\\res\\values-as\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,446,565,671,797,915,1024,1132,1271,1376,1522,1643,1772,1921,1977,2039", "endColumns": "103,148,118,105,125,117,108,107,138,104,145,120,128,148,55,61,81", "endOffsets": "296,445,564,670,796,914,1023,1131,1270,1375,1521,1642,1771,1920,1976,2038,2120"}, "to": {"startLines": "44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4151,4259,4412,4535,4645,4775,4897,5010,5248,5391,5500,5650,5775,5908,6061,6121,6187", "endColumns": "107,152,122,109,129,121,112,111,142,108,149,124,132,152,59,65,85", "endOffsets": "4254,4407,4530,4640,4770,4892,5005,5117,5386,5495,5645,5770,5903,6056,6116,6182,6268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a4c6936d97d71b0228d8c242453a157\\transformed\\material-1.6.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,406,529,608,673,762,827,886,972,1036,1099,1169,1233,1287,1392,1450,1512,1566,1638,1755,1842,1925,2035,2112,2193,2284,2351,2417,2487,2564,2651,2722,2799,2868,2937,3028,3100,3189,3278,3352,3424,3510,3560,3626,3706,3790,3852,3916,3979,4079,4176,4268,4367", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,101,122,78,64,88,64,58,85,63,62,69,63,53,104,57,61,53,71,116,86,82,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,80", "endOffsets": "222,299,401,524,603,668,757,822,881,967,1031,1094,1164,1228,1282,1387,1445,1507,1561,1633,1750,1837,1920,2030,2107,2188,2279,2346,2412,2482,2559,2646,2717,2794,2863,2932,3023,3095,3184,3273,3347,3419,3505,3555,3621,3701,3785,3847,3911,3974,4074,4171,4263,4362,4443"}, "to": {"startLines": "2,33,41,42,43,63,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3020,3847,3949,4072,6381,6446,6855,6920,6979,7065,7129,7192,7262,7326,7380,7485,7543,7605,7659,7731,7848,7935,8018,8128,8205,8286,8377,8444,8510,8580,8657,8744,8815,8892,8961,9030,9121,9193,9282,9371,9445,9517,9603,9653,9719,9799,9883,9945,10009,10072,10172,10269,10361,10460", "endLines": "5,33,41,42,43,63,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "endColumns": "12,76,101,122,78,64,88,64,58,85,63,62,69,63,53,104,57,61,53,71,116,86,82,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,80", "endOffsets": "272,3092,3944,4067,4146,6441,6530,6915,6974,7060,7124,7187,7257,7321,7375,7480,7538,7600,7654,7726,7843,7930,8013,8123,8200,8281,8372,8439,8505,8575,8652,8739,8810,8887,8956,9025,9116,9188,9277,9366,9440,9512,9598,9648,9714,9794,9878,9940,10004,10067,10167,10264,10356,10455,10536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0308a3384f661323c8933cff8f3f983\\transformed\\browser-1.6.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,269,377", "endColumns": "107,105,107,105", "endOffsets": "158,264,372,478"}, "to": {"startLines": "62,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6273,6535,6641,6749", "endColumns": "107,105,107,105", "endOffsets": "6376,6636,6744,6850"}}]}]}