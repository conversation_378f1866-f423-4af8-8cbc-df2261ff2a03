import { Platform, StyleSheet } from 'react-native';
import { systemOverrides } from './systemOverrides';

/**
 * Create platform-specific shadow styles
 * @param {Object} options - Shadow options
 * @param {number} options.elevation - Android elevation
 * @param {string} options.color - Shadow color
 * @param {Object} options.offset - Shadow offset {width, height}
 * @param {number} options.opacity - Shadow opacity
 * @param {number} options.radius - Shadow radius
 * @returns {Object} - Platform-specific shadow styles
 */
export const createShadow = ({
  elevation = 5,
  color = '#000',
  offset = { width: 0, height: 2 },
  opacity = 0.25,
  radius = 3.84,
} = {}) => {
  return Platform.select({
    ios: {
      shadowColor: color,
      shadowOffset: offset,
      shadowOpacity: opacity,
      shadowRadius: radius,
    },
    android: {
      elevation,
      // On Android, add a subtle border for devices that don't render elevation well
      borderWidth: 0.5,
      borderColor: 'rgba(0,0,0,0.1)',
    },
  });
};

/**
 * Create platform-specific border radius styles
 * Some Android devices render large border radii differently
 * @param {number} radius - Border radius value
 * @returns {Object} - Platform-specific border radius styles
 */
export const createBorderRadius = (radius) => {
  return Platform.select({
    ios: {
      borderRadius: radius,
    },
    android: {
      borderRadius: Math.min(radius, 20), // Cap at 20 for Android for better rendering
    },
  });
};

/**
 * Create platform-specific text styles
 * @param {Object} options - Text style options
 * @returns {Object} - Platform-specific text styles
 */
export const createTextStyle = ({
  fontFamily,
  fontWeight = 'normal',
  letterSpacing,
  ...rest
} = {}) => {
  // Android doesn't handle certain font weights well with some fonts
  const androidFontWeight = fontWeight === '600' ? 'bold' : fontWeight;
  
  return {
    ...Platform.select({
      ios: {
        fontWeight,
        letterSpacing,
      },
      android: {
        fontWeight: androidFontWeight,
        // Android needs less letter spacing
        letterSpacing: letterSpacing ? letterSpacing * 0.8 : undefined,
        // Improve text rendering on Android
        includeFontPadding: false,
      },
    }),
    fontFamily,
    ...rest,
  };
};

/**
 * Create platform-specific input styles
 * @param {Object} options - Input style options
 * @returns {Object} - Platform-specific input styles
 */
export const createInputStyle = ({
  height = 50,
  borderWidth = 1,
  borderColor = '#ccc',
  borderRadius = 8,
  paddingHorizontal = 15,
  ...rest
} = {}) => {
  return {
    height,
    borderWidth,
    borderColor,
    borderRadius,
    paddingHorizontal,
    ...Platform.select({
      ios: {},
      android: {
        // Android needs these for better text input rendering
        paddingVertical: 0,
        textAlignVertical: 'center',
      },
    }),
    ...rest,
  };
};

/**
 * Create platform-specific touchable styles
 * @param {Object} options - Touchable style options
 * @returns {Object} - Platform-specific touchable styles
 */
export const createTouchableStyle = ({
  feedback = true,
  ...rest
} = {}) => {
  return {
    ...Platform.select({
      ios: {
        activeOpacity: 0.7,
      },
      android: {
        // Android-specific props for better touch feedback
        useForeground: feedback,
        borderless: false,
      },
    }),
    ...rest,
  };
};

/**
 * Create platform-specific z-index styles
 * @param {number} zIndex - Z-index value
 * @returns {Object} - Platform-specific z-index styles
 */
export const createZIndex = (zIndex) => {
  return Platform.select({
    ios: {
      zIndex,
    },
    android: {
      elevation: zIndex / 10, // Convert zIndex to appropriate elevation
    },
  });
};

/**
 * Robust detection of the current runtime environment
 * @returns {Object} Environment information
 */
export const getEnvironmentInfo = () => {
  const environmentInfo = {
    platform: Platform.OS,
    isProduction: false,
    isDev: false,
    isExpoGo: false,
    isStandaloneApp: false,
    executionEnvironment: null,
    appOwnership: null,
    releaseChannel: null,
    hasHermes: !!global.HermesInternal,
    hasReactDevTools: !!global.__REACT_DEVTOOLS_GLOBAL_HOOK__
  };

  // Determine if we're in development or production
  environmentInfo.isDev = typeof __DEV__ !== 'undefined' ? __DEV__ : false;
  environmentInfo.isProduction = process.env.NODE_ENV === 'production' || !environmentInfo.isDev;
  
  // Apply system overrides if enabled
  if (systemOverrides.forceProductionMode) {
    environmentInfo.isProduction = true;
    environmentInfo.isDev = false;
    console.log('🔧 System override: Forcing production mode');
  }

  // Try to get Expo Constants without causing a crash
  try {
    const Constants = require('expo-constants').default;
    
    // Store all relevant Expo-specific indicators
    environmentInfo.executionEnvironment = Constants.executionEnvironment;
    environmentInfo.appOwnership = Constants.appOwnership;
    environmentInfo.releaseChannel = Constants.manifest?.releaseChannel;
    environmentInfo.isStandaloneApp = Constants.appOwnership === 'standalone';
    
    // Handle detection with proper logic
    let isExpoGoDetected = false;
    
    // Special case: release builds will always be standalone
    if (environmentInfo.isProduction) {
      isExpoGoDetected = false;
    }
    // Check various indicators of being in Expo Go
    else if (Constants.appOwnership === 'standalone' || 
        Constants.executionEnvironment === 'standalone' ||
        (Constants.manifest?.releaseChannel && !environmentInfo.isDev)) {
      isExpoGoDetected = false;
    }
    // If none of the above apply, base it on appOwnership
    else {
      isExpoGoDetected = Constants.appOwnership === 'expo';
    }
    
    // Apply the override if needed
    if (systemOverrides.forceNotExpoGo) {
      environmentInfo.isExpoGo = false;
      console.log('🔧 System override: Forcing not Expo Go');
    } else {
      environmentInfo.isExpoGo = isExpoGoDetected;
    }
  } catch (e) {
    // If we can't access expo-constants, we're not in an Expo environment
    environmentInfo.isExpoGo = false;
  }

  return environmentInfo;
};

/**
 * Simple check if running in Expo Go
 * @returns {boolean} True if running in Expo Go
 */
export const isRunningInExpoGo = () => {
  const env = getEnvironmentInfo();
  
  // Production builds should NEVER identify as Expo Go
  if (env.isProduction) {
    return false;
  }
  
  return env.isExpoGo;
};

export default {
  createShadow,
  createBorderRadius,
  createTextStyle,
  createInputStyle,
  createTouchableStyle,
  createZIndex,
  getEnvironmentInfo,
  isRunningInExpoGo,
};
