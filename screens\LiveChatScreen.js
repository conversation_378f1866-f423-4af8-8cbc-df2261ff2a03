import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
  collection,
  addDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  doc,
  getDoc,
  updateDoc,
  serverTimestamp
} from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';

const LiveChatScreen = ({ navigation }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [chatId, setChatId] = useState(null);
  const [supportAgentOnline, setSupportAgentOnline] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const flatListRef = useRef(null);

  useEffect(() => {
    if (!auth.currentUser) {
      navigation.navigate('Auth');
      return;
    }

    fetchUserInfo();
    initializeChat();
  }, []);

  const fetchUserInfo = async () => {
    try {
      const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
      if (userDoc.exists()) {
        setUserInfo(userDoc.data());
      }
    } catch (error) {
      console.error('Error fetching user info:', error);
    }
  };

  const initializeChat = async () => {
    try {
      // Check if user has an existing active chat
      const chatsRef = collection(db, 'supportChats');
      const q = query(
        chatsRef,
        where('userId', '==', auth.currentUser.uid),
        where('status', '==', 'active')
      );

      const unsubscribe = onSnapshot(q, (snapshot) => {
        if (!snapshot.empty) {
          // User has an active chat
          const chatDoc = snapshot.docs[0];
          setChatId(chatDoc.id);
          listenToMessages(chatDoc.id);
        } else {
          // Create new chat
          createNewChat();
        }
        setLoading(false);
      });

      return unsubscribe;
    } catch (error) {
      console.error('Error initializing chat:', error);
      setLoading(false);
    }
  };

  const createNewChat = async () => {
    try {
      const chatData = {
        userId: auth.currentUser.uid,
        userEmail: auth.currentUser.email,
        userName: userInfo?.name || 'User',
        status: 'active',
        createdAt: serverTimestamp(),
        lastMessageAt: serverTimestamp(),
        assignedAgent: null,
        isSeller: userInfo?.isSeller || false
      };

      const docRef = await addDoc(collection(db, 'supportChats'), chatData);
      setChatId(docRef.id);

      // Send welcome message
      await sendWelcomeMessage(docRef.id);

      listenToMessages(docRef.id);
    } catch (error) {
      console.error('Error creating chat:', error);
      Alert.alert('Error', 'Failed to start chat. Please try again.');
    }
  };

  const sendWelcomeMessage = async (chatId) => {
    const welcomeMessage = {
      chatId,
      senderId: 'system',
      senderName: 'SwipeSense Support',
      message: 'Hello! Welcome to SwipeSense support. How can we help you today?',
      timestamp: serverTimestamp(),
      type: 'system'
    };

    await addDoc(collection(db, 'chatMessages'), welcomeMessage);
  };

  const listenToMessages = (chatId) => {
    const messagesRef = collection(db, 'chatMessages');
    const q = query(
      messagesRef,
      where('chatId', '==', chatId),
      orderBy('timestamp', 'asc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const chatMessages = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date()
      }));

      setMessages(chatMessages);

      // Scroll to bottom when new messages arrive
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    });

    return unsubscribe;
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !chatId) return;

    const messageText = newMessage.trim();
    setNewMessage('');

    try {
      const messageData = {
        chatId,
        senderId: auth.currentUser.uid,
        senderName: userInfo?.name || 'User',
        message: messageText,
        timestamp: serverTimestamp(),
        type: 'user'
      };

      await addDoc(collection(db, 'chatMessages'), messageData);

      // Update chat last message time
      await updateDoc(doc(db, 'supportChats', chatId), {
        lastMessageAt: serverTimestamp()
      });

      // Send auto-response if no agent available
      setTimeout(() => {
        sendAutoResponse();
      }, 2000);

    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    }
  };

  const sendAutoResponse = async () => {
    if (!chatId) return;

    const autoResponses = [
      "Thank you for your message! Our support team has been notified and will respond shortly.",
      "We're currently experiencing high volume. A support agent will be with you soon.",
      "Your message is important to us. We'll get back to you as soon as possible."
    ];

    const randomResponse = autoResponses[Math.floor(Math.random() * autoResponses.length)];

    const autoMessage = {
      chatId,
      senderId: 'system',
      senderName: 'SwipeSense Support',
      message: randomResponse,
      timestamp: serverTimestamp(),
      type: 'system'
    };

    await addDoc(collection(db, 'chatMessages'), autoMessage);
  };

  const endChat = async () => {
    Alert.alert(
      'End Chat',
      'Are you sure you want to end this chat session?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'End Chat',
          style: 'destructive',
          onPress: async () => {
            try {
              if (chatId) {
                await updateDoc(doc(db, 'supportChats', chatId), {
                  status: 'ended',
                  endedAt: serverTimestamp()
                });
              }
              navigation.goBack();
            } catch (error) {
              console.error('Error ending chat:', error);
            }
          }
        }
      ]
    );
  };

  const renderMessage = ({ item }) => {
    const isUser = item.type === 'user';
    const isSystem = item.type === 'system';

    return (
      <View style={[
        styles.messageContainer,
        isUser ? styles.userMessage : styles.supportMessage
      ]}>
        <View style={[
          styles.messageBubble,
          isUser ? styles.userBubble : styles.supportBubble,
          isSystem && styles.systemBubble
        ]}>
          {!isUser && (
            <Text style={styles.senderName}>{item.senderName}</Text>
          )}
          <Text style={[
            styles.messageText,
            isUser ? styles.userMessageText : styles.supportMessageText,
            isSystem && styles.systemMessageText
          ]}>
            {item.message}
          </Text>
          <Text style={[
            styles.timestamp,
            isUser ? styles.userTimestamp : styles.supportTimestamp
          ]}>
            {item.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaWrapper>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Connecting to support...</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>Live Support</Text>
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusDot,
              { backgroundColor: supportAgentOnline ? '#4CAF50' : '#FF6B6B' }
            ]} />
            <Text style={styles.statusText}>
              {supportAgentOnline ? 'Agent Online' : 'Auto Response'}
            </Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={endChat}
        >
          <Ionicons name="close" size={24} color="#333" />
        </TouchableOpacity>
      </SafeAreaHeader>

      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >

        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
        />

        <View style={styles.inputContainer}>
          <TextInput
            style={styles.textInput}
            value={newMessage}
            onChangeText={setNewMessage}
            placeholder="Type your message..."
            multiline
            maxLength={500}
            returnKeyType="send"
            onSubmitEditing={sendMessage}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              { opacity: newMessage.trim() ? 1 : 0.5 }
            ]}
            onPress={sendMessage}
            disabled={!newMessage.trim()}
          >
            <Ionicons name="send" size={20} color="#FFF" />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  backButton: {
    padding: 8,
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 15,
    paddingBottom: 20,
  },
  messageContainer: {
    marginBottom: 15,
  },
  userMessage: {
    alignItems: 'flex-end',
  },
  supportMessage: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 18,
  },
  userBubble: {
    backgroundColor: '#4ECDC4',
  },
  supportBubble: {
    backgroundColor: '#FFF',
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  systemBubble: {
    backgroundColor: '#F1F3F4',
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  senderName: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
    fontWeight: '600',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  userMessageText: {
    color: '#FFF',
  },
  supportMessageText: {
    color: '#333',
  },
  systemMessageText: {
    color: '#666',
  },
  timestamp: {
    fontSize: 10,
    marginTop: 4,
  },
  userTimestamp: {
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'right',
  },
  supportTimestamp: {
    color: '#999',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 15,
    backgroundColor: '#FFF',
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    fontSize: 16,
    maxHeight: 100,
    marginRight: 10,
    backgroundColor: '#F8F9FA',
  },
  sendButton: {
    backgroundColor: '#4ECDC4',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default LiveChatScreen;
