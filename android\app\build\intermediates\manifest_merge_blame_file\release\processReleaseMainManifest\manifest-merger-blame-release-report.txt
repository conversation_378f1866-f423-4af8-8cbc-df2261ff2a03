1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.swipesense.swipesense"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:2:3-64
11-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:2:20-62
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:3:3-77
12-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:3:20-75
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:4:3-68
13-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:4:20-66
14    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
14-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:5:3-75
14-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:5:20-73
15    <uses-permission android:name="android.permission.VIBRATE" />
15-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:6:3-63
15-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:6:20-61
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:7:3-78
16-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:7:20-76
17    <uses-permission android:name="android.permission.CAMERA" />
17-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:8:3-62
17-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:8:20-60
18    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
18-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:9:3-73
18-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:9:20-71
19
20    <queries>
20-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:10:3-16:13
21        <intent>
21-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:11:5-15:14
22            <action android:name="android.intent.action.VIEW" />
22-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:12:7-58
22-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:12:15-56
23
24            <category android:name="android.intent.category.BROWSABLE" />
24-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:13:7-67
24-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:13:17-65
25
26            <data android:scheme="https" />
26-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:7-37
26-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:13-35
27        </intent>
28        <intent>
28-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
29
30            <!-- Required for picking images from the camera roll if targeting API 30 -->
31            <action android:name="android.media.action.IMAGE_CAPTURE" />
31-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
31-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
32        </intent>
33        <intent>
33-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
34
35            <!-- Required for picking images from the camera if targeting API 30 -->
36            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
36-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
36-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
37        </intent>
38        <intent>
38-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
39            <action android:name="android.intent.action.GET_CONTENT" />
39-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
39-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
40
41            <category android:name="android.intent.category.OPENABLE" />
41-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:13-73
41-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:23-70
42
43            <data android:mimeType="*/*" />
43-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:7-37
44        </intent> <!-- Query open documents -->
45        <intent>
45-->[:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
46            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
46-->[:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
46-->[:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
47        </intent>
48        <intent>
48-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:11:9-17:18
49            <action android:name="android.intent.action.VIEW" />
49-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:12:7-58
49-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:12:15-56
50
51            <data
51-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:7-37
52                android:mimeType="*/*"
53                android:scheme="*" />
53-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:13-35
54        </intent>
55        <intent>
55-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:18:9-27:18
56            <action android:name="android.intent.action.VIEW" />
56-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:12:7-58
56-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:12:15-56
57
58            <category android:name="android.intent.category.BROWSABLE" />
58-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:13:7-67
58-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:13:17-65
59
60            <data
60-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:7-37
61                android:host="pay"
62                android:mimeType="*/*"
63                android:scheme="upi" />
63-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:13-35
64        </intent>
65        <intent>
65-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:28:9-30:18
66            <action android:name="android.intent.action.MAIN" />
66-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:24:9-60
66-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:24:17-58
67        </intent>
68        <intent>
68-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:31:9-35:18
69            <action android:name="android.intent.action.SEND" />
69-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:32:13-65
69-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:32:21-62
70
71            <data android:mimeType="*/*" />
71-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:7-37
72        </intent>
73        <intent>
73-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:36:9-38:18
74            <action android:name="rzp.device_token.share" />
74-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:37:13-61
74-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:37:21-58
75        </intent>
76        <intent>
76-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:8:9-12:18
77
78            <!-- Required for opening tabs if targeting API 30 -->
79            <action android:name="android.support.customtabs.action.CustomTabsService" />
79-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:13-90
79-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a20c8b907e4fc3b68d8e602f81231e3\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:21-87
80        </intent>
81    </queries>
82
83    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
83-->[:expo-updates] D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-79
83-->[:expo-updates] D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-76
84    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
84-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-81
84-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-78
85    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
85-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
85-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
86    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
86-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
86-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
87    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
87-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
87-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
88
89    <uses-feature
89-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
90        android:glEsVersion="0x00020000"
90-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
91        android:required="true" />
91-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
92
93    <permission
93-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
94        android:name="com.swipesense.swipesense.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
94-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
95        android:protectionLevel="signature" />
95-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
96
97    <uses-permission android:name="com.swipesense.swipesense.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
97-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
97-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
98    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
99    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
100    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
101    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
102    <!-- for Samsung -->
103    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
103-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
103-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
104    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
104-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
104-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
105    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
105-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
105-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
106    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
106-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
106-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
107    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
107-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
107-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
108    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
108-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
108-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
109    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
109-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
109-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
110    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
110-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
110-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
111    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
111-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
111-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
112    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
112-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
112-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
113    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
114    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
115    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
116    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
117    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
118    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1698fd1b3cc44d43058567d52ca8085\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
119    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
119-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890791bb102a7b426c44a0bb0df90a0a\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
119-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890791bb102a7b426c44a0bb0df90a0a\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
120
121    <application
121-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:3-35:17
122        android:name="com.swipesense.swipesense.MainApplication"
122-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:16-47
123        android:allowBackup="true"
123-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:162-188
124        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
124-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed5382f5fb92ee142cb7b710335874f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
125        android:extractNativeLibs="false"
126        android:icon="@mipmap/ic_launcher"
126-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:81-115
127        android:label="@string/app_name"
127-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:48-80
128        android:roundIcon="@mipmap/ic_launcher_round"
128-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:116-161
129        android:supportsRtl="true"
129-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:221-247
130        android:theme="@style/AppTheme" >
130-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:17:189-220
131        <meta-data
131-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:18:5-83
132            android:name="expo.modules.updates.ENABLED"
132-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:18:16-59
133            android:value="false" />
133-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:18:60-81
134        <meta-data
134-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:19:5-119
135            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
135-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:19:16-72
136            android:value="@string/expo_runtime_version" />
136-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:19:73-117
137        <meta-data
137-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:20:5-105
138            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
138-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:20:16-80
139            android:value="ALWAYS" />
139-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:20:81-103
140        <meta-data
140-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:21:5-99
141            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
141-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:21:16-79
142            android:value="0" />
142-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:21:80-97
143
144        <activity
144-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:5-34:16
145            android:name="com.swipesense.swipesense.MainActivity"
145-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:15-43
146            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
146-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:44-134
147            android:exported="true"
147-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:256-279
148            android:launchMode="singleTask"
148-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:135-166
149            android:screenOrientation="portrait"
149-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:280-316
150            android:theme="@style/Theme.App.SplashScreen"
150-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:210-255
151            android:windowSoftInputMode="adjustResize" >
151-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:22:167-209
152            <intent-filter>
152-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:23:7-26:23
153                <action android:name="android.intent.action.MAIN" />
153-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:24:9-60
153-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:24:17-58
154
155                <category android:name="android.intent.category.LAUNCHER" />
155-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:25:9-68
155-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:25:19-66
156            </intent-filter>
157            <intent-filter>
157-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:27:7-33:23
158                <action android:name="android.intent.action.VIEW" />
158-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:12:7-58
158-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:12:15-56
159
160                <category android:name="android.intent.category.DEFAULT" />
160-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:29:9-67
160-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:29:19-65
161                <category android:name="android.intent.category.BROWSABLE" />
161-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:13:7-67
161-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:13:17-65
162
163                <data android:scheme="swipesense" />
163-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:7-37
163-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:13-35
164                <data android:scheme="exp+swipesense" />
164-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:7-37
164-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:14:13-35
165            </intent-filter>
166        </activity>
167        <activity
167-->[:react-native-razorpay] D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-10:86
168            android:name="com.razorpay.CheckoutActivity"
168-->[:react-native-razorpay] D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-57
169            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
169-->[:react-native-razorpay] D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-83
170            android:exported="false"
170-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:45:13-37
171            android:theme="@style/CheckoutTheme" >
171-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:46:13-49
172            <intent-filter>
172-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:47:13-49:29
173                <action android:name="android.intent.action.MAIN" />
173-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:24:9-60
173-->D:\app\StyleApp\android\app\src\main\AndroidManifest.xml:24:17-58
174            </intent-filter>
175        </activity>
176
177        <meta-data
177-->[:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
178            android:name="org.unimodules.core.AppLoader#react-native-headless"
178-->[:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
179            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
179-->[:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
180        <meta-data
180-->[:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
181            android:name="com.facebook.soloader.enabled"
181-->[:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
182            android:value="true" />
182-->[:expo-modules-core] D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
183
184        <service
184-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
185            android:name="com.google.android.gms.metadata.ModuleDependencies"
185-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
186            android:enabled="false"
186-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
187            android:exported="false" >
187-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
188            <intent-filter>
188-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
189                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
189-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
189-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
190            </intent-filter>
191
192            <meta-data
192-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
193                android:name="photopicker_activity:0:required"
193-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
194                android:value="" />
194-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
195        </service>
196
197        <activity
197-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
198            android:name="com.canhub.cropper.CropImageActivity"
198-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
199            android:exported="true"
199-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
200            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
200-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
201        <provider
201-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
202            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
202-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
203            android:authorities="com.swipesense.swipesense.ImagePickerFileProvider"
203-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
204            android:exported="false"
204-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
205            android:grantUriPermissions="true" >
205-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
206            <meta-data
206-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:51:13-53:71
207                android:name="android.support.FILE_PROVIDER_PATHS"
207-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:52:17-67
208                android:resource="@xml/image_picker_provider_paths" />
208-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:53:17-68
209        </provider>
210        <provider
210-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
211            android:name="com.canhub.cropper.CropFileProvider"
211-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
212            android:authorities="com.swipesense.swipesense.cropper.fileprovider"
212-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
213            android:exported="false"
213-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
214            android:grantUriPermissions="true" >
214-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10b099e99495a469d20423a3ae446b36\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
215            <meta-data
215-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:51:13-53:71
216                android:name="android.support.FILE_PROVIDER_PATHS"
216-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:52:17-67
217                android:resource="@xml/library_file_paths" />
217-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:53:17-68
218        </provider>
219        <provider
219-->[:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
220            android:name="expo.modules.filesystem.FileSystemFileProvider"
220-->[:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
221            android:authorities="com.swipesense.swipesense.FileSystemFileProvider"
221-->[:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
222            android:exported="false"
222-->[:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
223            android:grantUriPermissions="true" >
223-->[:expo-file-system] D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
224            <meta-data
224-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:51:13-53:71
225                android:name="android.support.FILE_PROVIDER_PATHS"
225-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:52:17-67
226                android:resource="@xml/file_system_provider_paths" />
226-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab03528e76e52ae73eb8029838664d6\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:53:17-68
227        </provider>
228
229        <meta-data
229-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:11:9-13:43
230            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
230-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:12:13-84
231            android:value="GlideModule" />
231-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1b27298caaaece40104acad1e8f983f\transformed\okhttp3-integration-4.12.0\AndroidManifest.xml:13:13-40
232
233        <provider
233-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:52:9-60:20
234            android:name="androidx.startup.InitializationProvider"
234-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:53:13-67
235            android:authorities="com.swipesense.swipesense.androidx-startup"
235-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:54:13-68
236            android:exported="false" >
236-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:55:13-37
237            <meta-data
237-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:57:13-59:52
238                android:name="com.razorpay.RazorpayInitializer"
238-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:58:17-64
239                android:value="androidx.startup" />
239-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:59:17-49
240            <meta-data
240-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
241                android:name="androidx.emoji2.text.EmojiCompatInitializer"
241-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
242                android:value="androidx.startup" />
242-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\38f63e4799288790f1aa4b75872e508d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
243            <meta-data
243-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
244                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
244-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
245                android:value="androidx.startup" />
245-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78deea635d44fcddad14d7628046d178\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
246            <meta-data
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
247                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
248                android:value="androidx.startup" />
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
249        </provider>
250
251        <activity
251-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:62:9-65:75
252            android:name="com.razorpay.MagicXActivity"
252-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:63:13-55
253            android:exported="false"
253-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:64:13-37
254            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
254-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:65:13-72
255
256        <meta-data
256-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:67:9-69:58
257            android:name="com.razorpay.plugin.googlepay_all"
257-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:68:13-61
258            android:value="com.razorpay.RzpGpayMerged" />
258-->[com.razorpay:standard-core:1.6.50] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15163c0957f19306034017803fd9e35\transformed\standard-core-1.6.50\AndroidManifest.xml:69:13-55
259
260        <activity
260-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
261            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
261-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
262            android:excludeFromRecents="true"
262-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
263            android:exported="false"
263-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
264            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
264-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
265        <!--
266            Service handling Google Sign-In user revocation. For apps that do not integrate with
267            Google Sign-In, this service will never be started.
268        -->
269        <service
269-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
270            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
270-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
271            android:exported="true"
271-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
272            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
272-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
273            android:visibleToInstantApps="true" />
273-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c05acaa8759fed8c36122362d1485654\transformed\play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
274        <service
274-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:9-17:19
275            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
275-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-91
276            android:exported="false" >
276-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-37
277            <intent-filter android:priority="-1" >
277-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
277-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
278                <action android:name="com.google.firebase.MESSAGING_EVENT" />
278-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
278-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
279            </intent-filter>
280        </service>
281
282        <receiver
282-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:20
283            android:name="expo.modules.notifications.service.NotificationsService"
283-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-83
284            android:enabled="true"
284-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-35
285            android:exported="false" >
285-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
286            <intent-filter android:priority="-1" >
286-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-30:29
286-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:28-49
287                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
287-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:17-88
287-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:25-85
288                <action android:name="android.intent.action.BOOT_COMPLETED" />
288-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
288-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
289                <action android:name="android.intent.action.REBOOT" />
289-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:17-71
289-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:25-68
290                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
290-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:17-82
290-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:25-79
291                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
291-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-82
291-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:25-79
292                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
292-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-84
292-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:25-81
293            </intent-filter>
294        </receiver>
295
296        <activity
296-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:9-40:75
297            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
297-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-92
298            android:excludeFromRecents="true"
298-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-46
299            android:exported="false"
299-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-37
300            android:launchMode="standard"
300-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-42
301            android:noHistory="true"
301-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-37
302            android:taskAffinity=""
302-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-36
303            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
303-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-72
304
305        <receiver
305-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
306            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
306-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
307            android:exported="true"
307-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
308            android:permission="com.google.android.c2dm.permission.SEND" >
308-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
309            <intent-filter>
309-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
310                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
310-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
310-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
311            </intent-filter>
312
313            <meta-data
313-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
314                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
314-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
315                android:value="true" />
315-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
316        </receiver>
317        <!--
318             FirebaseMessagingService performs security checks at runtime,
319             but set to not exported to explicitly avoid allowing another app to call it.
320        -->
321        <service
321-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
322            android:name="com.google.firebase.messaging.FirebaseMessagingService"
322-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
323            android:directBootAware="true"
323-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
324            android:exported="false" >
324-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
325            <intent-filter android:priority="-500" >
325-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
325-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
326                <action android:name="com.google.firebase.MESSAGING_EVENT" />
326-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
326-->[:expo-notifications] D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
327            </intent-filter>
328        </service>
329        <service
329-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
330            android:name="com.google.firebase.components.ComponentDiscoveryService"
330-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
331            android:directBootAware="true"
331-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
332            android:exported="false" >
332-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
333            <meta-data
333-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
334                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
334-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
335                android:value="com.google.firebase.components.ComponentRegistrar" />
335-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
336            <meta-data
336-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
337                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
337-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
338                android:value="com.google.firebase.components.ComponentRegistrar" />
338-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaf72e549f1d9eee87e815abd4f15a0\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
339            <meta-data
339-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
340                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
340-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
341                android:value="com.google.firebase.components.ComponentRegistrar" />
341-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
342            <meta-data
342-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
343                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
343-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
344                android:value="com.google.firebase.components.ComponentRegistrar" />
344-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\512c11c96971e2bae7849ec011617be7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
345            <meta-data
345-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
346                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
346-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
347                android:value="com.google.firebase.components.ComponentRegistrar" />
347-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\756920db57c2f6b3e680eb94f5bded2f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
348            <meta-data
348-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
349                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
349-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
350                android:value="com.google.firebase.components.ComponentRegistrar" />
350-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
351            <meta-data
351-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
352                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
352-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
353                android:value="com.google.firebase.components.ComponentRegistrar" />
353-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fe5b1c5a0f0828f7d35aca7d93f6777\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
354        </service> <!-- Needs to be explicitly declared on P+ -->
355        <uses-library
355-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
356            android:name="org.apache.http.legacy"
356-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
357            android:required="false" />
357-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1458a336835288f980e505673aa63c0\transformed\play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
358
359        <activity
359-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
360            android:name="com.google.android.gms.common.api.GoogleApiActivity"
360-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:19-85
361            android:exported="false"
361-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:22:19-43
362            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
362-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b940980dadcd84289ecd108819ba235\transformed\play-services-base-18.3.0\AndroidManifest.xml:21:19-78
363
364        <provider
364-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
365            android:name="com.google.firebase.provider.FirebaseInitProvider"
365-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
366            android:authorities="com.swipesense.swipesense.firebaseinitprovider"
366-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
367            android:directBootAware="true"
367-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
368            android:exported="false"
368-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
369            android:initOrder="100" />
369-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1125dfe554f662f7601e25346267f03\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
370
371        <service
371-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
372            android:name="androidx.room.MultiInstanceInvalidationService"
372-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
373            android:directBootAware="true"
373-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
374            android:exported="false" />
374-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eebe9f5d7b02e73a51505547b3d6577\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
375
376        <meta-data
376-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
377            android:name="com.google.android.gms.version"
377-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
378            android:value="@integer/google_play_services_version" />
378-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52445bdda948deffdf6c5ea7962ac4f7\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
379
380        <receiver
380-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
381            android:name="androidx.profileinstaller.ProfileInstallReceiver"
381-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
382            android:directBootAware="false"
382-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
383            android:enabled="true"
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
384            android:exported="true"
384-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
385            android:permission="android.permission.DUMP" >
385-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
386            <intent-filter>
386-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
387                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
387-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
387-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
388            </intent-filter>
389            <intent-filter>
389-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
390                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
390-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
390-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
391            </intent-filter>
392            <intent-filter>
392-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
393                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
393-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
393-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
394            </intent-filter>
395            <intent-filter>
395-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
396                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
396-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
396-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ec7da6925d2c4307567c7b98223d07b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
397            </intent-filter>
398        </receiver>
399
400        <service
400-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
401            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
401-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
402            android:exported="false" >
402-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
403            <meta-data
403-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
404                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
404-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
405                android:value="cct" />
405-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd42d453ced097b9754ed80e51e21c75\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
406        </service>
407        <service
407-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
408            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
408-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
409            android:exported="false"
409-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
410            android:permission="android.permission.BIND_JOB_SERVICE" >
410-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
411        </service>
412
413        <receiver
413-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
414            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
414-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
415            android:exported="false" />
415-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb184c428191ebd50dc54558f4f5f385\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
416    </application>
417
418</manifest>
