import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  ActivityIndicator,
  RefreshControl,
  Image,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  updateDoc,
  doc,
  deleteDoc
} from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import { checkAdminStatus } from '../utils/adminUtils';

const AdminContentModeration = ({ navigation }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [listings, setListings] = useState([]);
  const [filteredListings, setFilteredListings] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedListing, setSelectedListing] = useState(null);
  const [listingModalVisible, setListingModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingListing, setEditingListing] = useState(null);
  const [editForm, setEditForm] = useState({
    title: '',
    description: '',
    price: '',
    category: '',
    brand: '',
    size: ''
  });
  const [editLoading, setEditLoading] = useState(false);

  useEffect(() => {
    checkAdminAccess();
  }, []);

  useEffect(() => {
    if (isAdmin) {
      fetchListings();
    }
  }, [isAdmin]);

  useEffect(() => {
    filterListings();
  }, [listings, searchQuery]);

  const checkAdminAccess = async () => {
    try {
      const adminStatus = await checkAdminStatus(auth.currentUser.uid);
      setIsAdmin(adminStatus);

      if (!adminStatus) {
        Alert.alert(
          'Access Denied',
          'You do not have administrator privileges.',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const fetchListings = async () => {
    try {
      setLoading(true);

      // Always fetch all listings since we only have "all" filter option
      let q = query(collection(db, 'clothingItems'), orderBy('createdAt', 'desc'));

      const snapshot = await getDocs(q);
      const listingsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        // Ensure moderationStatus exists for filtering
        moderationStatus: doc.data().moderationStatus || 'pending'
      }));

      console.log(`Fetched ${listingsData.length} listings from clothingItems collection`);
      setListings(listingsData);
    } catch (error) {
      console.error('Error fetching listings:', error);
      Alert.alert('Error', 'Failed to fetch listings');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const filterListings = () => {
    let filtered = listings;

    if (searchQuery.trim()) {
      filtered = listings.filter(listing =>
        listing.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        listing.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        listing.category?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredListings(filtered);
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchListings();
  };

  const openListingModal = (listing) => {
    setSelectedListing(listing);
    setListingModalVisible(true);
  };



  const deleteListing = async (listing) => {
    Alert.alert(
      'Delete Listing',
      `Are you sure you want to permanently delete "${listing.title}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteDoc(doc(db, 'clothingItems', listing.id));

              Alert.alert('Success', 'Listing deleted successfully');
              setListingModalVisible(false);
              fetchListings();
            } catch (error) {
              console.error('Error deleting listing:', error);
              Alert.alert('Error', 'Failed to delete listing');
            }
          }
        }
      ]
    );
  };

  const editListing = (listing) => {
    setEditingListing(listing);
    setEditForm({
      title: listing.title || '',
      description: listing.description || '',
      price: listing.price?.toString() || '',
      category: listing.category || '',
      brand: listing.brand || '',
      size: listing.size || ''
    });
    setEditModalVisible(true);
  };

  const handleEditFormChange = (field, value) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const saveEditedListing = async () => {
    if (!editingListing) return;

    // Validation
    if (!editForm.title.trim()) {
      Alert.alert('Error', 'Title is required');
      return;
    }

    if (!editForm.price.trim()) {
      Alert.alert('Error', 'Price is required');
      return;
    }

    const priceValue = parseFloat(editForm.price.trim());
    if (isNaN(priceValue) || priceValue <= 0) {
      Alert.alert('Error', 'Please enter a valid price');
      return;
    }

    setEditLoading(true);
    try {
      await updateDoc(doc(db, 'clothingItems', editingListing.id), {
        title: editForm.title.trim(),
        description: editForm.description.trim(),
        price: priceValue,
        category: editForm.category.trim(),
        brand: editForm.brand.trim(),
        size: editForm.size.trim(),
        updatedAt: new Date(),
        lastModifiedBy: auth.currentUser.uid
      });

      Alert.alert('Success', 'Listing updated successfully');
      setEditModalVisible(false);
      setEditingListing(null);
      fetchListings(); // Refresh the listings
    } catch (error) {
      console.error('Error updating listing:', error);
      Alert.alert('Error', 'Failed to update listing');
    } finally {
      setEditLoading(false);
    }
  };

  const cancelEdit = () => {
    setEditModalVisible(false);
    setEditingListing(null);
    setEditForm({
      title: '',
      description: '',
      price: '',
      category: '',
      brand: '',
      size: ''
    });
  };







  const renderListingItem = ({ item }) => (
    <TouchableOpacity
      style={styles.listingCard}
      onPress={() => openListingModal(item)}
    >
      <View style={styles.listingHeader}>
        <Image
          source={{ uri: item.imageUrls?.[0] || item.imageUrl || 'https://via.placeholder.com/100' }}
          style={styles.listingImage}
        />
        <View style={styles.listingInfo}>
          <Text style={styles.listingTitle} numberOfLines={2}>
            {item.title || 'No Title'}
          </Text>
          <Text style={styles.listingPrice}>₹{item.price || 0}</Text>
          <Text style={styles.listingCategory}>{item.category || 'No Category'}</Text>
          <Text style={styles.listingDate}>
            {item.createdAt.toLocaleDateString()}
          </Text>
        </View>

      </View>

      <View style={styles.listingActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => openListingModal(item)}
        >
          <Ionicons name="eye-outline" size={16} color="#007AFF" />
          <Text style={styles.actionText}>View</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#4ECDC4' }]}
          onPress={() => editListing(item)}
        >
          <Ionicons name="create-outline" size={16} color="white" />
          <Text style={[styles.actionText, { color: 'white' }]}>Edit</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#FF6B6B' }]}
          onPress={() => deleteListing(item)}
        >
          <Ionicons name="trash-outline" size={16} color="white" />
          <Text style={[styles.actionText, { color: 'white' }]}>Remove</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
          <Text style={styles.loadingText}>Loading listings...</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  if (!isAdmin) {
    return (
      <SafeAreaWrapper>
        <View style={styles.accessDenied}>
          <Ionicons name="lock-closed-outline" size={80} color="#ccc" />
          <Text style={styles.accessDeniedText}>Access Denied</Text>
          <Text style={styles.accessDeniedSubtext}>
            You don't have administrator privileges.
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Content Moderation</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
          <Ionicons name="refresh" size={24} color="#FF6B6B" />
        </TouchableOpacity>
      </SafeAreaHeader>

      <View style={styles.container}>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search listings by title, description, or category..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>





        {/* Listings List */}
        <FlatList
          data={filteredListings}
          renderItem={renderListingItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="shirt-outline" size={80} color="#ccc" />
              <Text style={styles.emptyText}>No listings found</Text>
              <Text style={styles.emptySubtext}>
                {searchQuery ? 'Try adjusting your search' : 'Listings will appear here'}
              </Text>
            </View>
          }
          contentContainerStyle={styles.listContainer}
        />

        {/* Listing Details Modal */}
        <Modal
          visible={listingModalVisible}
          animationType="slide"
          onRequestClose={() => setListingModalVisible(false)}
        >
          <SafeAreaWrapper>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <TouchableOpacity
                  onPress={() => setListingModalVisible(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
                <Text style={styles.modalTitle}>Listing Details</Text>
              </View>

              {selectedListing && (
                <ScrollView style={styles.modalContent}>
                  <Image
                    source={{ uri: selectedListing.imageUrls?.[0] || selectedListing.imageUrl || 'https://via.placeholder.com/300' }}
                    style={styles.modalImage}
                  />

                  <View style={styles.detailCard}>
                    <Text style={styles.detailLabel}>Title</Text>
                    <Text style={styles.detailValue}>{selectedListing.title || 'No Title'}</Text>
                  </View>

                  <View style={styles.detailCard}>
                    <Text style={styles.detailLabel}>Price</Text>
                    <Text style={styles.detailValue}>₹{selectedListing.price || 0}</Text>
                  </View>

                  <View style={styles.detailCard}>
                    <Text style={styles.detailLabel}>Category</Text>
                    <Text style={styles.detailValue}>{selectedListing.category || 'No Category'}</Text>
                  </View>

                  <View style={styles.detailCard}>
                    <Text style={styles.detailLabel}>Description</Text>
                    <Text style={styles.detailValue}>
                      {selectedListing.description || 'No Description'}
                    </Text>
                  </View>


                </ScrollView>
              )}
            </View>
          </SafeAreaWrapper>
        </Modal>

        {/* Edit Listing Modal */}
        <Modal
          visible={editModalVisible}
          animationType="slide"
          onRequestClose={cancelEdit}
        >
          <SafeAreaWrapper>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <TouchableOpacity
                  onPress={cancelEdit}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
                <View style={styles.modalTitleContainer}>
                  <Text style={styles.modalTitle}>Edit Listing</Text>
                </View>
                <TouchableOpacity
                  onPress={saveEditedListing}
                  style={styles.saveButton}
                  disabled={editLoading}
                >
                  {editLoading ? (
                    <ActivityIndicator size="small" color="white" />
                  ) : (
                    <Text style={styles.saveButtonText}>Save</Text>
                  )}
                </TouchableOpacity>
              </View>

              {editingListing && (
                <ScrollView style={styles.modalContent}>
                  <View style={styles.editForm}>
                    <Text style={styles.editLabel}>Title *</Text>
                    <TextInput
                      style={styles.editInput}
                      value={editForm.title}
                      onChangeText={(value) => handleEditFormChange('title', value)}
                      placeholder="Enter listing title"
                      editable={!editLoading}
                    />

                    <Text style={styles.editLabel}>Description</Text>
                    <TextInput
                      style={[styles.editInput, styles.editTextArea]}
                      value={editForm.description}
                      onChangeText={(value) => handleEditFormChange('description', value)}
                      placeholder="Enter description"
                      multiline
                      numberOfLines={4}
                      editable={!editLoading}
                    />

                    <Text style={styles.editLabel}>Price *</Text>
                    <View style={styles.priceInputContainer}>
                      <Text style={styles.currencySymbol}>₹</Text>
                      <TextInput
                        style={styles.priceInput}
                        value={editForm.price}
                        onChangeText={(value) => handleEditFormChange('price', value)}
                        placeholder="0"
                        keyboardType="numeric"
                        editable={!editLoading}
                      />
                    </View>

                    <Text style={styles.editLabel}>Category</Text>
                    <TextInput
                      style={styles.editInput}
                      value={editForm.category}
                      onChangeText={(value) => handleEditFormChange('category', value)}
                      placeholder="Enter category"
                      editable={!editLoading}
                    />

                    <Text style={styles.editLabel}>Brand</Text>
                    <TextInput
                      style={styles.editInput}
                      value={editForm.brand}
                      onChangeText={(value) => handleEditFormChange('brand', value)}
                      placeholder="Enter brand"
                      editable={!editLoading}
                    />

                    <Text style={styles.editLabel}>Size</Text>
                    <TextInput
                      style={styles.editInput}
                      value={editForm.size}
                      onChangeText={(value) => handleEditFormChange('size', value)}
                      placeholder="Enter size"
                      editable={!editLoading}
                    />
                  </View>
                </ScrollView>
              )}
            </View>
          </SafeAreaWrapper>
        </Modal>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },

  refreshButton: {
    padding: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },

  listContainer: {
    paddingHorizontal: 16,
  },
  listingCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  listingHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  listingImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  listingInfo: {
    flex: 1,
  },
  listingTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  listingPrice: {
    fontSize: 14,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginBottom: 2,
  },
  listingCategory: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  listingDate: {
    fontSize: 12,
    color: '#999',
  },

  listingActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    flex: 1,
    marginHorizontal: 2,
    justifyContent: 'center',
  },
  actionText: {
    marginLeft: 4,
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  accessDenied: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  accessDeniedText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 20,
  },
  accessDeniedSubtext: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 10,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  modalCloseButton: {
    padding: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  modalContent: {
    padding: 16,
  },
  modalImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    marginBottom: 16,
  },
  detailCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#333',
  },

  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#4CAF50',
    minWidth: 70,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  editForm: {
    padding: 16,
  },
  editLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    marginTop: 16,
  },
  editInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
    marginBottom: 8,
  },
  editTextArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: 'white',
    marginBottom: 8,
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    paddingLeft: 12,
    paddingRight: 8,
  },
  priceInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
  },
});

export default AdminContentModeration;
