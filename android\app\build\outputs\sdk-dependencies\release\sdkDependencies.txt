# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "2.0.21"
  }
  digests {
    sha256: "q/H\a`\355\356\344\212\2046\236k\350\237j\265#u@\213\262\312;\341N\366c\367+\3561"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.react"
    artifactId: "react-android"
    version: "0.79.2"
  }
  digests {
    sha256: "\336\330\236\234\201+\252\346\264\375\336c\234\272\326G@\023K\215`\220\252\377\322\321\317\235\275\020\226\262"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.0"
  }
  digests {
    sha256: "\307#\001\274s\av\234\326\303\340\355\270\332X7\347\336\246\375\211\245\315\355\374\374\207\275X\030P\341"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.1"
  }
  digests {
    sha256: "\031\272P\320\224\3076\216\336\033L\317\021\225\316\270>5\227\a6Y?\202>Z\367\026\370\320]p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.1"
  }
  digests {
    sha256: ",\'\336\031\2255gP\005U0fYzK \372\036\352|\"\212\264\357k2\265\3769\312\037Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.2"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.2"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.8.1"
  }
  digests {
    sha256: "\2414\332\317Nex\262\2332\351z\245\005H\320\234\264^<\263U\034\347z\302~U\342e\330\365"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\363\324\365\336\0349\033\274\302\017;45\314\272\300\023R\036v\266\220-}Yc^\301\\\037y~"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.8.1"
  }
  digests {
    sha256: "\371\f,v\312\262\373=\270\233\370\247B\273O6\313\244>RG*\357\363\221\236\221C\275^\300r"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.1.0"
  }
  digests {
    sha256: "\326\005u\352\343\223P\346#HX\274\235}wSupz\350*hNl\257\177>A\241.%\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.3.0"
  }
  digests {
    sha256: "l\021\256>\262\335\177\0277?\221\234LUzp\344\317\211\033\300\311\266i&\240\246D]eCR"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.1"
  }
  digests {
    sha256: "^\353Yd\250\355\262Yf\325\314y3\376\211><X\342E\254C=5\204x\262\vm\t\324\343"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.2"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.6.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.2"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\237,\026\343:U\272\215g\243b:U\276\377\362\354\200d>?\n\222g`\337\035\225|?\212\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.2"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-ktx"
    version: "1.2.0"
  }
  digests {
    sha256: "\303?\234\275\223\036a\220\3128\252\t\277\212z\212\0319\035K\017\267\247`ZkY\362\324%\200\321"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "(\240\2704\364\016\335R\342\370\001\v\3340W\322\240\314v\252\245\254\223\021\255\260\271\316\221\234\251\314"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.6.2"
  }
  digests {
    sha256: "\212\316\2311?\n\346\257G\031K\312\376(\363D\343c\364\322\223\370K+\227\264\201s[\004\336\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.2"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\177\300\372#J3!\261\363M\265\206+\027\332\203\321\306,\033\306n\302\375O\033\260\247q\254\372\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.2"
  }
  digests {
    sha256: "{\307\334\272\261v6\354\ao\022\257\344\320&q&\\8\224W\261\263f\263z\016\214\271\036-\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.1.0"
  }
  digests {
    sha256: "\250;Y|\322@\235\301w\367\3712\367\304\b.\367\265P\213i\266\304\272q`~u\333\331\231q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.1"
  }
  digests {
    sha256: ">E\225\315\251\276\343\222qYY6mpy\a\004\377$\fwenp/<\202 \263\331$Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.10.0"
  }
  digests {
    sha256: ";\216\fQ\350\252\0322\357\266\262\324\030\032g\334\200xG\211\301f\277\343\a\367\016\344A\205\325\020"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.1.0"
  }
  digests {
    sha256: "g\316\270&|\'EW\303WX\353\276D\211\n :Uy\275+\bM\363Mpv<r\370\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.1.0"
  }
  digests {
    sha256: ",\347\220l\321\336\240Z\354\201\227]\262-T8#Y\300Z!\262Rz\330H\274`\366\262r\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.facebook.fbjni"
    artifactId: "fbjni"
    version: "0.7.0"
  }
  digests {
    sha256: "~1\232\341\020\254^^\361\211\004\027\n\352\\>u>\221]\031f\231\327\3759\323l\216\035\3766"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "nativeloader"
    version: "0.12.1"
  }
  digests {
    sha256: "\227\035\355\206\000\234\n\305o\262\'^\022\217\205.}\313\304\032@\315y\226\210\370\241\256\275\345\031]"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fresco"
    version: "3.6.0"
  }
  digests {
    sha256: "\333\202\022\214\v\r \233/H\257\330B\222\305\365K;\376@{(\3722\272\277\336\225\221K\003\371"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "soloader"
    version: "3.6.0"
  }
  digests {
    sha256: "\373\276\n7\331\033\206\3242\r\a(\004\f2dY\232\224\263\032\035\344\037\222\242^\024\177\1775\357"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fbcore"
    version: "3.6.0"
  }
  digests {
    sha256: "o\331\214+W7\356V\251\311\017\0344\330}$\345\3232O\327\374,\312mnt\261\300\264<\351"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "soloader"
    version: "0.12.1"
  }
  digests {
    sha256: "[\306\341]q/\220\240\243\211\r\"\333\016\357_\350\236\363\374\263\256\212\241\370\366\246\331\225G\310\222"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "annotation"
    version: "0.12.1"
  }
  digests {
    sha256: "\366\335\325Rh\277\030$\"]\367\243^\301\270\263<\371\210U\246\311\301S\321\211\211\236^\364\244\030"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "ui-common"
    version: "3.6.0"
  }
  digests {
    sha256: "\000\0361\002d\310\272\316y$=\026\254\003\373`\272\347\255\374,J\300\306k\000\006\302I \217\360"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "ui-core"
    version: "3.6.0"
  }
  digests {
    sha256: "\265\025\306\233\241 \306\036\241W\342Ym\337:\222&3\234\016j\317\311kP\277\226aI\261\301A"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "middleware"
    version: "3.6.0"
  }
  digests {
    sha256: "\226a\364|\261\025j\nrF\201\230\002\311\260\027\272Qp\366\256z\276\3371\375,\366b\343\334\020"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "drawee"
    version: "3.6.0"
  }
  digests {
    sha256: "`CU\031\313v\316\347\2079\331(\304j\255\251\030\367l\271\223\267Z\371*\261~\235tk\002w"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline"
    version: "3.6.0"
  }
  digests {
    sha256: "W\360A\225\345\327\'6q\316F}\016#\304\207\263\302\310tj\306%\177*\'S\314\aq\366\237"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.parse.bolts"
    artifactId: "bolts-tasks"
    version: "1.4.0"
  }
  digests {
    sha256: "\233\305\036>\312\205:\235\362\373\254b\3021}\262\311\352a\233\225\265S\0340D\002C\332\254\316\342"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "urimod"
    version: "3.6.0"
  }
  digests {
    sha256: "T\274\b\206\345y\353\b3)t\222\267\330\234\264\306\023\312\225\303y\272\314A\255\376)S\232\221\367"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "vito-source"
    version: "3.6.0"
  }
  digests {
    sha256: "\265\303\305\300\207\332\261\231\254\212\376\203\227\372\211\314\363\206\337{)\250\333w\263a%Wj\362\344\221"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-base"
    version: "3.6.0"
  }
  digests {
    sha256: "i\312\255\a\3767\000\264\217;Vf%\004\371\301#]\242\351w\270\352\315\235\362Q2\036\226I\306"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.infer.annotation"
    artifactId: "infer-annotation"
    version: "0.18.0"
  }
  digests {
    sha256: "\312\357\272\223VC\334\226\'X\247`\361bz@\"\025\257\003M\226\300\364\204\326;\177\335\312\231/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-annotations-jvm"
    version: "1.3.72"
  }
  digests {
    sha256: ">\343\245m\324Q\343?\203R\340\322\200:U\312\033\221V\327\370\250\340u\233\370\341\207\377\215\262J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-native"
    version: "3.6.0"
  }
  digests {
    sha256: "\\\276\257\260\367\331\034\a\216%k\261\257)\201I?7\262\243\206\337CY1\t\326\253\357w\222\331"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-ashmem"
    version: "3.6.0"
  }
  digests {
    sha256: "\v+\032\215}nX\0061,K{F\241U7\343\255\022\247\252\237\303\254&$e00\202\271F"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-native"
    version: "3.6.0"
  }
  digests {
    sha256: "\315\300\314\341\b\274\253i\332+v\036\3558\236G\222\371\224\260;RK\330&1l)2\353\f\370"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-java"
    version: "3.6.0"
  }
  digests {
    sha256: "E\004\375\305c\346\231t\2130\017>5\'\233\340\221\214\004\230\036~\366\037\221\226c\333\326\236h?"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagefilters"
    version: "3.6.0"
  }
  digests {
    sha256: "PDt^\205\3735\027~\216+\200\337\202\300*\226\212m\270\212\377\347\021\344\373\0008\222M2\275"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagetranscoder"
    version: "3.6.0"
  }
  digests {
    sha256: "\r\217\205Ya|\210v=\307^`\005\374`\343{\375k\262\017\345\271z\030|\253\357\302\261\032|"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-okhttp3"
    version: "3.6.0"
  }
  digests {
    sha256: "\372\240\036\3209J^\310l\376\234\006\341\006\243!y\344\373\203x\347\256\357\336X2\025\224\272e\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.9.2"
  }
  digests {
    sha256: ";.\341\267h\301\337(\303\r/\346\263\215\332M,Q\222\020\343\f\243\322yPa\214V<\222\336"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "2.9.0"
  }
  digests {
    sha256: "\271%\\\026;~\334\v\204\006U\235fW\234l2\336\240\037i\031C\272\305\323\375\275\020\366\233D"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.yoga"
    artifactId: "proguard-annotations"
    version: "1.19.0"
  }
  digests {
    sha256: "\373e\367\006\356\240[V@\373k\223\322\035lA\371\342\264\347\246\346\331\362\214\351\322\245tGI\225"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp-urlconnection"
    version: "4.9.2"
  }
  digests {
    sha256: "\016\0349\252\211f\217\226%\311 z\315J\207\204\334\206\212\320\325\242\001\035\236\025\340gO;}\021"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "21.1.0"
  }
  digests {
    sha256: "\n\032\356%*\303\245bYt#\223I\313\343 ;\261\350\314\353D\313\332\023\343\330\260;\243\317\355"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.3.0"
  }
  digests {
    sha256: "\224\006jF\004~=Y>\266R8>wg\340c\003\205\354\327Q3\346\214!\022J\251k\215\302"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.10"
  }
  digests {
    sha256: "\330\277\362\311\215#\2263\373U\002)\345\'w\224%\277\265\037\360K\226\254\017\022\323\254\272\v\037B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.0.1"
  }
  digests {
    sha256: "\263Ro\f\256\332\251lRl\304\311\272\224\262)\332\325\257\343\0372\364ol\326h\2406$\375B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "commons-io"
    artifactId: "commons-io"
    version: "2.6"
  }
  digests {
    sha256: "\370w\323\004f\n\302\241B\363\206[\255\374\227\035\354~\327<t|\177\215]/Q9\312se\023"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.8.2"
  }
  digests {
    sha256: "\003\330\355\3759}\202\370\226\320^:$u\342w\205B\357\200\321\'\264\"\371\316v\2232\271\266\371"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-extensions"
    version: "2.2.0"
  }
  digests {
    sha256: "d\214\215\341\321\v\002]RJ.F\254\231O\303\366\277\030h&\300\236\301\246-%\v\361\270w\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.insert-koin"
    artifactId: "koin-bom"
    version: "3.5.6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.insert-koin"
    artifactId: "koin-core"
    version: "3.5.6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.insert-koin"
    artifactId: "koin-core-jvm"
    version: "3.5.6"
  }
  digests {
    sha256: "\202;\2331X\217\371\002\025\301\374\352\343\r\372\024\035%(\343\324\344,(C\352@\3105+\337i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-concurrency"
    version: "2.0.6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-concurrency-jvm"
    version: "2.0.6"
  }
  digests {
    sha256: "\305|\333n\025\330\202F\033d\302\323[\356GJ\325\366\273i\366\367cX\260\003\033\216\227b|\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-strict"
    version: "2.0.6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-strict-jvm"
    version: "2.0.6"
  }
  digests {
    sha256: "p\317\324kE\000\361\236\237\027\270Z\221@\253@/\\\361-Q\002\322\325l\023O\247k\355\244|"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-concurrent-collections"
    version: "2.0.6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-concurrent-collections-jvm"
    version: "2.0.6"
  }
  digests {
    sha256: "z&s\3474\273\351\27079ue\303\351o\001\2235V\266H\244\033\362\347\362\307\210\315\n&\336"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.6.0"
  }
  digests {
    sha256: "\215\200\217\335\346\'\224\237\3651\241\021[.\357^\t>\323\022\2765h\306\311\316\006\210\301\205H\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.2.0"
  }
  digests {
    sha256: "w\224\b\261\2523\f\324\247\255\361\262\350_\322\211\303\016~\335.\216B\204{}\006X\232\367\025\372"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.6.1"
  }
  digests {
    sha256: "\202\306_Q0\252\253\303\252\002\314x\377}u\362a\354\341\236i\270\376\306\340wqr\363\346\311\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.0.1"
  }
  digests {
    sha256: "\354\025\265\324\242\357\360x\210\274\024\231\316.,n\376$\300\355`\314W\260\214\235\304\266\375<!\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-solver"
    version: "2.0.1"
  }
  digests {
    sha256: "\26272\355\2735\021\3317\376\241\377\357\004{\016l\000\033P\301\222\037\r\225\237\303\204\327\006\354j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.8.6"
  }
  digests {
    sha256: "\310\373H9\005M(\v03\370\000\321\365\251}\342\360(\353\213\242\353E\212\322\207\3456\363\362_"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-reflect"
    version: "2.0.21"
  }
  digests {
    sha256: ":\322\374\255\f\t\335\300\222-\353\253DD\326\022\024K{F[u\250\273u\207\342\r\337\257\327\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-codec"
    artifactId: "commons-codec"
    version: "1.10"
  }
  digests {
    sha256: "BA\337\251Nq\035C_)\244`J>-\345\304\252<\026^#\275\006k\346\374\037\3040\225i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-v4"
    version: "1.0.0"
  }
  digests {
    sha256: "x\376\301H_\0178\212GI\002-\325\024\026\205q\'\315%D\256\034?\320\261e\211\005T\200\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.0.0"
  }
  digests {
    sha256: "\262;R{+\254\207\fJtQ\346\230-q2\344\023\350\215\177\'\333\353\037\307d\nr\f\331\356"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.0.0"
  }
  digests {
    sha256: "v\277\373|\357\277x\a\224\330\201p\002\332\321V/>\'\300\251\367F\326$\001\310\355\263\n\356\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.16.0"
  }
  digests {
    sha256: "\211\201\034c\335&jHQ\375\033y\306\374\f\230)\226\354\3445\365\377H?\2700\312l\265<\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.16.0"
  }
  digests {
    sha256: "\225_\207*\364\322\243!\376\242\243F\374G\"%\242\271\225$\254\304g\201I\027\022\343\233j!O"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.16.0"
  }
  digests {
    sha256: "\242\'\366U\234\020J\245\245\310\213\np\351\307\343\333hY\343\vGt\363\202\221!\337\236bst"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.16.0"
  }
  digests {
    sha256: "\305\2369\261\263\033MU\222\'|\331\000\022\024\235t\vV\235C8\365dC\312\310,?\275\243\321"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.7"
  }
  digests {
    sha256: "\016\217\0302&l[\006g\255=;\020\230\346$\344\232\t\aT\223\240\024\247\350\212\360\037\323\n\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.0.1"
  }
  digests {
    sha256: "\022q\304\240\247\347\265\305\236\026w3 \\]R< \b\337v\374X\235\265q\217\032o\355+p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.2.0"
  }
  digests {
    sha256: "\262\262\217k\241s\365\340\304\376=6\242\327\356R9\307\254\277AC\265I\354\3601\243H[5\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.1.0"
  }
  digests {
    sha256: "}\257\303\237\016\2505G3f\254\303F\367\346\177\310D?Ld]\231\234>\230{\312\326\270\214{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.9"
  }
  digests {
    sha256: "At\\[\217B}$C\220\025\270Oe\032\324 q\211\221\206}*\374&,&@\t\270\002\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.9"
  }
  digests {
    sha256: "\a\243 %\246[\b\356~\021\321M\3059\247X\266g\023\360\301\3219\000\250\025\231\316\255\272\364M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "17.2.0"
  }
  digests {
    sha256: "\336\001\036j;ya\336c\217\027/,\266k\243\004\352\251\211f*\236+\017H\337\367t\314\301f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "me.leolin"
    artifactId: "ShortcutBadger"
    version: "1.1.22"
  }
  digests {
    sha256: "\315\026\020\334H\305i\222)P!\207\375\303\265\305C8p\325\252\261Y3!!\261\370\301\332\330\303"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "2.0.21"
  }
  digests {
    sha256: "\210\226\256o\325\2560\217\360P\025`\317\262\342\234\266\363s\2038\237\234\226e\251\344\334\215\177\373\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "2.0.21"
  }
  digests {
    sha256: "r\030\210h\243\234\202T\022RR\243\300.*\365\274x\035\a\216I\203v\340~\235$\362\217\212\372"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp-brotli"
    version: "4.9.2"
  }
  digests {
    sha256: "y\266\274Cd\252\024\245\202q\247\225\321\374\263\234$m\003\335\215\245\236\360\265\347\316\355\347\024\0047"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.brotli"
    artifactId: "dec"
    version: "0.1.2"
  }
  digests {
    sha256: "a\\\f>\376\371\220\327x1\020Du\373\246\241\367\227\023\210i\035K\255\024q\255\204\020\037mR"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.bouncycastle"
    artifactId: "bcutil-jdk15to18"
    version: "1.78.1"
  }
  digests {
    sha256: "\252\371Zmb\264\245\317\037\250Q\244\345\231\252\317\265\322\002\376\364i4\300\202K~\tq\364\224\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.bouncycastle"
    artifactId: "bcprov-jdk15to18"
    version: "1.78.1"
  }
  digests {
    sha256: "\266u\212\nr\355D\337\3331nP\246y\031\314F@\341`\242k\212~\235\230\234\334\263\374\212\177"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.application"
    version: "6.1.4"
  }
  digests {
    sha256: "n\005\356\201\245\3764\343\255\312\333~\003\314\240\217\034D\363\267\225oe\005g\223\223k\341\0336a"
  }
}
library {
  maven_library {
    groupId: "com.android.installreferrer"
    artifactId: "installreferrer"
    version: "2.2"
  }
  digests {
    sha256: "q\022\345\222\304)\224\227\207\301h\305\254br\021D\247;d\220PhEW\375\275\272]\323\266j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "expo.modules.asset"
    artifactId: "expo.modules.asset"
    version: "11.1.5"
  }
  digests {
    sha256: "\3528\266]\016\240\372.E\376\356T?\310Z\233(\343\261\230U\024\317\0057\272\321@\322\t#2"
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.crypto"
    version: "14.1.4"
  }
  digests {
    sha256: "Q)\357\3540 \316\235f\"O\234\303\"Q\t\373\215g\302\002D\266\216\350\314b\374\223\223\373I"
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.font"
    version: "13.3.1"
  }
  digests {
    sha256: "Rt\342\033\037\366\022\331\336\372\001\023\np\035\356Z|$Y\260mzT\264\235\v\204=`\021 "
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.imagepicker"
    version: "16.1.4"
  }
  digests {
    sha256: "cgF\032W\2235u\200\001\257\275\231\372&\323\325\203\357\351JU{\354B\353>\232\341\']L"
  }
}
library {
  maven_library {
    groupId: "com.vanniktech"
    artifactId: "android-image-cropper"
    version: "4.6.0"
  }
  digests {
    sha256: "\024\203\323\261V\372\n\033\313\241\006\036&\035\351x \0053\305\001\323\b\"\236\026U6\204r\336\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.keepawake"
    version: "14.1.4"
  }
  digests {
    sha256: "\330r\325\035\224d\233\232\267\bD\020\206T(\375Z\321\030\017\267\251\267c\233=[\334_&\300\367"
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.lineargradient"
    version: "14.1.4"
  }
  digests {
    sha256: "\371\326Q%wkP\371\215\3668\234\026\n\027\b\243Y\307\376\000Oc=h\315\253\236\000\314\307\335"
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.webbrowser"
    version: "14.1.6"
  }
  digests {
    sha256: " \004\232\205\307\025\201\371g=\016\361ww\177\357\205\343u\321v\254h\200\351\300[}?O\302Y"
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie"
    version: "6.5.2"
  }
  digests {
    sha256: "\2723\354\3001\367\346{\325\253\026\033\320\034n\311.\006\av\032\340v\332&\242\305\277%w\273\f"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "okhttp3-integration"
    version: "4.12.0"
  }
  digests {
    sha256: "\rr.\364\332\227\037=\t\352\a5\035\323\251\377s\017\261{C\213\035MtO\232(\246\315\326 "
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.react"
    artifactId: "hermes-android"
    version: "0.79.2"
  }
  digests {
    sha256: "\326\\\241\223u\227:\260\320\315\005\031\254\2250\372\220\352g\365\327t\215\307&\346\206\2413P\005a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.razorpay"
    artifactId: "checkout"
    version: "1.6.41"
  }
  digests {
    sha256: "\3669s\227\0356\315n\303\246\022\270\304\215\213Mw\332\352U\353<2sm\251\365r\221\317\261\335"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.razorpay"
    artifactId: "standard-core"
    version: "1.6.50"
  }
  digests {
    sha256: "\200\t\366*=q\305\244\030\004\\\207W\341\363\017!5\'y\037<\332\220;\316\251\216(\354\352\006"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-wallet"
    version: "18.1.3"
  }
  digests {
    sha256: "\341\235\037FP\365\034\342 ,\t,\276\027@X\206\veX\317&\310\2767\2472\357\363\256\030d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-identity"
    version: "17.0.0"
  }
  digests {
    sha256: "\211\207\306\303\003\352\252\234\020\304\003\202,\365\256\030\216\341\316a\303\005n\263\276,\244\252\354\310\v_"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-maps"
    version: "17.0.0"
  }
  digests {
    sha256: "\371\344y\274W\377B9Y\306\335\235\b\324c\306w\364@\342\235\220\336yT\030\352\'\332lg\373"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-gif"
    version: "3.6.0"
  }
  digests {
    sha256: "\307\t\275\177\017\316x\002\230ma\002of\313\261: D\357\240\316\351\232\260P\0170\361\212 \327"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-base"
    version: "3.6.0"
  }
  digests {
    sha256: "\215\251\217\036\367\257I\350\210];r\026Z5+\243\341\222V\3463\361\247\251\213\371\352dru\255"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "vito-options"
    version: "3.6.0"
  }
  digests {
    sha256: "VP=Y\311D.\204\032^\226\355\376~\365\t>gn\261\'\267w\334:\333\204\371[!Ct"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-drawable"
    version: "3.6.0"
  }
  digests {
    sha256: ":\276\332\316\327\315{\217\250\3559\355\214\373\367\354\300\336*\v@>\nOcU\276\314q\000\vy"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "vito-renderer"
    version: "3.6.0"
  }
  digests {
    sha256: "\r\243\022\312\264\352\020\0352+\266!\225GS\374\255\374+\217\323\353\235\\\314U\366$\276\016K\330"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "webpsupport"
    version: "3.6.0"
  }
  digests {
    sha256: "4\a\254HN\365[\263\242y\355\233\335\310_$\246\v\267w3\200\a\226q\223\304\004\004\372\217\317"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 52
  library_dep_index: 60
  library_dep_index: 61
  library_dep_index: 35
  library_dep_index: 62
  library_dep_index: 64
  library_dep_index: 87
  library_dep_index: 71
  library_dep_index: 69
  library_dep_index: 78
  library_dep_index: 67
  library_dep_index: 90
  library_dep_index: 79
  library_dep_index: 91
  library_dep_index: 88
  library_dep_index: 89
  library_dep_index: 92
  library_dep_index: 0
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 52
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 10
  library_dep_index: 55
  library_dep_index: 56
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 45
  library_dep_index: 59
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 52
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 20
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 42
  library_dep_index: 35
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 49
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 0
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 8
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 10
}
library_dependencies {
  library_index: 12
  library_dep_index: 0
}
library_dependencies {
  library_index: 13
  library_dep_index: 8
}
library_dependencies {
  library_index: 14
  library_dep_index: 8
  library_dep_index: 15
}
library_dependencies {
  library_index: 16
  library_dep_index: 8
}
library_dependencies {
  library_index: 17
  library_dep_index: 8
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 18
  library_dep_index: 8
}
library_dependencies {
  library_index: 19
  library_dep_index: 8
  library_dep_index: 18
}
library_dependencies {
  library_index: 20
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 0
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 1
  library_dep_index: 24
  library_dep_index: 0
}
library_dependencies {
  library_index: 24
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 22
  library_dep_index: 25
}
library_dependencies {
  library_index: 25
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 0
}
library_dependencies {
  library_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 27
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 28
}
library_dependencies {
  library_index: 28
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 10
  library_dep_index: 29
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 42
  library_dep_index: 46
  library_dep_index: 0
  library_dep_index: 48
}
library_dependencies {
  library_index: 29
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 30
  library_dep_index: 8
  library_dep_index: 20
  library_dep_index: 20
  library_dep_index: 31
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 31
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 32
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 33
  library_dep_index: 8
  library_dep_index: 17
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 34
  library_dep_index: 8
  library_dep_index: 35
}
library_dependencies {
  library_index: 35
  library_dep_index: 8
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 35
  library_dep_index: 0
  library_dep_index: 35
}
library_dependencies {
  library_index: 37
  library_dep_index: 8
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 38
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 39
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 40
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 42
  library_dep_index: 8
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 43
}
library_dependencies {
  library_index: 43
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 42
}
library_dependencies {
  library_index: 44
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 13
}
library_dependencies {
  library_index: 45
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 34
  library_dep_index: 15
}
library_dependencies {
  library_index: 46
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 13
}
library_dependencies {
  library_index: 48
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 10
  library_dep_index: 28
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 28
}
library_dependencies {
  library_index: 49
  library_dep_index: 7
  library_dep_index: 37
  library_dep_index: 40
  library_dep_index: 43
  library_dep_index: 7
}
library_dependencies {
  library_index: 50
  library_dep_index: 0
  library_dep_index: 13
}
library_dependencies {
  library_index: 51
  library_dep_index: 8
  library_dep_index: 13
}
library_dependencies {
  library_index: 52
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 6
}
library_dependencies {
  library_index: 53
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 13
}
library_dependencies {
  library_index: 54
  library_dep_index: 53
  library_dep_index: 16
  library_dep_index: 13
}
library_dependencies {
  library_index: 55
  library_dep_index: 8
}
library_dependencies {
  library_index: 56
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 47
}
library_dependencies {
  library_index: 57
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 58
}
library_dependencies {
  library_index: 58
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 57
  library_dep_index: 57
}
library_dependencies {
  library_index: 59
  library_dep_index: 8
}
library_dependencies {
  library_index: 60
  library_dep_index: 11
}
library_dependencies {
  library_index: 61
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 16
}
library_dependencies {
  library_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
  library_dep_index: 63
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 66
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 81
  library_dep_index: 82
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 85
  library_dep_index: 86
  library_dep_index: 0
}
library_dependencies {
  library_index: 65
  library_dep_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 66
  library_dep_index: 11
  library_dep_index: 0
}
library_dependencies {
  library_index: 67
  library_dep_index: 68
  library_dep_index: 63
}
library_dependencies {
  library_index: 69
  library_dep_index: 66
  library_dep_index: 0
}
library_dependencies {
  library_index: 70
  library_dep_index: 0
}
library_dependencies {
  library_index: 71
  library_dep_index: 66
  library_dep_index: 69
  library_dep_index: 0
}
library_dependencies {
  library_index: 72
  library_dep_index: 66
  library_dep_index: 73
  library_dep_index: 81
  library_dep_index: 82
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 69
  library_dep_index: 71
  library_dep_index: 70
  library_dep_index: 0
}
library_dependencies {
  library_index: 73
  library_dep_index: 63
  library_dep_index: 68
  library_dep_index: 74
  library_dep_index: 66
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 0
  library_dep_index: 77
}
library_dependencies {
  library_index: 75
  library_dep_index: 66
  library_dep_index: 70
  library_dep_index: 76
  library_dep_index: 0
}
library_dependencies {
  library_index: 76
  library_dep_index: 71
  library_dep_index: 0
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
  library_dep_index: 68
  library_dep_index: 74
  library_dep_index: 66
  library_dep_index: 71
  library_dep_index: 0
}
library_dependencies {
  library_index: 78
  library_dep_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 81
  library_dep_index: 73
  library_dep_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 82
  library_dep_index: 66
  library_dep_index: 73
}
library_dependencies {
  library_index: 83
  library_dep_index: 66
  library_dep_index: 73
  library_dep_index: 81
  library_dep_index: 63
}
library_dependencies {
  library_index: 84
  library_dep_index: 66
  library_dep_index: 73
  library_dep_index: 81
}
library_dependencies {
  library_index: 85
  library_dep_index: 73
  library_dep_index: 81
  library_dep_index: 82
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 63
  library_dep_index: 74
  library_dep_index: 66
}
library_dependencies {
  library_index: 86
  library_dep_index: 77
  library_dep_index: 63
  library_dep_index: 74
  library_dep_index: 66
}
library_dependencies {
  library_index: 87
  library_dep_index: 66
  library_dep_index: 73
  library_dep_index: 81
  library_dep_index: 82
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 71
  library_dep_index: 88
  library_dep_index: 0
}
library_dependencies {
  library_index: 88
  library_dep_index: 89
  library_dep_index: 0
}
library_dependencies {
  library_index: 89
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 91
  library_dep_index: 88
  library_dep_index: 3
}
library_dependencies {
  library_index: 93
  library_dep_index: 28
  library_dep_index: 44
  library_dep_index: 94
  library_dep_index: 96
  library_dep_index: 95
  library_dep_index: 27
  library_dep_index: 97
  library_dep_index: 26
}
library_dependencies {
  library_index: 94
  library_dep_index: 95
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 95
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 28
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 96
  library_dep_index: 13
  library_dep_index: 95
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 97
  library_dep_index: 95
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 99
  library_dep_index: 8
}
library_dependencies {
  library_index: 100
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 28
  library_dep_index: 20
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
}
library_dependencies {
  library_index: 102
  library_dep_index: 103
}
library_dependencies {
  library_index: 103
  library_dep_index: 104
  library_dep_index: 108
  library_dep_index: 0
}
library_dependencies {
  library_index: 104
  library_dep_index: 105
}
library_dependencies {
  library_index: 105
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 106
}
library_dependencies {
  library_index: 106
  library_dep_index: 107
}
library_dependencies {
  library_index: 107
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 108
  library_dep_index: 109
}
library_dependencies {
  library_index: 109
  library_dep_index: 3
  library_dep_index: 104
  library_dep_index: 4
}
library_dependencies {
  library_index: 110
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 15
}
library_dependencies {
  library_index: 111
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 47
}
library_dependencies {
  library_index: 112
  library_dep_index: 8
  library_dep_index: 6
  library_dep_index: 113
  library_dep_index: 111
  library_dep_index: 114
  library_dep_index: 11
  library_dep_index: 56
  library_dep_index: 116
  library_dep_index: 12
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 121
  library_dep_index: 122
  library_dep_index: 53
  library_dep_index: 123
}
library_dependencies {
  library_index: 113
  library_dep_index: 8
}
library_dependencies {
  library_index: 114
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 115
}
library_dependencies {
  library_index: 116
  library_dep_index: 11
  library_dep_index: 13
  library_dep_index: 117
}
library_dependencies {
  library_index: 117
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 118
  library_dep_index: 44
  library_dep_index: 119
  library_dep_index: 120
}
library_dependencies {
  library_index: 118
  library_dep_index: 8
}
library_dependencies {
  library_index: 119
  library_dep_index: 8
}
library_dependencies {
  library_index: 120
  library_dep_index: 8
}
library_dependencies {
  library_index: 121
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 47
  library_dep_index: 13
}
library_dependencies {
  library_index: 122
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 13
}
library_dependencies {
  library_index: 123
  library_dep_index: 8
  library_dep_index: 28
  library_dep_index: 121
  library_dep_index: 11
  library_dep_index: 13
}
library_dependencies {
  library_index: 125
  library_dep_index: 0
}
library_dependencies {
  library_index: 127
  library_dep_index: 11
  library_dep_index: 128
  library_dep_index: 117
  library_dep_index: 129
  library_dep_index: 28
}
library_dependencies {
  library_index: 128
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 51
}
library_dependencies {
  library_index: 129
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 117
  library_dep_index: 47
  library_dep_index: 46
  library_dep_index: 111
  library_dep_index: 56
  library_dep_index: 130
  library_dep_index: 16
  library_dep_index: 61
  library_dep_index: 131
  library_dep_index: 55
}
library_dependencies {
  library_index: 130
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 47
}
library_dependencies {
  library_index: 131
  library_dep_index: 8
  library_dep_index: 11
}
library_dependencies {
  library_index: 132
  library_dep_index: 133
  library_dep_index: 134
  library_dep_index: 135
  library_dep_index: 28
  library_dep_index: 54
  library_dep_index: 136
  library_dep_index: 35
}
library_dependencies {
  library_index: 133
  library_dep_index: 8
}
library_dependencies {
  library_index: 136
  library_dep_index: 8
}
library_dependencies {
  library_index: 137
  library_dep_index: 138
  library_dep_index: 142
  library_dep_index: 139
  library_dep_index: 143
  library_dep_index: 146
  library_dep_index: 149
  library_dep_index: 147
  library_dep_index: 150
  library_dep_index: 151
  library_dep_index: 152
  library_dep_index: 153
  library_dep_index: 8
  library_dep_index: 144
  library_dep_index: 148
  library_dep_index: 145
  library_dep_index: 95
  library_dep_index: 27
  library_dep_index: 154
  library_dep_index: 155
  library_dep_index: 26
  library_dep_index: 141
  library_dep_index: 0
}
library_dependencies {
  library_index: 138
  library_dep_index: 25
  library_dep_index: 139
  library_dep_index: 140
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 139
  library_dep_index: 140
  library_dep_index: 8
  library_dep_index: 141
}
library_dependencies {
  library_index: 140
  library_dep_index: 92
}
library_dependencies {
  library_index: 142
  library_dep_index: 138
  library_dep_index: 3
  library_dep_index: 139
  library_dep_index: 140
}
library_dependencies {
  library_index: 143
  library_dep_index: 144
  library_dep_index: 145
  library_dep_index: 148
  library_dep_index: 8
}
library_dependencies {
  library_index: 144
  library_dep_index: 8
}
library_dependencies {
  library_index: 145
  library_dep_index: 144
  library_dep_index: 8
  library_dep_index: 92
  library_dep_index: 146
  library_dep_index: 147
}
library_dependencies {
  library_index: 146
  library_dep_index: 8
}
library_dependencies {
  library_index: 147
  library_dep_index: 8
  library_dep_index: 146
}
library_dependencies {
  library_index: 148
  library_dep_index: 144
  library_dep_index: 145
  library_dep_index: 146
  library_dep_index: 149
  library_dep_index: 8
}
library_dependencies {
  library_index: 149
  library_dep_index: 8
  library_dep_index: 146
}
library_dependencies {
  library_index: 150
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 151
  library_dep_index: 152
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 140
  library_dep_index: 138
  library_dep_index: 142
  library_dep_index: 139
}
library_dependencies {
  library_index: 152
  library_dep_index: 26
  library_dep_index: 140
}
library_dependencies {
  library_index: 153
  library_dep_index: 27
  library_dep_index: 140
}
library_dependencies {
  library_index: 154
  library_dep_index: 27
  library_dep_index: 26
}
library_dependencies {
  library_index: 155
  library_dep_index: 117
  library_dep_index: 27
}
library_dependencies {
  library_index: 157
  library_dep_index: 0
  library_dep_index: 158
}
library_dependencies {
  library_index: 158
  library_dep_index: 0
}
library_dependencies {
  library_index: 159
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 160
  library_dep_index: 162
  library_dep_index: 163
  library_dep_index: 160
  library_dep_index: 161
}
library_dependencies {
  library_index: 160
  library_dep_index: 8
  library_dep_index: 3
  library_dep_index: 161
  library_dep_index: 159
}
library_dependencies {
  library_index: 161
  library_dep_index: 160
  library_dep_index: 159
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 160
  library_dep_index: 159
}
library_dependencies {
  library_index: 162
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 163
}
library_dependencies {
  library_index: 163
  library_dep_index: 8
  library_dep_index: 162
  library_dep_index: 0
  library_dep_index: 162
}
library_dependencies {
  library_index: 164
  library_dep_index: 88
  library_dep_index: 165
  library_dep_index: 3
}
library_dependencies {
  library_index: 166
  library_dep_index: 167
}
library_dependencies {
  library_index: 168
  library_dep_index: 2
  library_dep_index: 169
}
library_dependencies {
  library_index: 170
  library_dep_index: 2
}
library_dependencies {
  library_index: 171
  library_dep_index: 2
}
library_dependencies {
  library_index: 172
  library_dep_index: 2
  library_dep_index: 5
}
library_dependencies {
  library_index: 173
  library_dep_index: 2
  library_dep_index: 49
  library_dep_index: 6
  library_dep_index: 136
  library_dep_index: 174
  library_dep_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 174
  library_dep_index: 157
  library_dep_index: 49
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 136
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 99
  library_dep_index: 0
}
library_dependencies {
  library_index: 175
  library_dep_index: 2
}
library_dependencies {
  library_index: 176
  library_dep_index: 2
}
library_dependencies {
  library_index: 177
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 110
}
library_dependencies {
  library_index: 178
  library_dep_index: 89
  library_dep_index: 6
}
library_dependencies {
  library_index: 179
  library_dep_index: 132
  library_dep_index: 88
  library_dep_index: 8
}
library_dependencies {
  library_index: 180
  library_dep_index: 62
  library_dep_index: 90
  library_dep_index: 8
}
library_dependencies {
  library_index: 181
  library_dep_index: 182
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 26
  library_dep_index: 183
  library_dep_index: 2
}
library_dependencies {
  library_index: 182
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 26
  library_dep_index: 183
  library_dep_index: 34
  library_dep_index: 2
}
library_dependencies {
  library_index: 183
  library_dep_index: 28
  library_dep_index: 95
  library_dep_index: 27
  library_dep_index: 184
  library_dep_index: 185
  library_dep_index: 26
}
library_dependencies {
  library_index: 184
  library_dep_index: 95
  library_dep_index: 27
}
library_dependencies {
  library_index: 185
  library_dep_index: 28
  library_dep_index: 95
  library_dep_index: 27
}
library_dependencies {
  library_index: 186
  library_dep_index: 74
  library_dep_index: 63
  library_dep_index: 66
  library_dep_index: 187
}
library_dependencies {
  library_index: 187
  library_dep_index: 74
  library_dep_index: 188
  library_dep_index: 71
  library_dep_index: 66
  library_dep_index: 77
  library_dep_index: 73
  library_dep_index: 81
  library_dep_index: 82
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 189
  library_dep_index: 0
}
library_dependencies {
  library_index: 188
  library_dep_index: 72
  library_dep_index: 66
  library_dep_index: 73
  library_dep_index: 0
}
library_dependencies {
  library_index: 189
  library_dep_index: 77
  library_dep_index: 72
  library_dep_index: 66
  library_dep_index: 190
  library_dep_index: 188
  library_dep_index: 0
}
library_dependencies {
  library_index: 190
  library_dep_index: 0
}
library_dependencies {
  library_index: 191
  library_dep_index: 63
  library_dep_index: 74
  library_dep_index: 66
  library_dep_index: 77
  library_dep_index: 71
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 186
  dependency_index: 191
  dependency_index: 180
  dependency_index: 93
  dependency_index: 168
  dependency_index: 170
  dependency_index: 171
  dependency_index: 172
  dependency_index: 173
  dependency_index: 175
  dependency_index: 176
  dependency_index: 177
  dependency_index: 2
  dependency_index: 8
  dependency_index: 98
  dependency_index: 99
  dependency_index: 6
  dependency_index: 100
  dependency_index: 101
  dependency_index: 102
  dependency_index: 67
  dependency_index: 88
  dependency_index: 124
  dependency_index: 22
  dependency_index: 21
  dependency_index: 125
  dependency_index: 110
  dependency_index: 111
  dependency_index: 112
  dependency_index: 122
  dependency_index: 10
  dependency_index: 126
  dependency_index: 91
  dependency_index: 89
  dependency_index: 127
  dependency_index: 132
  dependency_index: 36
  dependency_index: 11
  dependency_index: 17
  dependency_index: 33
  dependency_index: 30
  dependency_index: 137
  dependency_index: 156
  dependency_index: 157
  dependency_index: 159
  dependency_index: 161
  dependency_index: 164
  dependency_index: 166
  dependency_index: 178
  dependency_index: 179
  dependency_index: 90
  dependency_index: 181
  dependency_index: 48
  dependency_index: 61
  dependency_index: 40
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://www.jitpack.io"
  }
}
repositories {
  maven_repo {
    url: "https://oss.sonatype.org/content/repositories/snapshots/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://www.jitpack.io"
  }
}
