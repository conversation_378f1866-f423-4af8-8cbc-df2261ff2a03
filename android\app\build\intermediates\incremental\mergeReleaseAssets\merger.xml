<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-updates-interface\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-linking" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-linking\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-json-utils\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-manifests\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-image-manipulator" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-image-manipulator\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-eas-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-eas-client\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-dev-client\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-constants\android\build\intermediates\library_assets\release\packageReleaseAssets\out"><file name="app.config" path="D:\app\StyleApp\node_modules\expo-constants\android\build\intermediates\library_assets\release\packageReleaseAssets\out\app.config"/></source></dataSet><dataSet config=":expo-notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-notifications\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-image-loader" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-image-loader\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-file-system\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-structured-headers" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-structured-headers\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-updates" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\library_assets\release\packageReleaseAssets\out"><file name="expo-root.pem" path="D:\app\StyleApp\node_modules\expo-updates\android\build\intermediates\library_assets\release\packageReleaseAssets\out\expo-root.pem"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-modules-core\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-dev-menu-interface\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\react-native-reanimated\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-razorpay" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\react-native-razorpay\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-fast-image" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\react-native-fast-image\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\expo\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-picker_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\@react-native-picker\picker\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\react-native-screens\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":lottie-react-native" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\lottie-react-native\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-google-signin_google-signin" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\android\app\src\main\assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\android\app\src\release\assets"/></dataSet><dataSet config="assets-createBundleReleaseJsAndAssets" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\android\app\build\generated\assets\createBundleReleaseJsAndAssets"><file name="index.android.bundle" path="D:\app\StyleApp\android\app\build\generated\assets\createBundleReleaseJsAndAssets\index.android.bundle"/></source></dataSet><dataSet config="assets-createReleaseUpdatesResources" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\android\app\build\generated\assets\createReleaseUpdatesResources"><file name="app.manifest" path="D:\app\StyleApp\android\app\build\generated\assets\createReleaseUpdatesResources\app.manifest"/></source></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\app\StyleApp\android\app\build\intermediates\shader_assets\release\compileReleaseShaders\out"/></dataSet></merger>