import React from 'react';
import { View, TouchableOpacity, TextInput, Image, Platform, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './FeedHeader.styles';

const FeedHeader = ({
  currentUserPhotoURL,
  currentUserId,
  searchQueryHeader,
  setSearchQueryHeader,
  setSearchModalVisible,
  cartItems,
  navigation,
  onClearSearch,
  isSearchMode,
  searchResultsCount
}) => {
  const handleProfilePress = () => {
    if (currentUserId) {
      navigation.navigate('MyProfile');
    } else {
      navigation.navigate('Auth');
    }
  };

  const handleCartPress = () => {
    navigation.navigate('Cart');
  };

  const handleSearchPress = () => {
    setSearchModalVisible(true);
  };

  const handleSearchChange = (text) => {
    setSearchQueryHeader(text);
  };

  const handleClearSearch = () => {
    setSearchQueryHeader('');
    if (onClearSearch) {
      onClearSearch();
    }
  };

  return (
    <View style={styles.header}>
      {/* Profile Icon */}
      <TouchableOpacity
        style={styles.headerIconLeft}
        onPress={handleProfilePress}
      >
        {currentUserPhotoURL ? (
          <Image
            source={{ uri: currentUserPhotoURL }}
            style={styles.profileImage}
            key={`profile-${currentUserPhotoURL}`}
            onError={(e) => {
              console.log("[FeedHeader] Profile image load error:", e.nativeEvent.error);
            }}
          />
        ) : (
          <Ionicons name="person-circle-outline" size={32} color="#555" />
        )}
      </TouchableOpacity>

      {/* Search Bar */}
      <TouchableOpacity
        style={styles.searchContainerHeader}
        onPress={handleSearchPress}
        activeOpacity={0.7}
      >
        <Ionicons name="search" size={20} color="#666" style={styles.searchIconHeader} />
        <TextInput
          style={[
            styles.searchInputHeader,
            isSearchMode && { color: '#007AFF', fontWeight: '500' }
          ]}
          placeholder={isSearchMode ? `${searchResultsCount || 0} results` : "Search clothing..."}
          placeholderTextColor={isSearchMode ? "#007AFF" : "#999"}
          value={searchQueryHeader}
          onChangeText={handleSearchChange}
          onFocus={handleSearchPress}
          editable={false}
          pointerEvents="none"
        />
        {searchQueryHeader ? (
          <TouchableOpacity
            style={styles.clearSearchButton}
            onPress={handleClearSearch}
          >
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        ) : null}
      </TouchableOpacity>

      {/* Cart Icon */}
      <TouchableOpacity
        style={styles.headerIconRight}
        onPress={handleCartPress}
      >
        <Ionicons name="bag-outline" size={28} color="#555" />
        {cartItems && cartItems.length > 0 && (
          <View style={styles.cartBadge}>
            <Text style={styles.cartBadgeText}>
              {cartItems.length > 99 ? '99+' : cartItems.length}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default FeedHeader;
