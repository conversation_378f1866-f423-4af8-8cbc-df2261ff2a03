/**
 * Hierarchical Category System for Clothing Items
 * This defines the broad categories and their detailed subcategories
 */

export const categoryHierarchy = {
  // Tops
  'Shirts': [
    'Casual Shirts',
    'Formal Shirts',
    'Polo Shirts',
    'Striped Shirts',
    'Solid Color Shirts',
    'Printed Shirts',
    'Denim Shirts',
    'Flannel Shirts'
  ],
  'T-Shirts': [
    'Basic Tees',
    'Graphic Tees',
    'V-Neck',
    'Crew Neck',
    'Long Sleeve',
    'Tank Tops',
    'Oversized Tees',
    'Fitted Tees'
  ],
  'Blouses': [
    'Casual Blouses',
    'Formal Blouses',
    'Silk Blouses',
    'Chiffon Blouses',
    'Printed Blouses',
    'Solid Blouses'
  ],
  'Sweaters': [
    'Pullover Sweaters',
    'Cardigan Sweaters',
    'Turtleneck Sweaters',
    'V-Neck Sweaters',
    'Crew Neck Sweaters',
    'Oversized Sweaters'
  ],
  'Hoodies': [
    'Pullover Hoodies',
    'Zip-Up Hoodies',
    'Oversized Hoodies',
    'Cropped Hoodies',
    'Graphic Hoodies'
  ],

  // Bottoms
  'Jeans': [
    'Skinny Jeans',
    'Straight Jeans',
    'Bootcut Jeans',
    'Wide Leg Jeans',
    'High Waisted Jeans',
    'Low Rise Jeans',
    'Distressed Jeans',
    'Dark Wash Jeans',
    'Light Wash Jeans'
  ],
  'Pants': [
    'Chinos',
    'Dress Pants',
    'Cargo Pants',
    'Joggers',
    'Wide Leg Pants',
    'Straight Leg Pants',
    'Cropped Pants'
  ],
  'Shorts': [
    'Denim Shorts',
    'Chino Shorts',
    'Athletic Shorts',
    'Cargo Shorts',
    'High Waisted Shorts',
    'Bermuda Shorts'
  ],
  'Skirts': [
    'Mini Skirts',
    'Midi Skirts',
    'Maxi Skirts',
    'A-Line Skirts',
    'Pencil Skirts',
    'Pleated Skirts',
    'Denim Skirts'
  ],
  'Leggings': [
    'Athletic Leggings',
    'Casual Leggings',
    'High Waisted Leggings',
    'Cropped Leggings',
    'Printed Leggings'
  ],

  // Dresses
  'Casual Dresses': [
    'Sundresses',
    'Shirt Dresses',
    'T-Shirt Dresses',
    'Wrap Dresses',
    'A-Line Dresses',
    'Maxi Dresses',
    'Mini Dresses'
  ],
  'Formal Dresses': [
    'Cocktail Dresses',
    'Evening Gowns',
    'Business Dresses',
    'Little Black Dresses',
    'Midi Formal Dresses'
  ],
  'Party Dresses': [
    'Sequin Dresses',
    'Bodycon Dresses',
    'Off-Shoulder Dresses',
    'Backless Dresses',
    'Cut-Out Dresses'
  ],

  // Outerwear
  'Jackets': [
    'Denim Jackets',
    'Leather Jackets',
    'Bomber Jackets',
    'Blazers',
    'Windbreakers',
    'Puffer Jackets'
  ],
  'Coats': [
    'Trench Coats',
    'Wool Coats',
    'Pea Coats',
    'Long Coats',
    'Winter Coats'
  ],
  'Cardigans': [
    'Open Front Cardigans',
    'Button-Up Cardigans',
    'Long Cardigans',
    'Cropped Cardigans',
    'Oversized Cardigans'
  ],

  // Shoes
  'Sneakers': [
    'Running Shoes',
    'Casual Sneakers',
    'High-Top Sneakers',
    'Low-Top Sneakers',
    'Platform Sneakers',
    'Slip-On Sneakers'
  ],
  'Boots': [
    'Ankle Boots',
    'Knee-High Boots',
    'Combat Boots',
    'Chelsea Boots',
    'Hiking Boots',
    'Rain Boots'
  ],
  'Heels': [
    'Stiletto Heels',
    'Block Heels',
    'Wedge Heels',
    'Platform Heels',
    'Kitten Heels'
  ],
  'Flats': [
    'Ballet Flats',
    'Loafers',
    'Oxford Shoes',
    'Slip-On Flats',
    'Pointed Toe Flats'
  ],
  'Sandals': [
    'Flip Flops',
    'Strappy Sandals',
    'Platform Sandals',
    'Slide Sandals',
    'Gladiator Sandals'
  ],

  // Accessories
  'Bags': [
    'Handbags',
    'Backpacks',
    'Crossbody Bags',
    'Tote Bags',
    'Clutches',
    'Shoulder Bags',
    'Messenger Bags'
  ],
  'Jewelry': [
    'Necklaces',
    'Earrings',
    'Bracelets',
    'Rings',
    'Watches',
    'Anklets'
  ],
  'Hats': [
    'Baseball Caps',
    'Beanies',
    'Sun Hats',
    'Fedoras',
    'Bucket Hats'
  ],
  'Scarves': [
    'Silk Scarves',
    'Wool Scarves',
    'Cotton Scarves',
    'Infinity Scarves',
    'Bandanas'
  ],
  'Belts': [
    'Leather Belts',
    'Chain Belts',
    'Fabric Belts',
    'Wide Belts',
    'Skinny Belts'
  ],
  'Sunglasses': [
    'Aviator Sunglasses',
    'Cat Eye Sunglasses',
    'Round Sunglasses',
    'Square Sunglasses',
    'Oversized Sunglasses'
  ]
};

// Get all broad categories
export const getBroadCategories = () => {
  return Object.keys(categoryHierarchy);
};

// Get detailed categories for a specific broad category
export const getDetailedCategories = (broadCategory) => {
  return categoryHierarchy[broadCategory] || [];
};

// Check if a broad category exists
export const isBroadCategory = (category) => {
  return categoryHierarchy.hasOwnProperty(category);
};

// Check if a detailed category exists under a broad category
export const isDetailedCategory = (broadCategory, detailedCategory) => {
  const detailedCategories = categoryHierarchy[broadCategory];
  return detailedCategories && detailedCategories.includes(detailedCategory);
};

// Get broad category for a detailed category (reverse lookup)
export const getBroadCategoryForDetailed = (detailedCategory) => {
  for (const [broadCat, detailedCats] of Object.entries(categoryHierarchy)) {
    if (detailedCats.includes(detailedCategory)) {
      return broadCat;
    }
  }
  return null;
};

// Search categories by query
export const searchCategories = (query) => {
  const results = {
    broad: [],
    detailed: []
  };

  const lowerQuery = query.toLowerCase();

  // Search broad categories
  Object.keys(categoryHierarchy).forEach(broadCat => {
    if (broadCat.toLowerCase().includes(lowerQuery)) {
      results.broad.push(broadCat);
    }
  });

  // Search detailed categories
  Object.entries(categoryHierarchy).forEach(([broadCat, detailedCats]) => {
    detailedCats.forEach(detailedCat => {
      if (detailedCat.toLowerCase().includes(lowerQuery)) {
        results.detailed.push({
          broadCategory: broadCat,
          detailedCategory: detailedCat
        });
      }
    });
  });

  return results;
};

// Validate category selection
export const validateCategorySelection = (broadCategory, detailedCategory) => {
  if (!broadCategory) {
    return { valid: false, error: 'Broad category is required' };
  }

  if (!isBroadCategory(broadCategory)) {
    return { valid: false, error: 'Invalid broad category' };
  }

  if (!detailedCategory) {
    return { valid: false, error: 'Detailed category is required' };
  }

  if (!isDetailedCategory(broadCategory, detailedCategory)) {
    return { valid: false, error: 'Invalid detailed category for the selected broad category' };
  }

  return { valid: true };
};

// Get all categories in a flat structure for migration
export const getAllCategoriesFlat = () => {
  const flatCategories = [];
  Object.entries(categoryHierarchy).forEach(([broadCat, detailedCats]) => {
    flatCategories.push(broadCat);
    flatCategories.push(...detailedCats);
  });
  return [...new Set(flatCategories)]; // Remove duplicates
};
