/**
 * Support System Setup Script
 * 
 * Run this script to initialize your support system with sample data
 * and configure Firebase collections.
 */

import { collection, addDoc, doc, setDoc } from 'firebase/firestore';
import { db } from '../firebase.config';

// Sample FAQ data
const sampleFAQs = [
  {
    category: 'Account',
    questions: [
      {
        question: 'How do I reset my password?',
        answer: 'Go to the login screen and tap "Forgot Password". Enter your email address and we\'ll send you a reset link.'
      },
      {
        question: 'How do I change my profile picture?',
        answer: 'Go to MyProfile → Edit Profile, then tap on your current profile picture to upload a new one.'
      }
    ]
  },
  {
    category: 'Orders',
    questions: [
      {
        question: 'How can I track my order?',
        answer: 'Go to MyProfile → My Orders to see all your orders and their current status. You can tap on any order for detailed tracking information.'
      },
      {
        question: 'Can I cancel my order?',
        answer: 'You can cancel orders that haven\'t been shipped yet. Go to MyProfile → My Orders, select your order, and tap "Cancel Order".'
      }
    ]
  }
];

// Sample support ticket templates
const ticketTemplates = [
  {
    category: 'account',
    title: 'Account Access Issues',
    template: 'Thank you for contacting us about your account access issue. Our team will review your account and get back to you within 24 hours. In the meantime, please ensure you\'re using the correct email address and try resetting your password.'
  },
  {
    category: 'orders',
    title: 'Order Problems',
    template: 'We apologize for any issues with your order. Please provide your order number and a description of the problem. Our team will investigate and resolve this within 2-3 business days.'
  },
  {
    category: 'payments',
    title: 'Payment Issues',
    template: 'We understand payment issues can be frustrating. Please provide details about the payment method used and any error messages you received. Our payment team will investigate and respond within 1-2 business days.'
  },
  {
    category: 'technical',
    title: 'Technical Problems',
    template: 'Thank you for reporting this technical issue. Please include details about your device, app version, and steps to reproduce the problem. Our technical team will investigate and provide a solution.'
  }
];

// Auto-response messages for different scenarios
const autoResponses = {
  offline: "Thank you for contacting StyleApp support! Our agents are currently offline, but we'll respond to your message as soon as possible. Typical response time is 2-4 hours during business hours.",
  busy: "All our support agents are currently busy helping other customers. We'll respond to your message shortly. Thank you for your patience!",
  afterHours: "You've contacted us outside of business hours. Our support team will respond first thing tomorrow morning. For urgent issues, <NAME_EMAIL>"
};

/**
 * Initialize Firebase collections for the support system
 */
export const initializeSupportSystem = async () => {
  try {
    console.log('Initializing support system...');

    // Create support configuration document
    await setDoc(doc(db, 'supportConfig', 'main'), {
      emailNotifications: true,
      autoResponses: autoResponses,
      businessHours: {
        start: '09:00',
        end: '17:00',
        timezone: 'UTC',
        weekdays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
      },
      escalationRules: {
        highPriorityMinutes: 60,
        mediumPriorityHours: 24,
        lowPriorityDays: 3
      },
      ticketCategories: [
        'account',
        'orders', 
        'payments',
        'technical',
        'seller',
        'refund',
        'other'
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Create ticket templates
    for (const template of ticketTemplates) {
      await addDoc(collection(db, 'supportTemplates'), {
        ...template,
        createdAt: new Date(),
        isActive: true
      });
    }

    // Create FAQ entries
    for (const category of sampleFAQs) {
      await addDoc(collection(db, 'supportFAQ'), {
        category: category.category,
        questions: category.questions,
        createdAt: new Date(),
        isActive: true,
        order: 0
      });
    }

    // Create sample support statistics
    await setDoc(doc(db, 'supportStats', 'current'), {
      totalTickets: 0,
      openTickets: 0,
      inProgressTickets: 0,
      resolvedTickets: 0,
      closedTickets: 0,
      averageResponseTime: 0,
      customerSatisfaction: 0,
      lastUpdated: new Date()
    });

    console.log('Support system initialized successfully!');
    return { success: true, message: 'Support system initialized successfully!' };

  } catch (error) {
    console.error('Error initializing support system:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create sample support tickets for testing
 */
export const createSampleTickets = async (userId) => {
  const sampleTickets = [
    {
      userId: userId,
      subject: 'Cannot login to my account',
      message: 'I\'m having trouble logging into my account. I keep getting an error message that says "Invalid credentials" even though I\'m sure my password is correct.',
      category: 'account',
      status: 'open',
      priority: 'medium',
      createdAt: new Date(),
      updatedAt: new Date(),
      responses: []
    },
    {
      userId: userId,
      subject: 'Order not delivered',
      message: 'My order #12345 was supposed to be delivered yesterday but I haven\'t received it yet. The tracking shows it was delivered but I didn\'t get anything.',
      category: 'orders',
      status: 'in_progress',
      priority: 'high',
      createdAt: new Date(Date.now() - ********), // 1 day ago
      updatedAt: new Date(),
      responses: [
        {
          message: 'We\'re investigating your delivery issue. We\'ve contacted the courier and will update you within 24 hours.',
          responderId: 'admin',
          responderName: 'Support Team',
          createdAt: new Date(),
          isAdminResponse: true
        }
      ]
    },
    {
      userId: userId,
      subject: 'Payment failed but money was deducted',
      message: 'I tried to buy an item but the payment failed. However, the money was deducted from my account. Please help me get a refund.',
      category: 'payments',
      status: 'resolved',
      priority: 'high',
      createdAt: new Date(Date.now() - *********), // 2 days ago
      updatedAt: new Date(),
      responses: [
        {
          message: 'We\'ve processed your refund. The amount will be credited back to your account within 3-5 business days.',
          responderId: 'admin',
          responderName: 'Payment Team',
          createdAt: new Date(),
          isAdminResponse: true
        }
      ]
    }
  ];

  try {
    for (const ticket of sampleTickets) {
      await addDoc(collection(db, 'supportTickets'), ticket);
    }
    console.log('Sample tickets created successfully!');
    return { success: true, message: 'Sample tickets created successfully!' };
  } catch (error) {
    console.error('Error creating sample tickets:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Setup Firebase Security Rules for Support Collections
 */
export const getSupportSecurityRules = () => {
  return `
    // Support System Security Rules
    // Add these rules to your firestore.rules file
    
    // Support Tickets
    match /supportTickets/{ticketId} {
      allow read, write: if request.auth != null && 
        (resource.data.userId == request.auth.uid || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    // Support Templates (Admin only)
    match /supportTemplates/{templateId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    // Support FAQ (Read for all, write for admin)
    match /supportFAQ/{faqId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    // Support Configuration (Admin only)
    match /supportConfig/{configId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    // Live Chat Sessions
    match /liveChatSessions/{sessionId} {
      allow read, write: if request.auth != null && 
        (resource.data.userId == request.auth.uid || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    // Support Statistics (Admin read, system write)
    match /supportStats/{statId} {
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
      allow write: if request.auth != null;
    }
  `;
};

/**
 * Instructions for setting up the support system
 */
export const getSetupInstructions = () => {
  return `
    StyleApp Support System Setup Instructions
    ==========================================
    
    1. Initialize the support system:
       - Call initializeSupportSystem() once to set up collections
       - This creates default configuration and templates
    
    2. Configure Firebase Security Rules:
       - Add the provided security rules to your firestore.rules file
       - Deploy the rules: firebase deploy --only firestore:rules
    
    3. Set up admin users:
       - Add isAdmin: true field to admin user documents in Firestore
       - Only admin users can access the AdminSupportDashboard
    
    4. Configure email notifications (optional):
       - Set up your email service (e.g., SendGrid, Mailgun)
       - Update email configuration in supportUtils.js
    
    5. Test the system:
       - Create sample tickets using createSampleTickets()
       - Test all support flows (FAQ, Live Chat, Tickets)
    
    6. Customize:
       - Update FAQ content in FAQScreen.js
       - Modify support categories in SupportScreen.js
       - Customize auto-responses in supportUtils.js
    
    Navigation Access Points:
    - MyProfile → Help & Support
    - Settings → Help & Support section
    - Settings → Admin Support Dashboard (admin only)
    
    For questions or issues, check the SUPPORT_SYSTEM_GUIDE.md file.
  `;
};

export default {
  initializeSupportSystem,
  createSampleTickets,
  getSupportSecurityRules,
  getSetupInstructions
};
