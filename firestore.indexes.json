{"indexes": [{"collectionGroup": "clothingItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "clothingItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "likeCount", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "clothingItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uploaderId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "clothingItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "commentCount", "order": "DESCENDING"}]}, {"collectionGroup": "clothingItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "clothingItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "clothingItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "likeCount", "order": "DESCENDING"}]}, {"collectionGroup": "clothingItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "saveCount", "order": "DESCENDING"}]}, {"collectionGroup": "clothingItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "swipeViewCount", "order": "DESCENDING"}]}, {"collectionGroup": "clothingItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "viewCount", "order": "DESCENDING"}]}, {"collectionGroup": "comments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "likeCount", "order": "DESCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "threads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "arrayConfig": "CONTAINS"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "supportChats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "supportChats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "lastMessageAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatMessages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "chatId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "customCategories", "fieldPath": "createdAt", "ttl": false, "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION_GROUP"}]}]}