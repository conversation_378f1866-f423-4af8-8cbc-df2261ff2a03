import React from 'react';
import { Text, StyleSheet } from 'react-native';
import theme from '../utils/theme';
import { scaleFontSize } from '../utils/responsiveUtils';
import { createTextStyle } from '../utils/platformUtils';

/**
 * ResponsiveText component that scales text size based on screen dimensions
 * and applies platform-specific optimizations
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Text content
 * @param {string} props.variant - Text variant (h1, h2, h3, body, caption, etc.)
 * @param {string} props.color - Text color
 * @param {string} props.align - Text alignment
 * @param {boolean} props.bold - Whether text should be bold
 * @param {boolean} props.italic - Whether text should be italic
 * @param {boolean} props.underline - Whether text should be underlined
 * @param {Object} props.style - Additional styles for the text
 * @param {Object} props.rest - Additional props for the Text component
 * @returns {React.ReactElement} - ResponsiveText component
 */
const ResponsiveText = ({
  children,
  variant = 'body',
  color,
  align,
  bold = false,
  italic = false,
  underline = false,
  style,
  ...rest
}) => {
  // Get the base style for the variant
  const variantStyle = styles[variant] || styles.body;
  
  // Apply text transformations
  const textStyle = [
    variantStyle,
    color && { color },
    align && { textAlign: align },
    bold && { fontWeight: 'bold' },
    italic && { fontStyle: 'italic' },
    underline && { textDecorationLine: 'underline' },
    style,
  ];
  
  return (
    <Text style={textStyle} {...rest}>
      {children}
    </Text>
  );
};

// Text variant styles
const styles = StyleSheet.create({
  h1: createTextStyle({
    fontSize: scaleFontSize(28),
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    lineHeight: scaleFontSize(34),
  }),
  h2: createTextStyle({
    fontSize: scaleFontSize(24),
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    lineHeight: scaleFontSize(30),
  }),
  h3: createTextStyle({
    fontSize: scaleFontSize(20),
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
    lineHeight: scaleFontSize(26),
  }),
  h4: createTextStyle({
    fontSize: scaleFontSize(18),
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
    lineHeight: scaleFontSize(24),
  }),
  body: createTextStyle({
    fontSize: scaleFontSize(16),
    color: theme.colors.text,
    lineHeight: scaleFontSize(22),
  }),
  bodySmall: createTextStyle({
    fontSize: scaleFontSize(14),
    color: theme.colors.text,
    lineHeight: scaleFontSize(20),
  }),
  caption: createTextStyle({
    fontSize: scaleFontSize(12),
    color: theme.colors.textLight,
    lineHeight: scaleFontSize(16),
  }),
  button: createTextStyle({
    fontSize: scaleFontSize(16),
    fontWeight: '600',
    color: theme.colors.white,
    textAlign: 'center',
  }),
  label: createTextStyle({
    fontSize: scaleFontSize(14),
    fontWeight: '500',
    color: theme.colors.textLight,
    marginBottom: theme.spacing.xs,
  }),
  error: createTextStyle({
    fontSize: scaleFontSize(12),
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
  }),
});

export default ResponsiveText;
