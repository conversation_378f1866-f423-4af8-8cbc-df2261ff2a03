import { db } from '../firebase.config';
import { doc, getDoc } from 'firebase/firestore';

/**
 * Get admin settings from Firestore
 * @returns {Promise<Object>} Admin settings object
 */
export const getAdminSettings = async () => {
  try {
    const settingsDoc = await getDoc(doc(db, 'adminSettings', 'appConfig'));

    if (settingsDoc.exists()) {
      return settingsDoc.data();
    } else {
      // Return default settings if none exist
      return getDefaultSettings();
    }
  } catch (error) {
    // Handle permission errors gracefully without logging as errors
    if (error.code === 'permission-denied') {
      console.log('Permission denied for admin settings, using default settings');
    } else {
      console.error('Error fetching admin settings:', error);
    }
    // Return default settings on any error
    return getDefaultSettings();
  }
};

/**
 * Get default admin settings
 * @returns {Object} Default settings object
 */
export const getDefaultSettings = () => ({
  // App Configuration
  appName: 'SwipeSense',
  appVersion: '1.0.0',
  maintenanceMode: false,
  allowNewRegistrations: true,
  requireEmailVerification: true,

  // Content Moderation
  autoModerationEnabled: true,
  requireListingApproval: false,
  maxListingsPerUser: 50,

  // Support Settings
  supportEmail: '<EMAIL>',
  autoResponseEnabled: true,
  maxSupportTicketsPerUser: 10,

  // Security Settings
  maxLoginAttempts: 5,
  sessionTimeout: 24, // hours
  twoFactorRequired: false,

  // Notification Settings
  emailNotificationsEnabled: true,
  pushNotificationsEnabled: true,
  adminNotificationsEnabled: true
});

/**
 * Check if maintenance mode is enabled
 * @returns {Promise<boolean>} True if maintenance mode is on
 */
export const isMaintenanceModeEnabled = async () => {
  try {
    const settings = await getAdminSettings();
    return settings.maintenanceMode || false;
  } catch (error) {
    console.error('Error checking maintenance mode:', error);
    return false;
  }
};

/**
 * Check if new registrations are allowed
 * @returns {Promise<boolean>} True if registrations are allowed
 */
export const areNewRegistrationsAllowed = async () => {
  try {
    const settings = await getAdminSettings();
    return settings.allowNewRegistrations !== false; // Default to true
  } catch (error) {
    console.error('Error checking registration settings:', error);
    return true; // Default to allowing registrations
  }
};

/**
 * Check if email verification is required
 * @returns {Promise<boolean>} True if email verification is required
 */
export const isEmailVerificationRequired = async () => {
  try {
    const settings = await getAdminSettings();
    return settings.requireEmailVerification !== false; // Default to true
  } catch (error) {
    console.error('Error checking email verification settings:', error);
    return true; // Default to requiring verification
  }
};

/**
 * Check if listing approval is required
 * @returns {Promise<boolean>} True if approval is required
 */
export const isListingApprovalRequired = async () => {
  try {
    const settings = await getAdminSettings();
    return settings.requireListingApproval || false;
  } catch (error) {
    console.error('Error checking listing approval settings:', error);
    return false;
  }
};

/**
 * Get maximum listings per user
 * @returns {Promise<number>} Maximum number of listings per user
 */
export const getMaxListingsPerUser = async () => {
  try {
    const settings = await getAdminSettings();
    return settings.maxListingsPerUser || 50;
  } catch (error) {
    console.error('Error getting max listings setting:', error);
    return 50;
  }
};

/**
 * Get maximum support tickets per user
 * @returns {Promise<number>} Maximum number of support tickets per user
 */
export const getMaxSupportTicketsPerUser = async () => {
  try {
    const settings = await getAdminSettings();
    return settings.maxSupportTicketsPerUser || 10;
  } catch (error) {
    console.error('Error getting max support tickets setting:', error);
    return 10;
  }
};

/**
 * Check if auto-moderation is enabled
 * @returns {Promise<boolean>} True if auto-moderation is enabled
 */
export const isAutoModerationEnabled = async () => {
  try {
    const settings = await getAdminSettings();
    return settings.autoModerationEnabled !== false; // Default to true
  } catch (error) {
    console.error('Error checking auto-moderation settings:', error);
    return true;
  }
};

/**
 * Get support email address
 * @returns {Promise<string>} Support email address
 */
export const getSupportEmail = async () => {
  try {
    const settings = await getAdminSettings();
    return settings.supportEmail || '<EMAIL>';
  } catch (error) {
    console.error('Error getting support email:', error);
    return '<EMAIL>';
  }
};

/**
 * Check if auto-response is enabled for support
 * @returns {Promise<boolean>} True if auto-response is enabled
 */
export const isAutoResponseEnabled = async () => {
  try {
    const settings = await getAdminSettings();
    return settings.autoResponseEnabled !== false; // Default to true
  } catch (error) {
    console.error('Error checking auto-response settings:', error);
    return true;
  }
};

/**
 * Get maximum login attempts
 * @returns {Promise<number>} Maximum login attempts
 */
export const getMaxLoginAttempts = async () => {
  try {
    const settings = await getAdminSettings();
    return settings.maxLoginAttempts || 5;
  } catch (error) {
    console.error('Error getting max login attempts:', error);
    return 5;
  }
};

/**
 * Get session timeout in hours
 * @returns {Promise<number>} Session timeout in hours
 */
export const getSessionTimeout = async () => {
  try {
    const settings = await getAdminSettings();
    return settings.sessionTimeout || 24;
  } catch (error) {
    console.error('Error getting session timeout:', error);
    return 24;
  }
};

/**
 * Check if notifications are enabled
 * @param {string} type - Type of notification ('email', 'push', 'admin')
 * @returns {Promise<boolean>} True if notifications are enabled
 */
export const areNotificationsEnabled = async (type) => {
  try {
    const settings = await getAdminSettings();

    switch (type) {
      case 'email':
        return settings.emailNotificationsEnabled !== false;
      case 'push':
        return settings.pushNotificationsEnabled !== false;
      case 'admin':
        return settings.adminNotificationsEnabled !== false;
      default:
        return true;
    }
  } catch (error) {
    console.error('Error checking notification settings:', error);
    return true;
  }
};

/**
 * Validate user action against admin settings
 * @param {string} action - Action to validate
 * @param {Object} context - Context data for validation
 * @returns {Promise<Object>} Validation result
 */
export const validateUserAction = async (action, context = {}) => {
  try {
    const settings = await getAdminSettings();

    switch (action) {
      case 'register':
        if (!settings.allowNewRegistrations) {
          return {
            allowed: false,
            message: 'New registrations are currently disabled. Please try again later.'
          };
        }
        break;

      case 'create_listing':
        if (context.userListingCount >= (settings.maxListingsPerUser || 50)) {
          return {
            allowed: false,
            message: `You have reached the maximum limit of ${settings.maxListingsPerUser || 50} listings.`
          };
        }
        break;

      case 'create_support_ticket':
        if (context.userTicketCount >= (settings.maxSupportTicketsPerUser || 10)) {
          return {
            allowed: false,
            message: `You have reached the maximum limit of ${settings.maxSupportTicketsPerUser || 10} support tickets.`
          };
        }
        break;

      default:
        break;
    }

    return { allowed: true };
  } catch (error) {
    console.error('Error validating user action:', error);
    return { allowed: true }; // Default to allowing action if validation fails
  }
};

export default {
  getAdminSettings,
  getDefaultSettings,
  isMaintenanceModeEnabled,
  areNewRegistrationsAllowed,
  isEmailVerificationRequired,
  isListingApprovalRequired,
  getMaxListingsPerUser,
  getMaxSupportTicketsPerUser,
  isAutoModerationEnabled,
  getSupportEmail,
  isAutoResponseEnabled,
  getMaxLoginAttempts,
  getSessionTimeout,
  areNotificationsEnabled,
  validateUserAction
};
