import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  TextInput,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Predefined color palette
const COLOR_PALETTE = [
  { name: 'Red', value: '#FF0000', textColor: '#FFFFFF' },
  { name: 'Blue', value: '#0000FF', textColor: '#FFFFFF' },
  { name: 'Green', value: '#008000', textColor: '#FFFFFF' },
  { name: 'Yellow', value: '#FFFF00', textColor: '#000000' },
  { name: 'Black', value: '#000000', textColor: '#FFFFFF' },
  { name: 'White', value: '#FFFFFF', textColor: '#000000' },
  { name: 'Pink', value: '#FFC0CB', textColor: '#000000' },
  { name: '<PERSON>', value: '#800080', textColor: '#FFFFFF' },
  { name: 'Orange', value: '#FFA500', textColor: '#000000' },
  { name: '<PERSON>', value: '#A52A2A', textColor: '#FFFFFF' },
  { name: 'Gray', value: '#808080', textColor: '#FFFFFF' },
  { name: 'Navy', value: '#000080', textColor: '#FFFFFF' },
  { name: 'Beige', value: '#F5F5DC', textColor: '#000000' },
  { name: 'Khaki', value: '#F0E68C', textColor: '#000000' },
  { name: 'Maroon', value: '#800000', textColor: '#FFFFFF' },
  { name: 'Teal', value: '#008080', textColor: '#FFFFFF' },
  { name: 'Olive', value: '#808000', textColor: '#FFFFFF' },
  { name: 'Silver', value: '#C0C0C0', textColor: '#000000' },
  { name: 'Gold', value: '#FFD700', textColor: '#000000' },
  { name: 'Coral', value: '#FF7F50', textColor: '#000000' },
  { name: 'Turquoise', value: '#40E0D0', textColor: '#000000' },
  { name: 'Lavender', value: '#E6E6FA', textColor: '#000000' },
  { name: 'Mint', value: '#98FB98', textColor: '#000000' },
  { name: 'Cream', value: '#FFFDD0', textColor: '#000000' },
];

const ColorSelector = ({
  selectedColors = [],
  onColorsChange,
  disabled = false,
  style
}) => {
  const [showModal, setShowModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredColors, setFilteredColors] = useState(COLOR_PALETTE);

  // Filter colors based on search
  React.useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = COLOR_PALETTE.filter(color =>
        color.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredColors(filtered);
    } else {
      setFilteredColors(COLOR_PALETTE);
    }
  }, [searchQuery]);

  const handleColorToggle = (colorName) => {
    const newSelected = selectedColors.includes(colorName)
      ? selectedColors.filter(color => color !== colorName)
      : [...selectedColors, colorName];

    onColorsChange(newSelected);
  };

  const openModal = () => {
    if (!disabled) {
      setSearchQuery('');
      setShowModal(true);
    }
  };

  const getColorByName = (colorName) => {
    return COLOR_PALETTE.find(color => color.name === colorName);
  };

  const renderColorItem = ({ item: color }) => {
    const isSelected = selectedColors.includes(color.name);

    return (
      <TouchableOpacity
        style={[
          styles.colorItem,
          isSelected && styles.selectedColorItem
        ]}
        onPress={() => handleColorToggle(color.name)}
      >
        <View style={styles.colorItemContent}>
          <View
            style={[
              styles.colorCircle,
              { backgroundColor: color.value },
              color.name === 'White' && styles.whiteColorBorder
            ]}
          />
          <Text style={[
            styles.colorItemText,
            isSelected && styles.selectedColorItemText
          ]}>
            {color.name}
          </Text>
        </View>
        <Ionicons
          name={isSelected ? "checkbox" : "square-outline"}
          size={20}
          color={isSelected ? "#FF6B6B" : "#666"}
        />
      </TouchableOpacity>
    );
  };

  const renderSelectedColors = () => (
    <View style={styles.selectedColorsContainer}>
      {selectedColors.length === 0 ? (
        <Text style={styles.emptyText}>No colors selected</Text>
      ) : (
        <View style={styles.colorsWrapper}>
          {selectedColors.map((colorName, index) => {
            const colorData = getColorByName(colorName);
            return (
              <View key={index} style={styles.colorTag}>
                <View
                  style={[
                    styles.colorTagCircle,
                    { backgroundColor: colorData?.value || '#ccc' },
                    colorName === 'White' && styles.whiteColorBorder
                  ]}
                />
                <Text style={styles.colorTagText}>{colorName}</Text>
                <TouchableOpacity
                  onPress={() => handleColorToggle(colorName)}
                  style={styles.colorTagRemoveButton}
                  disabled={disabled}
                >
                  <Ionicons name="close" size={14} color="#FF6B6B" />
                </TouchableOpacity>
              </View>
            );
          })}
        </View>
      )}
    </View>
  );

  return (
    <View style={[styles.container, style]}>
      <View style={styles.selectorContainer}>
        <Text style={styles.label}>Colors</Text>
        <TouchableOpacity
          style={[
            styles.selector,
            disabled && styles.selectorDisabled
          ]}
          onPress={openModal}
          disabled={disabled}
        >
          <Text style={styles.selectorText}>
            {selectedColors.length > 0
              ? `${selectedColors.length} color${selectedColors.length > 1 ? 's' : ''} selected`
              : 'Select colors'}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#666" />
        </TouchableOpacity>

        {renderSelectedColors()}
      </View>

      {/* Color Selection Modal */}
      <Modal
        visible={showModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Colors</Text>
              <TouchableOpacity
                onPress={() => setShowModal(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search colors..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
              />
            </View>

            <FlatList
              data={filteredColors}
              keyExtractor={(item) => item.name}
              renderItem={renderColorItem}
              style={styles.colorsList}
              numColumns={2}
              columnWrapperStyle={styles.colorRow}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  selectorContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
    marginBottom: 10,
  },
  selectorDisabled: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
  },
  selectorText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectedColorsContainer: {
    minHeight: 40,
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  colorsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 10,
  },
  colorTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
    margin: 3,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  colorTagCircle: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 6,
  },
  whiteColorBorder: {
    borderWidth: 1,
    borderColor: '#ccc',
  },
  colorTagText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    marginRight: 5,
  },
  colorTagRemoveButton: {
    padding: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginVertical: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  colorsList: {
    maxHeight: 400,
    paddingHorizontal: 10,
  },
  colorRow: {
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },
  colorItem: {
    flex: 0.48,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginVertical: 4,
    borderWidth: 1,
    borderColor: '#f0f0f0',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  selectedColorItem: {
    backgroundColor: '#fff5f5',
    borderColor: '#FF6B6B',
  },
  colorItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  colorCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 10,
  },
  colorItemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectedColorItemText: {
    color: '#FF6B6B',
    fontWeight: '500',
  },
});

export default ColorSelector;
