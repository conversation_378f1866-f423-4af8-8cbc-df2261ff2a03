import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  Switch,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
  doc,
  getDoc,
  setDoc,
  updateDoc
} from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import { checkAdminStatus } from '../utils/adminUtils';

const AdminSettings = ({ navigation }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState({
    // App Configuration
    appName: 'SwipeSense',
    appVersion: '1.0.0',
    maintenanceMode: false,
    allowNewRegistrations: true,
    requireEmailVerification: true,

    // Content Moderation
    autoModerationEnabled: true,
    requireListingApproval: false,
    maxListingsPerUser: 50,

    // Support Settings
    supportEmail: '<EMAIL>',
    autoResponseEnabled: true,
    maxSupportTicketsPerUser: 10,

    // Security Settings
    maxLoginAttempts: 5,
    sessionTimeout: 24, // hours
    twoFactorRequired: false,

    // Notification Settings
    emailNotificationsEnabled: true,
    pushNotificationsEnabled: true,
    adminNotificationsEnabled: true
  });

  useEffect(() => {
    checkAdminAccess();
  }, []);

  useEffect(() => {
    if (isAdmin) {
      loadSettings();
    }
  }, [isAdmin]);

  const checkAdminAccess = async () => {
    try {
      const adminStatus = await checkAdminStatus(auth.currentUser.uid);
      setIsAdmin(adminStatus);

      if (!adminStatus) {
        Alert.alert(
          'Access Denied',
          'You do not have administrator privileges.',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const loadSettings = async () => {
    try {
      const settingsDoc = await getDoc(doc(db, 'adminSettings', 'appConfig'));

      if (settingsDoc.exists()) {
        setSettings(prevSettings => ({
          ...prevSettings,
          ...settingsDoc.data()
        }));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      Alert.alert('Error', 'Failed to load settings');
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);

      await setDoc(doc(db, 'adminSettings', 'appConfig'), {
        ...settings,
        lastUpdated: new Date(),
        updatedBy: auth.currentUser.uid
      });

      Alert.alert('Success', 'Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (key, value) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      [key]: value
    }));
  };

  const renderSettingItem = (title, key, type = 'switch', options = {}) => {
    const value = settings[key];

    return (
      <View style={styles.settingItem}>
        <View style={styles.settingInfo}>
          <Text style={styles.settingTitle}>{title}</Text>
          {options.description && (
            <Text style={styles.settingDescription}>{options.description}</Text>
          )}
        </View>

        {type === 'switch' && (
          <Switch
            value={value}
            onValueChange={(newValue) => updateSetting(key, newValue)}
            trackColor={{ false: '#767577', true: '#FF6B6B' }}
            thumbColor={value ? '#fff' : '#f4f3f4'}
          />
        )}

        {type === 'text' && (
          <TextInput
            style={styles.textInput}
            value={value?.toString() || ''}
            onChangeText={(text) => updateSetting(key, text)}
            placeholder={options.placeholder || ''}
            keyboardType={options.keyboardType || 'default'}
          />
        )}

        {type === 'number' && (
          <TextInput
            style={styles.numberInput}
            value={value?.toString() || ''}
            onChangeText={(text) => updateSetting(key, parseInt(text) || 0)}
            placeholder={options.placeholder || '0'}
            keyboardType="numeric"
          />
        )}
      </View>
    );
  };

  const renderSection = (title, children) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>
        {children}
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
          <Text style={styles.loadingText}>Loading settings...</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  if (!isAdmin) {
    return (
      <SafeAreaWrapper>
        <View style={styles.accessDenied}>
          <Ionicons name="lock-closed-outline" size={80} color="#ccc" />
          <Text style={styles.accessDeniedText}>Access Denied</Text>
          <Text style={styles.accessDeniedSubtext}>
            You don't have administrator privileges.
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Admin Settings</Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveSettings}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#FF6B6B" />
          ) : (
            <Ionicons name="save" size={24} color="#FF6B6B" />
          )}
        </TouchableOpacity>
      </SafeAreaHeader>

      <View style={styles.container}>

        <ScrollView style={styles.content}>
          {/* App Configuration */}
          {renderSection('App Configuration', [
            renderSettingItem(
              'App Name',
              'appName',
              'text',
              { placeholder: 'Enter app name' }
            ),
            renderSettingItem(
              'Maintenance Mode',
              'maintenanceMode',
              'switch',
              { description: 'Temporarily disable app for maintenance' }
            ),
            renderSettingItem(
              'Allow New Registrations',
              'allowNewRegistrations',
              'switch',
              { description: 'Allow new users to register' }
            ),
            renderSettingItem(
              'Require Email Verification',
              'requireEmailVerification',
              'switch',
              { description: 'Users must verify email before using app' }
            )
          ])}

          {/* Content Moderation */}
          {renderSection('Content Moderation', [
            renderSettingItem(
              'Auto Moderation',
              'autoModerationEnabled',
              'switch',
              { description: 'Automatically moderate content using AI' }
            ),
            renderSettingItem(
              'Require Listing Approval',
              'requireListingApproval',
              'switch',
              { description: 'All listings need admin approval' }
            ),
            renderSettingItem(
              'Max Listings Per User',
              'maxListingsPerUser',
              'number',
              { placeholder: '50' }
            )
          ])}

          {/* Support Settings */}
          {renderSection('Support Settings', [
            renderSettingItem(
              'Support Email',
              'supportEmail',
              'text',
              { placeholder: '<EMAIL>', keyboardType: 'email-address' }
            ),
            renderSettingItem(
              'Auto Response',
              'autoResponseEnabled',
              'switch',
              { description: 'Send automatic responses to support tickets' }
            ),
            renderSettingItem(
              'Max Support Tickets Per User',
              'maxSupportTicketsPerUser',
              'number',
              { placeholder: '10' }
            )
          ])}

          {/* Security Settings */}
          {renderSection('Security Settings', [
            renderSettingItem(
              'Max Login Attempts',
              'maxLoginAttempts',
              'number',
              { placeholder: '5' }
            ),
            renderSettingItem(
              'Session Timeout (hours)',
              'sessionTimeout',
              'number',
              { placeholder: '24' }
            ),
            renderSettingItem(
              'Two-Factor Authentication Required',
              'twoFactorRequired',
              'switch',
              { description: 'Require 2FA for all admin accounts' }
            )
          ])}

          {/* Notification Settings */}
          {renderSection('Notification Settings', [
            renderSettingItem(
              'Email Notifications',
              'emailNotificationsEnabled',
              'switch',
              { description: 'Send email notifications to users' }
            ),
            renderSettingItem(
              'Push Notifications',
              'pushNotificationsEnabled',
              'switch',
              { description: 'Send push notifications to mobile devices' }
            ),
            renderSettingItem(
              'Admin Notifications',
              'adminNotificationsEnabled',
              'switch',
              { description: 'Send notifications to admin users' }
            )
          ])}

          {/* Save Button */}
          <TouchableOpacity
            style={styles.saveButtonLarge}
            onPress={saveSettings}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text style={styles.saveButtonText}>Save All Settings</Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  sectionContent: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 12,
    color: '#666',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    minWidth: 120,
    textAlign: 'right',
  },
  numberInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    minWidth: 80,
    textAlign: 'center',
  },
  saveButtonLarge: {
    backgroundColor: '#FF6B6B',
    margin: 20,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 40,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  accessDenied: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  accessDeniedText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 20,
  },
  accessDeniedSubtext: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 10,
  },
});

export default AdminSettings;
