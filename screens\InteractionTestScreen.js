import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  SafeAreaView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import { collection, getDocs, limit, query } from 'firebase/firestore';
import { toggleLike, saveToCollection, addToCart, isItemInCart, removeFromCart } from '../utils/itemInteractions';

const InteractionTestScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState([]);
  const [testResults, setTestResults] = useState([]);
  const [collections, setCollections] = useState([]);

  useEffect(() => {
    fetchItems();
    fetchCollections();
  }, []);

  const fetchItems = async () => {
    setLoading(true);
    try {
      const itemsQuery = query(collection(db, 'clothingItems'), limit(5));
      const snapshot = await getDocs(itemsQuery);
      const itemsList = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setItems(itemsList);
    } catch (error) {
      console.error('Error fetching items:', error);
      Alert.alert('Error', 'Failed to fetch items');
    } finally {
      setLoading(false);
    }
  };

  const fetchCollections = async () => {
    if (!auth.currentUser) return;

    try {
      const collectionsRef = collection(db, 'users', auth.currentUser.uid, 'collections');
      const snapshot = await getDocs(collectionsRef);
      const collectionsList = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Add a default collection if none exists
      if (collectionsList.length === 0) {
        collectionsList.push({
          id: 'default',
          name: 'Default Collection',
          isDefault: true
        });
      }

      setCollections(collectionsList);
    } catch (error) {
      console.error('Error fetching collections:', error);
    }
  };

  const addTestResult = (test, status, message) => {
    setTestResults(prev => [
      { test, status, message, timestamp: new Date().toISOString() },
      ...prev
    ]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testLike = async (item) => {
    try {
      addTestResult('Like Test', 'Running', `Testing like for item ${item.id}`);
      const result = await toggleLike(item.id);

      if (result.success) {
        addTestResult(
          'Like Test',
          'Success',
          `Item ${item.id} ${result.isLiked ? 'liked' : 'unliked'} successfully`
        );
      } else {
        addTestResult('Like Test', 'Error', `Failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Error in like test:', error);
      addTestResult('Like Test', 'Error', `Exception: ${error.message}`);
    }
  };

  const testSave = async (item) => {
    if (collections.length === 0) {
      addTestResult('Save Test', 'Error', 'No collections available');
      return;
    }

    try {
      const collection = collections[0];
      addTestResult('Save Test', 'Running', `Testing save for item ${item.id} to collection ${collection.name}`);

      const result = await saveToCollection(item.id, collection.id, {
        title: item.title,
        imageUrl: item.imageUrl,
        brand: item.brand,
        category: item.category
      });

      if (result.success) {
        addTestResult('Save Test', 'Success', `Item ${item.id} saved to collection ${collection.name}`);
      } else {
        addTestResult('Save Test', 'Error', `Failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Error in save test:', error);
      addTestResult('Save Test', 'Error', `Exception: ${error.message}`);
    }
  };

  const testCart = async (item) => {
    try {
      addTestResult('Cart Test', 'Running', `Testing cart for item ${item.id}`);

      // Check if item is already in cart
      const inCart = await isItemInCart(item.id);

      if (inCart) {
        addTestResult('Cart Test', 'Info', `Item ${item.id} is already in cart`);
        return;
      }

      const result = await addToCart(item.id, {
        title: item.title,
        imageUrl: item.imageUrl,
        brand: item.brand,
        category: item.category,
        size: item.size,
        price: item.price || 0
      });

      if (result.success) {
        addTestResult('Cart Test', 'Success', `Item ${item.id} added to cart`);
      } else {
        addTestResult('Cart Test', 'Error', `Failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Error in cart test:', error);
      addTestResult('Cart Test', 'Error', `Exception: ${error.message}`);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Success':
        return <Ionicons name="checkmark-circle" size={20} color="green" />;
      case 'Error':
        return <Ionicons name="close-circle" size={20} color="red" />;
      case 'Running':
        return <ActivityIndicator size="small" color="#6200ee" />;
      case 'Info':
        return <Ionicons name="information-circle" size={20} color="#2196F3" />;
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Interaction Test</Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={fetchItems}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Refresh Items</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.clearButton}
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6200ee" />
          <Text style={styles.loadingText}>Loading items...</Text>
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          <Text style={styles.sectionTitle}>Test Items</Text>
          {items.length === 0 ? (
            <Text style={styles.noItemsText}>No items available</Text>
          ) : (
            items.map(item => (
              <View key={item.id} style={styles.itemCard}>
                <Text style={styles.itemTitle}>{item.title || 'Untitled Item'}</Text>
                <Text style={styles.itemId}>ID: {item.id}</Text>

                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={[styles.actionButton, styles.likeButton]}
                    onPress={() => testLike(item)}
                  >
                    <Ionicons name="heart" size={16} color="#fff" />
                    <Text style={styles.actionButtonText}>Like</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.actionButton, styles.saveButton]}
                    onPress={() => testSave(item)}
                  >
                    <Ionicons name="bookmark" size={16} color="#fff" />
                    <Text style={styles.actionButtonText}>Save</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.actionButton, styles.cartButton]}
                    onPress={() => testCart(item)}
                  >
                    <Ionicons name="cart" size={16} color="#fff" />
                    <Text style={styles.actionButtonText}>Cart</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))
          )}

          <Text style={styles.sectionTitle}>Test Results</Text>
          {testResults.length === 0 ? (
            <Text style={styles.noResultsText}>No test results yet</Text>
          ) : (
            testResults.map((result, index) => (
              <View key={index} style={styles.resultItem}>
                <View style={styles.resultHeader}>
                  {getStatusIcon(result.status)}
                  <Text style={[
                    styles.resultTitle,
                    {
                      color:
                        result.status === 'Success' ? 'green' :
                        result.status === 'Error' ? 'red' :
                        result.status === 'Info' ? '#2196F3' : '#6200ee'
                    }
                  ]}>
                    {result.test} - {result.status}
                  </Text>
                </View>
                <Text style={styles.resultMessage}>{result.message}</Text>
                <Text style={styles.timestamp}>{new Date(result.timestamp).toLocaleTimeString()}</Text>
              </View>
            ))
          )}
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
  },
  refreshButton: {
    backgroundColor: '#6200ee',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  clearButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    marginTop: 8,
  },
  noItemsText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
  },
  itemCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  itemId: {
    fontSize: 12,
    color: '#666',
    marginBottom: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    flex: 1,
    marginHorizontal: 4,
    justifyContent: 'center',
  },
  likeButton: {
    backgroundColor: '#FF6B6B',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
  },
  cartButton: {
    backgroundColor: '#2196F3',
  },
  actionButtonText: {
    color: '#fff',
    marginLeft: 4,
    fontWeight: 'bold',
  },
  noResultsText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
  },
  resultItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  resultTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  resultMessage: {
    fontSize: 14,
    marginBottom: 4,
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
    alignSelf: 'flex-end',
  },
});

export default InteractionTestScreen;
