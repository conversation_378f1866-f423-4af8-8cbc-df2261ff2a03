# Tinder-Style Swipe Animation System

## Overview

This implementation provides a complete Tinder-style swiping animation system for React Native applications. It replaces the previous swipe behavior with a one-directional swipe-and-dismiss pattern that matches modern dating app interactions.

## Key Features

### ✅ **Core Animation Behavior**
- **Permanent Exit**: Cards permanently exit screen when swiped (no return animation)
- **Smooth Tracking**: Real-time gesture tracking during drag with 60fps performance
- **Auto-Completion**: Automatic completion when swipe threshold or velocity is reached
- **Stack System**: Next card appears from behind with smooth scaling animations

### ✅ **Gesture Mechanics**
- **Cross-Platform**: Touch and mouse drag support for mobile and desktop
- **Smart Thresholds**: 30% screen width distance OR 800px/s velocity triggers completion
- **Velocity-Based**: Fast swipes complete regardless of distance traveled
- **Rotation Effect**: Cards tilt naturally based on horizontal movement (max 15°)

### ✅ **Visual Effects**
- **Scale Feedback**: Cards scale down slightly (95%) while being dragged
- **Color Overlays**: Green for right swipe (like), red for left swipe (dislike)
- **Stack Animation**: Cards behind scale up and move forward as current card is dismissed
- **Spring Physics**: Smooth spring animations for natural feel

## Implementation Details

### File Structure
```
hooks/
  └── useSwipeGestures.js     # Core gesture detection and animation logic
components/
  ├── CardStack.js            # Main card stack component
  └── CardStack.styles.js     # Styling for cards and overlays
config/
  └── tinderSwipeConfig.js    # Configuration and presets
docs/
  └── TinderSwipeImplementation.md  # This documentation
```

### Core Configuration

```javascript
const SWIPE_CONFIG = {
  // Gesture thresholds
  SWIPE_THRESHOLD: SCREEN_WIDTH * 0.3,    // 30% of screen width
  VELOCITY_THRESHOLD: 800,                 // Minimum velocity for auto-complete
  
  // Visual effects
  MAX_ROTATION: 15,                        // Maximum rotation degrees
  SCALE_FACTOR: 0.95,                      // Card scales down during drag
  OVERLAY_OPACITY: 0.7,                    // Color overlay opacity
  
  // Animation timing
  SPRING_CONFIG: {
    damping: 20,
    stiffness: 300,
    mass: 1,
  },
};
```

### Gesture Detection Logic

1. **onStart**: Card scales down slightly for visual feedback
2. **onUpdate**: 
   - Track horizontal/vertical movement
   - Apply rotation based on horizontal distance
   - Show color overlay based on swipe direction
   - Animate stack cards slightly
3. **onEnd**: 
   - Check distance threshold (30% screen width)
   - Check velocity threshold (800px/s)
   - Complete swipe or return to center

### Animation States

#### Swipe Completion
```javascript
// Card exits screen with spring animation
translateX.value = withSpring(exitX, {
  damping: 20,
  stiffness: 300,
  velocity: gestureVelocity * 0.5
});

// Stack cards move forward
stackAnimations.forEach((animation, index) => {
  animation.scale.value = withSpring(1 - index * 0.05);
  animation.translateY.value = withSpring(index * 20);
});
```

#### Incomplete Swipe
```javascript
// Return to center with spring animation
translateX.value = withSpring(0);
rotate.value = withSpring(0);
scale.value = withSpring(1);
```

## Usage Examples

### Basic Implementation
```javascript
import { useSwipeGestures } from '../hooks/useSwipeGestures';

const MyCardStack = ({ items, currentIndex, onSwipeComplete }) => {
  const { 
    panGesture, 
    topCardStyle, 
    overlayStyle,
    stackCardStyles 
  } = useSwipeGestures({
    items,
    currentIndex,
    onSwipeComplete,
  });

  return (
    <View style={styles.container}>
      {items.map((item, index) => (
        <Card
          key={item.id}
          panGesture={index === 0 ? panGesture : null}
          animatedStyle={index === 0 ? topCardStyle : stackCardStyles[index - 1]}
        />
      ))}
    </View>
  );
};
```

### Custom Configuration
```javascript
import { getPresetConfig } from '../config/tinderSwipeConfig';

// Use preset configuration
const config = getPresetConfig('sensitive'); // or 'standard', 'precise'

// Or create custom configuration
const customConfig = {
  SWIPE_THRESHOLD_PERCENTAGE: 0.25,  // 25% instead of 30%
  VELOCITY_THRESHOLD: 600,           // Lower velocity threshold
  MAX_ROTATION: 20,                  // More rotation
};
```

## Performance Optimizations

### 1. **Native Driver Usage**
All animations use the native driver for 60fps performance:
```javascript
useNativeDriver: true
```

### 2. **Worklet Functions**
Gesture handlers run on UI thread:
```javascript
.onUpdate((event) => {
  'worklet';
  // Gesture logic runs on UI thread
});
```

### 3. **Minimal Re-renders**
State updates are batched and optimized to prevent unnecessary re-renders.

### 4. **Memory Management**
Animations are properly cleaned up and reused to prevent memory leaks.

## Testing Guidelines

### Swipe Threshold Testing
```javascript
// Test different threshold values
const testThresholds = [0.2, 0.3, 0.4]; // 20%, 30%, 40%

testThresholds.forEach(threshold => {
  console.log(`Testing ${threshold * 100}% threshold`);
  // Perform swipe tests
});
```

### Velocity Testing
```javascript
// Test velocity-based completion
const testVelocities = [600, 800, 1000]; // px/s

testVelocities.forEach(velocity => {
  console.log(`Testing ${velocity}px/s velocity`);
  // Perform fast swipe tests
});
```

### Performance Monitoring
```javascript
// Monitor frame rate during animations
import { PerformanceMonitor } from 'react-native-performance';

PerformanceMonitor.startProfiling();
// Perform swipe actions
PerformanceMonitor.stopProfiling();
```

## Cross-Browser Compatibility

### Supported Platforms
- ✅ iOS (React Native)
- ✅ Android (React Native)
- ✅ Web (React Native Web)

### Browser Support
- ✅ Chrome 70+
- ✅ Safari 12+
- ✅ Firefox 65+
- ✅ Edge 79+

### Touch Events
- ✅ Touch devices (mobile/tablet)
- ✅ Mouse devices (desktop)
- ✅ Hybrid devices (Surface, etc.)

## Accessibility Features

### Keyboard Navigation
```javascript
// Add keyboard support for accessibility
const handleKeyPress = (event) => {
  if (event.key === 'ArrowLeft') {
    completeSwipe('left');
  } else if (event.key === 'ArrowRight') {
    completeSwipe('right');
  }
};
```

### Screen Reader Support
```javascript
<View 
  accessible={true}
  accessibilityLabel="Swipe card left to dislike, right to like"
  accessibilityRole="button"
>
  <Card />
</View>
```

## Troubleshooting

### Common Issues

1. **Cards not swiping**: Check gesture handler is properly attached
2. **Jerky animations**: Ensure native driver is enabled
3. **Memory leaks**: Verify animations are cleaned up properly
4. **Poor performance**: Check for unnecessary re-renders

### Debug Mode
```javascript
// Enable debug logging
const DEBUG_SWIPE = __DEV__;

if (DEBUG_SWIPE) {
  console.log('[TinderSwipe] Gesture started');
  console.log('[TinderSwipe] Distance:', translationX);
  console.log('[TinderSwipe] Velocity:', velocityX);
}
```

## Migration from Previous System

### Key Changes
1. **Removed**: Reset animations that return cards to center
2. **Added**: Proper velocity-based completion
3. **Improved**: Spring physics for natural feel
4. **Enhanced**: Visual feedback with color overlays

### Breaking Changes
- `resetCardAnimations` replaced with `resetCardPosition`
- New required props: `overlayStyle`, `stackCardStyles`
- Configuration moved to separate file

This implementation provides a production-ready Tinder-style swiping system with excellent performance, accessibility, and customization options.
