import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Alert, ScrollView, Platform, ActivityIndicator, Linking } from 'react-native';
import { getAuth, signOut } from 'firebase/auth';
import { doc, onSnapshot } from 'firebase/firestore';
import { db } from '../firebase.config';
import { Ionicons } from '@expo/vector-icons';

// Helper function to format address with graceful handling of missing fields
const formatAddress = (address) => {
  const parts = [];

  if (address.street) parts.push(address.street);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);

  // Handle both zipCode and pincode for backward compatibility
  const postalCode = address.zipCode || address.pincode;
  if (postalCode) parts.push(postalCode);

  if (address.country) parts.push(address.country);

  return parts.join(', ');
};

const ProfileScreen = ({ navigation }) => {
  const [userInfo, setUserInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const auth = getAuth();
  const user = auth.currentUser;

  useEffect(() => {
    let unsubscribe = null;
    if (user) {
      setLoading(true);
      const userDocRef = doc(db, 'users', user.uid);

      unsubscribe = onSnapshot(userDocRef, (docSnap) => {
        if (docSnap.exists()) {
          const data = docSnap.data();
          setUserInfo({
            name: data.name || user.displayName || 'User Name',
            bio: data.bio || 'No bio yet.',
            address: data.address || null,
            storeLink: data.website || null,
            profilePicture: data.profilePictureUrl || user.photoURL || 'https://via.placeholder.com/150',
            isSeller: data.isSeller || false,
            isAdmin: data.isAdmin || false,
          });
        } else {
          console.log("No user profile document found!");
          setUserInfo({
            name: user.displayName || 'User Name',
            bio: 'Welcome! Edit your profile to add a bio.',
            profilePicture: user.photoURL || 'https://via.placeholder.com/150',
            isSeller: false,
            isAdmin: false,
          });
        }
        setLoading(false);
      }, (error) => {
        console.error("Error fetching user data:", error);
        Alert.alert("Error", "Could not fetch user profile.");
        setLoading(false);
        setUserInfo({
          name: 'Error Loading',
          bio: 'Could not load profile.',
          profilePicture: 'https://via.placeholder.com/150',
          isSeller: false,
          isAdmin: false,
        });
      });
    } else {
      setLoading(false);
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [user]);

  const handleLogout = () => {
    signOut(auth).then(() => {
    }).catch((error) => {
      console.error("Logout Error:", error);
      Alert.alert("Logout Failed", error.message);
    });
  };

  const handleEditProfile = () => {
    // Try to navigate directly to EditProfile first
    try {
      // Check if we can navigate directly
      navigation.navigate('EditProfile');
    } catch (error) {
      // If direct navigation fails, try using the parent navigator
      try {
        navigation.getParent().navigate('EditProfile');
      } catch (navError) {
        // If both methods fail, use a more robust approach
        console.error("Navigation error:", navError);
        // Navigate to MainApp first, then to EditProfile
        navigation.navigate('MainApp', { screen: 'MyProfile' });
        // Use a timeout to ensure the navigation to MainApp completes first
        setTimeout(() => {
          navigation.getParent().navigate('EditProfile');
        }, 100);
      }
    }
  };

  const handleViewUploads = () => {
    // For sellers, navigate to the Listings tab instead of UserUploads screen
    if (userInfo?.isSeller) {
      // Check if we're already on the Listings tab
      const currentRoute = navigation.getState().routes.find(route => route.name === 'Listings');
      if (currentRoute) {
        navigation.navigate('Listings');
      } else {
        // If we're in a nested stack, navigate to the main tab first
        navigation.navigate('MainApp', { screen: 'Listings' });
      }
    } else {
      // For non-sellers, use the original behavior
      navigation.navigate('UserUploads', { userId: user?.uid });
    }
  };

  const handleViewWishlist = () => {
    navigation.navigate('UserWishlist', { userId: user?.uid });
  };

  const handleViewLikes = () => {
    navigation.navigate('UserLikes', { userId: user?.uid });
  };

  const handleViewOrders = () => {
    navigation.navigate('OrderHistory');
  };

  const handleSettings = () => {
    // Direct approach for both seller and buyer
    if (userInfo?.isSeller) {
      // For sellers
      try {
        // Try to navigate directly to Settings
        navigation.getParent().navigate('Settings', { source: 'MyProfile', isSeller: userInfo.isSeller });
      } catch (error) {
        console.error("Error navigating to Settings for seller:", error);
        Alert.alert("Navigation Error", "Could not navigate to Settings. Please try again.");
      }
    } else {
      // For buyers
      try {
        // First navigate to the main app to ensure we're in the right context
        navigation.navigate('MainApp');

        // Then use a timeout to ensure the first navigation completes
        setTimeout(() => {
          // Now navigate to Settings from the root navigator
          navigation.getParent().navigate('Settings', { source: 'MyProfile', isSeller: userInfo.isSeller });
        }, 100);
      } catch (error) {
        console.error("Error navigating to Settings for buyer:", error);
        Alert.alert("Navigation Error", "Could not navigate to Settings. Please try again.");
      }
    }
  };

  const ProfileActionButton = ({ iconName, title, onPress }) => (
    <TouchableOpacity style={styles.actionButton} onPress={onPress}>
      <Ionicons name={iconName} size={24} color="#555" style={styles.actionIcon} />
      <Text style={styles.actionText}>{title}</Text>
      <Ionicons name="chevron-forward-outline" size={20} color="#ccc" />
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainerCentered}>
        <ActivityIndicator size="large" color="#FF6B6B" />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back-outline" size={28} color="#333" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.settingsButton} onPress={handleSettings}>
          <Ionicons name="settings-outline" size={26} color="#333" />
        </TouchableOpacity>
      </View>

      {userInfo ? (
        <View style={styles.profileInfoContainer}>
          <Image source={{ uri: userInfo.profilePicture }} style={styles.profileImage} />
          <View style={styles.nameSellerContainer}>
            <Text style={styles.userName}>{userInfo.name}</Text>
            {userInfo.isSeller && (
              <View style={styles.sellerBadge}>
                <Ionicons name="storefront-outline" size={16} color="#FF6B6B" />
                <Text style={styles.sellerBadgeText}>Seller</Text>
              </View>
            )}
          </View>
          <Text style={styles.userBio}>{userInfo.bio}</Text>
          {userInfo.address && (
            <Text style={styles.addressText}>
              {formatAddress(userInfo.address)}
            </Text>
          )}
          {userInfo.storeLink ? (
            <Text style={[styles.userBio, styles.linkText]} onPress={() => Linking.openURL(userInfo.storeLink)}>{userInfo.storeLink}</Text>
          ) : null}
          <TouchableOpacity style={styles.editProfileButton} onPress={handleEditProfile}>
            <Text style={styles.editProfileButtonText}>Edit Profile</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.loadingContainer}>
          <Text>Could not load profile information.</Text>
        </View>
      )}

      <View style={styles.actionsContainer}>
        {userInfo.isSeller ? (
          // Only show My Listings for sellers
          <ProfileActionButton iconName="list-outline" title="My Listings" onPress={handleViewUploads} />
        ) : (
          // Show these options only for buyers
          <>
            <ProfileActionButton iconName="bookmark-outline" title="Saved Collection" onPress={handleViewWishlist} />
            <ProfileActionButton iconName="thumbs-up-outline" title="My Likes" onPress={handleViewLikes} />
            <ProfileActionButton iconName="receipt-outline" title="My Orders" onPress={handleViewOrders} />
          </>)}
      </View>

      {/* Admin Section - Only show for admins */}
      {userInfo?.isAdmin && (
        <View style={styles.actionsContainer}>
          <ProfileActionButton
            iconName="shield-outline"
            title="Admin Dashboard"
            onPress={() => navigation.navigate('AdminDashboard')}
          />
        </View>
      )}



      {/* Support Section */}
      <View style={styles.actionsContainer}>
        <ProfileActionButton
          iconName="help-circle-outline"
          title="Help & Support"
          onPress={() => navigation.navigate('Support')}
        />
      </View>

      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <Ionicons name="log-out-outline" size={24} color="#FF6B6B" style={styles.actionIcon} />
        <Text style={styles.logoutButtonText}>Logout</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'android' ? 40 : 50,
    paddingHorizontal: 15,
    paddingBottom: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  settingsButton: {
    padding: 5,
  },
  profileInfoContainer: {
    alignItems: 'center',
    paddingVertical: 30,
    backgroundColor: '#fff',
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 15,
    borderWidth: 3,
    borderColor: '#FFC0CB',
  },
  nameSellerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
    textAlign: 'center',
  },
  sellerBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffebee',
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: '#FFC0CB',
    marginTop: Platform.OS === 'ios' ? 0 : 4,
  },
  sellerBadgeText: {
    marginLeft: 4,
    color: '#FF6B6B',
    fontSize: 14,
    fontWeight: '600',
  },
  userBio: {
    fontSize: 15,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
    paddingHorizontal: 20,
    lineHeight: 22,
  },
  addressText: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  linkText: {
    color: '#007AFF',
    textDecorationLine: 'underline',
  },
  editProfileButton: {
    borderColor: '#FF6B6B',
    borderWidth: 1,
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 25,
    marginTop: 10,
  },
  editProfileButtonText: {
    color: '#FF6B6B',
    fontWeight: 'bold',
    fontSize: 14,
  },
  actionsContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginHorizontal: 15,
    marginBottom: 20,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#eee',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 18,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  actionIcon: {
    marginRight: 15,
  },
  actionText: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    marginHorizontal: 15,
    paddingVertical: 18,
    borderWidth: 1,
    borderColor: '#eee',
    marginBottom: 30,
  },
  logoutButtonText: {
    fontSize: 16,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginLeft: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingContainerCentered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
});

export default ProfileScreen;
