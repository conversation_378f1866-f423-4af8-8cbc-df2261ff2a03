import { db } from '../firebase.config';
import { collection, getDocs, query, limit, doc, getDoc, addDoc } from 'firebase/firestore';

/**
 * Debug utility to help troubleshoot order data issues
 */

export const debugOrderCollection = async () => {
  console.log('🔍 [Debug] Starting order collection analysis...');

  try {
    // Test 1: Check if orders collection exists and is accessible
    console.log('📋 [Debug] Test 1: Checking orders collection access...');
    const ordersRef = collection(db, 'orders');
    const testSnapshot = await getDocs(query(ordersRef, limit(1)));
    console.log('✅ [Debug] Orders collection accessible. Sample size:', testSnapshot.size);

    // Test 2: Get total count (up to 1000 docs)
    console.log('📊 [Debug] Test 2: Getting total order count...');
    const allOrdersSnapshot = await getDocs(ordersRef);
    console.log('📈 [Debug] Total orders found:', allOrdersSnapshot.size);

    // Test 3: Analyze first few orders
    console.log('🔬 [Debug] Test 3: Analyzing order structure...');
    const sampleOrders = allOrdersSnapshot.docs.slice(0, 5);

    sampleOrders.forEach((doc, index) => {
      const data = doc.data();
      console.log(`📄 [Debug] Order ${index + 1} (${doc.id}):`);
      console.log('  - Keys:', Object.keys(data));
      console.log('  - Status:', data.status || data.orderStatus || 'undefined');
      console.log('  - CreatedAt type:', typeof data.createdAt);
      console.log('  - CreatedAt value:', data.createdAt);
      console.log('  - User ID:', data.userId || 'undefined');
      console.log('  - Seller ID:', data.sellerId || 'undefined');
      console.log('  - Total:', data.total || data.totalAmount || 'undefined');
    });

    // Test 4: Check for different possible field names
    console.log('🏷️ [Debug] Test 4: Field name analysis...');
    const fieldAnalysis = {
      status: 0,
      orderStatus: 0,
      createdAt: 0,
      userId: 0,
      sellerId: 0,
      total: 0,
      totalAmount: 0,
      buyerEmail: 0,
      sellerEmail: 0,
      userEmail: 0
    };

    allOrdersSnapshot.docs.forEach(doc => {
      const data = doc.data();
      Object.keys(fieldAnalysis).forEach(field => {
        if (data[field] !== undefined) {
          fieldAnalysis[field]++;
        }
      });
    });

    console.log('📊 [Debug] Field usage statistics:');
    Object.entries(fieldAnalysis).forEach(([field, count]) => {
      console.log(`  - ${field}: ${count}/${allOrdersSnapshot.size} orders (${((count / allOrdersSnapshot.size) * 100).toFixed(1)}%)`);
    });

    return {
      success: true,
      totalOrders: allOrdersSnapshot.size,
      sampleOrders: sampleOrders.map(doc => ({ id: doc.id, ...doc.data() })),
      fieldAnalysis
    };

  } catch (error) {
    console.error('❌ [Debug] Error analyzing orders collection:', error);
    return {
      success: false,
      error: error.message,
      totalOrders: 0
    };
  }
};

export const debugAdminAccess = async (userId) => {
  console.log('🔐 [Debug] Checking admin access for user:', userId);

  try {
    // Check if user document exists
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      console.log('❌ [Debug] User document does not exist');
      return { success: false, reason: 'User document not found' };
    }

    const userData = userDoc.data();
    console.log('👤 [Debug] User data keys:', Object.keys(userData));
    console.log('🔑 [Debug] Admin status:', userData.isAdmin);
    console.log('🏷️ [Debug] User role:', userData.role);

    return {
      success: true,
      isAdmin: userData.isAdmin,
      role: userData.role,
      userData
    };

  } catch (error) {
    console.error('❌ [Debug] Error checking admin access:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

export const createTestOrder = async (userId) => {
  console.log('🧪 [Debug] Creating test order for debugging...');

  try {
    const testOrder = {
      userId: userId,
      sellerId: 'test-seller-id',
      status: 'pending',
      orderStatus: 'pending',
      total: 99.99,
      totalAmount: 99.99,
      buyerEmail: '<EMAIL>',
      sellerEmail: '<EMAIL>',
      userEmail: '<EMAIL>',
      createdAt: new Date(),
      updatedAt: new Date(),
      items: [
        {
          id: 'test-item-1',
          title: 'Test Item',
          price: 99.99,
          quantity: 1
        }
      ],
      shippingAddress: {
        street: '123 Test St',
        city: 'Test City',
        state: 'Test State',
        zipCode: '12345'
      },
      paymentStatus: 'completed',
      paymentId: 'test-payment-id',
      isTestOrder: true // Flag to identify test orders
    };

    const ordersRef = collection(db, 'orders');
    const docRef = await addDoc(ordersRef, testOrder);

    console.log('✅ [Debug] Test order created with ID:', docRef.id);
    return { success: true, orderId: docRef.id };

  } catch (error) {
    console.error('❌ [Debug] Error creating test order:', error);
    return { success: false, error: error.message };
  }
};

// Helper function to run all debug tests
export const runFullOrderDebug = async (userId) => {
  console.log('🚀 [Debug] Running full order debug suite...');

  const results = {
    timestamp: new Date().toISOString(),
    userId,
    tests: {}
  };

  // Test admin access
  results.tests.adminAccess = await debugAdminAccess(userId);

  // Test order collection
  results.tests.orderCollection = await debugOrderCollection();

  console.log('📋 [Debug] Full debug results:', results);
  return results;
};
