import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
  Linking,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import { collection, addDoc, query, where, orderBy, getDocs, doc, getDoc } from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';

const SupportScreen = ({ navigation }) => {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [showTickets, setShowTickets] = useState(false);
  const [userTickets, setUserTickets] = useState([]);
  const [ticketsLoading, setTicketsLoading] = useState(false);

  const supportCategories = [
    { id: 'account', title: 'Account Issues', icon: 'person-outline' },
    { id: 'orders', title: 'Order Problems', icon: 'bag-outline' },
    { id: 'payments', title: 'Payment Issues', icon: 'card-outline' },
    { id: 'technical', title: 'Technical Problems', icon: 'settings-outline' },
    { id: 'seller', title: 'Seller Support', icon: 'storefront-outline' },
    { id: 'refund', title: 'Refunds & Returns', icon: 'return-up-back-outline' },
    { id: 'other', title: 'Other Issues', icon: 'help-outline' }
  ];

  useEffect(() => {
    fetchUserInfo();
  }, []);

  const fetchUserInfo = async () => {
    if (!auth.currentUser) return;

    try {
      const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
      if (userDoc.exists()) {
        setUserInfo(userDoc.data());
      }
    } catch (error) {
      console.error('Error fetching user info:', error);
    }
  };

  const fetchUserTickets = async () => {
    if (!auth.currentUser) return;

    setTicketsLoading(true);
    try {
      const ticketsRef = collection(db, 'supportTickets');
      const q = query(
        ticketsRef,
        where('userId', '==', auth.currentUser.uid),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const tickets = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      }));

      setUserTickets(tickets);
    } catch (error) {
      console.error('Error fetching tickets:', error);
      Alert.alert('Error', 'Failed to load your support tickets');
    } finally {
      setTicketsLoading(false);
    }
  };

  const submitTicket = async () => {
    if (!auth.currentUser) {
      navigation.navigate('Auth');
      return;
    }

    if (!selectedCategory || !subject.trim() || !message.trim()) {
      Alert.alert('Missing Information', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    try {
      const ticketData = {
        userId: auth.currentUser.uid,
        userEmail: auth.currentUser.email,
        userName: userInfo?.name || 'User',
        category: selectedCategory,
        subject: subject.trim(),
        message: message.trim(),
        status: 'open',
        priority: 'normal',
        createdAt: new Date(),
        updatedAt: new Date(),
        responses: [],
        isSeller: userInfo?.isSeller || false
      }; const docRef = await addDoc(collection(db, 'supportTickets'), ticketData);

      // Email notifications will be automatically sent via Firebase functions
      console.log('Support ticket created with ID:', docRef.id);

      Alert.alert(
        'Ticket Submitted',
        'Your support request has been submitted successfully. You will receive a confirmation email and a response within 24 hours.',
        [
          {
            text: 'OK',
            onPress: () => {
              setSelectedCategory('');
              setSubject('');
              setMessage('');
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error submitting ticket:', error);
      Alert.alert('Error', 'Failed to submit your request. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const openEmail = () => {
    const email = '<EMAIL>';
    const subject = 'SwipeSense Support Request';
    const body = 'Hi SwipeSense Team,\n\nI need help with:\n\n';

    Linking.openURL(`mailto:${email}?subject=${subject}&body=${body}`);
  };

  const renderTicketStatus = (status) => {
    const statusConfig = {
      open: { color: '#FF6B6B', text: 'Open' },
      'in-progress': { color: '#4ECDC4', text: 'In Progress' },
      resolved: { color: '#45B7D1', text: 'Resolved' },
      closed: { color: '#96CEB4', text: 'Closed' }
    };

    const config = statusConfig[status] || statusConfig.open;

    return (
      <View style={[styles.statusBadge, { backgroundColor: config.color }]}>
        <Text style={styles.statusText}>{config.text}</Text>
      </View>
    );
  };

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Support Center</Text>
        <TouchableOpacity
          style={styles.ticketsButton}
          onPress={() => {
            setShowTickets(true);
            fetchUserTickets();
          }}
        >
          <Ionicons name="time-outline" size={24} color="#FF6B6B" />
        </TouchableOpacity>
      </SafeAreaHeader>

      <View style={styles.container}>

        <ScrollView style={styles.content}>
          {/* Quick Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Help</Text>
            <View style={styles.quickActions}>
              <TouchableOpacity
                style={styles.quickAction}
                onPress={() => navigation.navigate('FAQ')}
              >
                <Ionicons name="help-circle-outline" size={24} color="#4ECDC4" />
                <Text style={styles.quickActionText}>FAQ</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.quickAction}
                onPress={() => navigation.navigate('LiveChat')}
              >
                <Ionicons name="chatbubble-outline" size={24} color="#4ECDC4" />
                <Text style={styles.quickActionText}>Live Chat</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.quickAction}
                onPress={openEmail}
              >
                <Ionicons name="mail-outline" size={24} color="#4ECDC4" />
                <Text style={styles.quickActionText}>Email Us</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Submit New Ticket */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Submit Support Request</Text>

            <Text style={styles.label}>Category *</Text>
            <View style={styles.categoryGrid}>
              {supportCategories.map(category => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryItem,
                    selectedCategory === category.id && styles.categoryItemSelected
                  ]}
                  onPress={() => setSelectedCategory(category.id)}
                >
                  <Ionicons
                    name={category.icon}
                    size={20}
                    color={selectedCategory === category.id ? '#FFF' : '#666'}
                  />
                  <Text style={[
                    styles.categoryText,
                    selectedCategory === category.id && styles.categoryTextSelected
                  ]}>
                    {category.title}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.label}>Subject *</Text>
            <TextInput
              style={styles.input}
              value={subject}
              onChangeText={setSubject}
              placeholder="Brief description of your issue"
              maxLength={100}
            />

            <Text style={styles.label}>Message *</Text>
            <TextInput
              style={[styles.input, styles.messageInput]}
              value={message}
              onChangeText={setMessage}
              placeholder="Please provide detailed information about your issue..."
              multiline
              textAlignVertical="top"
              maxLength={1000}
            />

            <TouchableOpacity
              style={[styles.submitButton, loading && styles.submitButtonDisabled]}
              onPress={submitTicket}
              disabled={loading}
            >
              <Text style={styles.submitButtonText}>
                {loading ? 'Submitting...' : 'Submit Request'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Contact Info */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contact Information</Text>
            <View style={styles.contactInfo}>
              <View style={styles.contactItem}>
                <Ionicons name="mail-outline" size={16} color="#666" />
                <Text style={styles.contactText}><EMAIL></Text>
              </View>
              <View style={styles.contactItem}>
                <Ionicons name="time-outline" size={16} color="#666" />
                <Text style={styles.contactText}>Response Time: 24 hours</Text>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* My Tickets Modal */}
        <Modal
          visible={showTickets}
          animationType="slide"
          presentationStyle="pageSheet"
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>My Support Tickets</Text>
              <TouchableOpacity onPress={() => setShowTickets(false)}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              {ticketsLoading ? (
                <Text style={styles.loadingText}>Loading tickets...</Text>
              ) : userTickets.length === 0 ? (
                <Text style={styles.emptyText}>No support tickets found</Text>
              ) : (
                userTickets.map(ticket => (
                  <View key={ticket.id} style={styles.ticketItem}>
                    <View style={styles.ticketHeader}>
                      <Text style={styles.ticketSubject}>{ticket.subject}</Text>
                      {renderTicketStatus(ticket.status)}
                    </View>
                    <Text style={styles.ticketCategory}>{ticket.category}</Text>
                    <Text style={styles.ticketDate}>
                      {ticket.createdAt.toLocaleDateString()}
                    </Text>
                    <Text style={styles.ticketMessage} numberOfLines={2}>
                      {ticket.message}
                    </Text>
                  </View>
                ))
              )}
            </ScrollView>
          </View>
        </Modal>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  ticketsButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickAction: {
    alignItems: 'center',
    padding: 15,
  },
  quickActionText: {
    marginTop: 8,
    fontSize: 12,
    color: '#666',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    marginTop: 15,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryItem: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E9ECEF',
    marginBottom: 10,
  },
  categoryItemSelected: {
    backgroundColor: '#4ECDC4',
    borderColor: '#4ECDC4',
  },
  categoryText: {
    marginLeft: 8,
    fontSize: 12,
    color: '#666',
    flex: 1,
  },
  categoryTextSelected: {
    color: '#FFF',
  },
  input: {
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#333',
    backgroundColor: '#FFF',
  },
  messageInput: {
    height: 120,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#4ECDC4',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  submitButtonDisabled: {
    backgroundColor: '#CCC',
  },
  submitButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
  },
  contactInfo: {
    gap: 10,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  contactText: {
    fontSize: 14,
    color: '#666',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginTop: 50,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginTop: 50,
  },
  ticketItem: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  ticketHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  ticketSubject: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    color: '#FFF',
    fontWeight: '600',
  },
  ticketCategory: {
    fontSize: 12,
    color: '#4ECDC4',
    fontWeight: '500',
    marginBottom: 4,
  },
  ticketDate: {
    fontSize: 12,
    color: '#999',
    marginBottom: 8,
  },
  ticketMessage: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default SupportScreen;
