import { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Platform,
  StatusBar,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  deleteDoc,
  updateDoc,
  increment
} from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { useFocusEffect } from '@react-navigation/native';

// Responsive dimensions
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const IMAGE_SIZE = SCREEN_WIDTH * 0.18;
const HEADER_FONT_SIZE = SCREEN_WIDTH * 0.05;
const TITLE_FONT_SIZE = SCREEN_WIDTH * 0.045;
const CATEGORY_FONT_SIZE = SCREEN_WIDTH * 0.04;

const CollectionDetailScreen = ({ route, navigation }) => {
  const { collectionId, collectionName } = route.params;
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const currentUserId = auth.currentUser?.uid;

  const fetchCollectionItems = useCallback(async () => {
    if (!currentUserId || !collectionId) {
      setError("Missing user ID or collection ID");
      setLoading(false);
      return;
    }

    console.log(`Fetching items for collection: ${collectionId}`);
    setLoading(true);
    setError(null);

    try {
      // Get all wishlist items that belong to this collection
      const wishlistRef = collection(db, 'users', currentUserId, 'wishlist');
      const wishlistQuery = query(wishlistRef, where('collectionId', '==', collectionId));
      const wishlistSnap = await getDocs(wishlistQuery);

      if (wishlistSnap.empty) {
        console.log("No items in this collection");
        setItems([]);
        setLoading(false);
        return;
      }

      // Get the item details for each wishlist item
      const itemPromises = wishlistSnap.docs.map(async (docSnapshot) => {
        const wishlistData = docSnapshot.data();
        const itemId = wishlistData.itemId;

        // Get the item details
        const itemDoc = await getDoc(doc(db, 'clothingItems', itemId));

        if (itemDoc.exists()) {
          return {
            id: itemId,
            wishlistDocId: docSnapshot.id,
            ...itemDoc.data(),
            addedAt: wishlistData.addedAt?.toDate() || new Date()
          };
        }
        return null;
      });

      const itemsWithDetails = (await Promise.all(itemPromises)).filter(Boolean);

      // Add a uniqueKey property to each item
      itemsWithDetails.forEach(item => {
        item.uniqueKey = `${item.id}_${item.wishlistDocId}`;
      });

      // Sort by most recently added
      itemsWithDetails.sort((a, b) => b.addedAt - a.addedAt);

      console.log(`Successfully loaded ${itemsWithDetails.length} items for collection`);
      setItems(itemsWithDetails);
    } catch (e) {
      console.error("Error fetching collection items:", e);
      setError("Failed to load collection items");
      setItems([]);
    }
    setLoading(false);
  }, [currentUserId, collectionId]);

  // Initial load
  useEffect(() => {
    if (currentUserId && collectionId) {
      fetchCollectionItems();
    }
  }, [currentUserId, collectionId, fetchCollectionItems]);

  // Refresh when screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log("Collection detail screen focused, refreshing data");
      if (currentUserId && collectionId) {
        fetchCollectionItems();
      }
      return () => {
        console.log("Collection detail screen unfocused");
      };
    }, [currentUserId, collectionId, fetchCollectionItems])
  );

  const removeFromCollection = async (itemId, wishlistDocId) => {
    Alert.alert(
      "Remove Item",
      "Are you sure you want to remove this item from this collection?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Remove",
          onPress: async () => {
            try {
              // Delete the wishlist document
              const wishlistDocRef = doc(db, 'users', currentUserId, 'wishlist', wishlistDocId);
              await deleteDoc(wishlistDocRef);

              // Update the collection item count
              if (collectionId !== 'default' && collectionId !== 'all') {
                const collectionRef = doc(db, 'users', currentUserId, 'collections', collectionId);

                // Get the current collection to check if this was the last item
                const collectionDoc = await getDoc(collectionRef);

                if (collectionDoc.exists()) {
                  const currentItemCount = collectionDoc.data().itemCount || 0;

                  // If this was the last item, we need to update the latestItemImageUrl
                  if (currentItemCount <= 1) {
                    await updateDoc(collectionRef, {
                      itemCount: 0,
                      latestItemImageUrl: null
                    });
                  } else {
                    // Otherwise just decrement the count
                    await updateDoc(collectionRef, {
                      itemCount: increment(-1)
                    });

                    // If this was the item used as the collection thumbnail, we need to update it
                    if (collectionDoc.data().latestItemImageUrl === items.find(i => i.id === itemId)?.imageUrl) {
                      // Find another item in this collection to use as thumbnail
                      const remainingItems = items.filter(i => i.id !== itemId);
                      if (remainingItems.length > 0) {
                        await updateDoc(collectionRef, {
                          latestItemImageUrl: remainingItems[0].imageUrl
                        });
                      }
                    }
                  }
                }
              }

              // Update the UI by removing the item
              setItems(currentItems => currentItems.filter(item => item.wishlistDocId !== wishlistDocId));

            } catch (error) {
              console.error("Error removing from collection:", error);
              Alert.alert("Error", "Failed to remove item from collection");
            }
          },
          style: "destructive"
        }
      ]
    );
  };

  if (loading) return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back-outline" size={24} color="#FF6B6B" />
        </TouchableOpacity>
        <Text style={styles.header}>{collectionName}</Text>
        <View style={styles.placeholderButton} />
      </View>
      <ActivityIndicator style={{ flex: 1 }} size="large" color="#FF6B6B" />
    </SafeAreaView>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.headerContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back-outline" size={24} color="#FF6B6B" />
          </TouchableOpacity>
          <Text style={styles.header}>{collectionName}</Text>
          <View style={styles.placeholderButton} />
        </View>

        {error ? (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle-outline" size={60} color="#FF6B6B" />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => fetchCollectionItems()}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={items}
            keyExtractor={item => item.uniqueKey}
            key="collection-detail-list" // Add a key to ensure proper rendering
            renderItem={({ item }) => (
              <View style={styles.itemRow}>
                <TouchableOpacity
                  style={styles.itemContent}
                  onPress={() => navigation.navigate('ItemDetails', { itemId: item.id })}
                  activeOpacity={0.7}
                >
                  <Image source={{ uri: item.imageUrl }} style={styles.image} />
                  <View style={{ flex: 1 }}>
                    <Text style={styles.title}>{item.title}</Text>
                    <Text style={styles.category}>{item.category}</Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => removeFromCollection(item.id, item.wishlistDocId)}
                >
                  <Ionicons name="close-circle" size={24} color="#FF6B6B" />
                </TouchableOpacity>
              </View>
            )}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="bookmark-outline" size={60} color="#ccc" style={styles.emptyIcon} />
                <Text style={styles.empty}>No items in this collection.</Text>
                <Text style={styles.emptySubText}>Save items to this collection by tapping the bookmark icon on items you like!</Text>
              </View>
            }
            contentContainerStyle={styles.listContent}
            refreshing={loading}
            onRefresh={fetchCollectionItems}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
    // add top padding for Android status bar
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SCREEN_WIDTH * 0.04,
    paddingVertical: SCREEN_WIDTH * 0.02,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    padding: 5,
  },
  header: {
    fontSize: HEADER_FONT_SIZE,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholderButton: {
    width: 34,
    height: 34,
  },
  listContent: {
    padding: 15,
    paddingBottom: 30,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    backgroundColor: '#fff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  itemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  image: {
    width: IMAGE_SIZE,
    height: IMAGE_SIZE,
    borderRadius: IMAGE_SIZE * 0.15,
    marginRight: SCREEN_WIDTH * 0.04,
  },
  title: {
    fontSize: TITLE_FONT_SIZE,
    fontWeight: '500',
    color: '#333',
    marginBottom: SCREEN_WIDTH * 0.01,
  },
  category: {
    fontSize: CATEGORY_FONT_SIZE,
    color: '#666',
  },
  removeButton: {
    padding: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  emptyIcon: {
    marginBottom: 15,
  },
  empty: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
    marginBottom: 5,
  },
  emptySubText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
});

export default CollectionDetailScreen;
