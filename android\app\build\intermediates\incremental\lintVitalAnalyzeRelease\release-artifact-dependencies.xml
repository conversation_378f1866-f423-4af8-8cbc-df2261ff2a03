<dependencies>
  <compile
      roots=":@@:react-native-google-signin_google-signin::release,:@@:react-native-screens::release,com.facebook.react:react-android:0.79.2:release@aar,com.facebook.fresco:fresco:3.6.0@aar,com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar,com.facebook.fresco:middleware:3.6.0@aar,com.facebook.fresco:ui-common:3.6.0@aar,:@@:expo::release,:@@:expo-file-system::release,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.squareup.okhttp3:okhttp:4.9.2@jar,com.squareup.okio:okio:2.9.0@jar,:@@:expo-modules-core::release,:@@:expo-dev-launcher::release,:@@:expo-dev-menu::release,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,:@@:expo-image-loader::release,com.github.bumptech.glide:glide:4.16.0@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment:1.6.1@aar,androidx.activity:activity:1.10.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,host.exp.exponent:expo.modules.webbrowser:14.1.6@aar,androidx.browser:browser:1.6.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-service:2.6.2@aar,androidx.lifecycle:lifecycle-process:2.6.2@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar,androidx.core:core-ktx:1.13.1@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.media:media:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-common:2.6.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar,androidx.savedstate:savedstate:1.2.1@aar,com.facebook.fresco:fbcore:3.6.0@aar,com.facebook.fresco:drawee:3.6.0@aar,io.insert-koin:koin-core-jvm:3.5.6@jar,co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar,co.touchlab:stately-concurrency-jvm:2.0.6@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,:@@:expo-constants::release,:@@:expo-image-manipulator::release,:@@:expo-dev-client::release,androidx.databinding:viewbinding:8.8.2@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.8.1@jar,androidx.annotation:annotation-experimental:1.4.0@aar,com.facebook.fresco:ui-core:3.6.0@aar,com.facebook.fresco:imagepipeline:3.6.0@aar,com.facebook.fresco:imagepipeline-base:3.6.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,:@@:react-native-async-storage_async-storage::release,:@@:react-native-picker_picker::release,:@@:lottie-react-native::release,:@@:react-native-fast-image::release,:@@:react-native-gesture-handler::release,:@@:react-native-razorpay::release,:@@:react-native-reanimated::release,:@@:react-native-safe-area-context::release,com.facebook.fresco:animated-gif:3.6.0@aar,com.facebook.fresco:webpsupport:3.6.0@aar,com.facebook.react:hermes-android:0.79.2:release@aar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:1.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.autofill:autofill:1.1.0@aar,com.facebook.fbjni:fbjni:0.7.0@aar,com.facebook.soloader:soloader:0.12.1@aar,com.facebook.soloader:nativeloader:0.12.1@jar,com.facebook.soloader:annotation:0.12.1@jar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.fresco:imagepipeline-native:3.6.0@aar,com.facebook.fresco:memory-type-ashmem:3.6.0@aar,com.facebook.fresco:memory-type-native:3.6.0@aar,com.facebook.fresco:memory-type-java:3.6.0@aar,com.facebook.fresco:nativeimagefilters:3.6.0@aar,com.facebook.fresco:nativeimagetranscoder:3.6.0@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,javax.inject:javax.inject:1@jar,commons-io:commons-io:2.6@jar,commons-codec:commons-codec:1.10@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,androidx.exifinterface:exifinterface:1.3.7@aar,host.exp.exponent:expo.modules.application:6.1.4@aar,expo.modules.asset:expo.modules.asset:11.1.5@aar,host.exp.exponent:expo.modules.crypto:14.1.4@aar,host.exp.exponent:expo.modules.font:13.3.1@aar,host.exp.exponent:expo.modules.imagepicker:16.1.4@aar,host.exp.exponent:expo.modules.keepawake:14.1.4@aar,host.exp.exponent:expo.modules.lineargradient:14.1.4@aar,:@@:expo-dev-menu-interface::release,:@@:expo-eas-client::release,:@@:expo-json-utils::release,:@@:expo-linking::release,:@@:expo-manifests::release,:@@:expo-notifications::release,:@@:expo-structured-headers::release,:@@:expo-updates::release,:@@:expo-updates-interface::release">
    <dependency
        name=":@@:react-native-google-signin_google-signin::release"
        simpleName="SwipeSense:react-native-google-signin_google-signin"/>
    <dependency
        name=":@@:react-native-screens::release"
        simpleName="SwipeSense:react-native-screens"/>
    <dependency
        name="com.facebook.react:react-android:0.79.2:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="com.facebook.fresco:fresco:3.6.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.facebook.fresco:middleware:3.6.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name=":@@:expo::release"
        simpleName="host.exp.exponent:expo"/>
    <dependency
        name=":@@:expo-file-system::release"
        simpleName="host.exp.exponent:expo-file-system"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:2.9.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name=":@@:expo-modules-core::release"
        simpleName="host.exp.exponent:expo-modules-core"/>
    <dependency
        name=":@@:expo-dev-launcher::release"
        simpleName="host.exp.exponent:expo-dev-launcher"/>
    <dependency
        name=":@@:expo-dev-menu::release"
        simpleName="host.exp.exponent:expo-dev-menu"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name=":@@:expo-image-loader::release"
        simpleName="host.exp.exponent:expo-image-loader"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.fragment:fragment:1.6.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.10.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="host.exp.exponent:expo.modules.webbrowser:14.1.6@aar"
        simpleName="host.exp.exponent:expo.modules.webbrowser"/>
    <dependency
        name="androidx.browser:browser:1.6.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.media:media:1.0.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.6.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="com.facebook.fresco:drawee:3.6.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="io.insert-koin:koin-core-jvm:3.5.6@jar"
        simpleName="io.insert-koin:koin-core-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrent-collections-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrency-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrency-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name=":@@:expo-constants::release"
        simpleName="host.exp.exponent:expo-constants"/>
    <dependency
        name=":@@:expo-image-manipulator::release"
        simpleName="host.exp.exponent:expo-image-manipulator"/>
    <dependency
        name=":@@:expo-dev-client::release"
        simpleName="host.exp.exponent:expo-dev-client"/>
    <dependency
        name="androidx.databinding:viewbinding:8.8.2@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.facebook.fresco:ui-core:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-core"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name=":@@:react-native-async-storage_async-storage::release"
        simpleName="SwipeSense:react-native-async-storage_async-storage"/>
    <dependency
        name=":@@:react-native-picker_picker::release"
        simpleName="SwipeSense:react-native-picker_picker"/>
    <dependency
        name=":@@:lottie-react-native::release"
        simpleName="SwipeSense:lottie-react-native"/>
    <dependency
        name=":@@:react-native-fast-image::release"
        simpleName="SwipeSense:react-native-fast-image"/>
    <dependency
        name=":@@:react-native-gesture-handler::release"
        simpleName="SwipeSense:react-native-gesture-handler"/>
    <dependency
        name=":@@:react-native-razorpay::release"
        simpleName="SwipeSense:react-native-razorpay"/>
    <dependency
        name=":@@:react-native-reanimated::release"
        simpleName="SwipeSense:react-native-reanimated"/>
    <dependency
        name=":@@:react-native-safe-area-context::release"
        simpleName="SwipeSense:react-native-safe-area-context"/>
    <dependency
        name="com.facebook.fresco:animated-gif:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-gif"/>
    <dependency
        name="com.facebook.fresco:webpsupport:3.6.0@aar"
        simpleName="com.facebook.fresco:webpsupport"/>
    <dependency
        name="com.facebook.react:hermes-android:0.79.2:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.7.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="commons-io:commons-io:2.6@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="commons-codec:commons-codec:1.10@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.7@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="host.exp.exponent:expo.modules.application:6.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.application"/>
    <dependency
        name="expo.modules.asset:expo.modules.asset:11.1.5@aar"
        simpleName="expo.modules.asset:expo.modules.asset"/>
    <dependency
        name="host.exp.exponent:expo.modules.crypto:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.crypto"/>
    <dependency
        name="host.exp.exponent:expo.modules.font:13.3.1@aar"
        simpleName="host.exp.exponent:expo.modules.font"/>
    <dependency
        name="host.exp.exponent:expo.modules.imagepicker:16.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.imagepicker"/>
    <dependency
        name="host.exp.exponent:expo.modules.keepawake:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.keepawake"/>
    <dependency
        name="host.exp.exponent:expo.modules.lineargradient:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.lineargradient"/>
    <dependency
        name=":@@:expo-dev-menu-interface::release"
        simpleName="host.exp.exponent:expo-dev-menu-interface"/>
    <dependency
        name=":@@:expo-eas-client::release"
        simpleName="host.exp.exponent:expo-eas-client"/>
    <dependency
        name=":@@:expo-json-utils::release"
        simpleName="host.exp.exponent:expo-json-utils"/>
    <dependency
        name=":@@:expo-linking::release"
        simpleName="host.exp.exponent:expo-linking"/>
    <dependency
        name=":@@:expo-manifests::release"
        simpleName="host.exp.exponent:expo-manifests"/>
    <dependency
        name=":@@:expo-notifications::release"
        simpleName="host.exp.exponent:expo-notifications"/>
    <dependency
        name=":@@:expo-structured-headers::release"
        simpleName="host.exp.exponent:expo-structured-headers"/>
    <dependency
        name=":@@:expo-updates::release"
        simpleName="host.exp.exponent:expo-updates"/>
    <dependency
        name=":@@:expo-updates-interface::release"
        simpleName="host.exp.exponent:expo-updates-interface"/>
  </compile>
  <package
      roots=":@@:react-native-google-signin_google-signin::release,:@@:lottie-react-native::release,:@@:react-native-gesture-handler::release,:@@:react-native-safe-area-context::release,:@@:react-native-screens::release,:@@:react-native-async-storage_async-storage::release,:@@:react-native-picker_picker::release,:@@:expo::release,:@@:react-native-fast-image::release,:@@:react-native-razorpay::release,:@@:react-native-reanimated::release,:@@:expo-dev-launcher::release,:@@:expo-dev-menu::release,:@@:expo-dev-menu-interface::release,:@@:expo-modules-core::release,:@@:expo-updates::release,host.exp.exponent:expo.modules.font:13.3.1@aar,com.facebook.react:react-android:0.79.2:release@aar,com.google.android.material:material:1.6.1@aar,:@@:expo-structured-headers::release,host.exp.exponent:expo.modules.imagepicker:16.1.4@aar,com.airbnb.android:lottie:6.5.2@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,com.vanniktech:android-image-cropper:4.6.0@aar,androidx.constraintlayout:constraintlayout:2.0.1@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,:@@:expo-file-system::release,androidx.legacy:legacy-support-v4:1.0.0@aar,:@@:expo-image-loader::release,com.github.bumptech.glide:okhttp3-integration:4.12.0@aar,com.github.bumptech.glide:glide:4.16.0@aar,com.razorpay:checkout:1.6.41@aar,com.razorpay:standard-core:1.6.50@aar,com.google.android.gms:play-services-auth:21.1.0@aar,com.google.android.gms:play-services-wallet:18.1.3@aar,androidx.viewpager2:viewpager2:1.0.0@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,:@@:expo-notifications::release,com.google.firebase:firebase-messaging:24.0.1@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-fido:20.0.1@aar,com.google.android.gms:play-services-identity:17.0.0@aar,com.google.android.gms:play-services-maps:17.0.0@aar,com.google.android.gms:play-services-base:18.3.0@aar,com.google.firebase:firebase-installations:17.2.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-cloud-messaging:17.2.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,androidx.activity:activity:1.10.0@aar,androidx.activity:activity-ktx:1.10.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.autofill:autofill:1.1.0@aar,com.facebook.fresco:animated-gif:3.6.0@aar,com.facebook.fresco:webpsupport:3.6.0@aar,com.facebook.fresco:fresco:3.6.0@aar,com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar,com.facebook.fresco:animated-base:3.6.0@aar,com.facebook.fresco:animated-drawable:3.6.0@aar,com.facebook.fresco:vito-options:3.6.0@aar,com.facebook.fresco:drawee:3.6.0@aar,com.facebook.fresco:nativeimagefilters:3.6.0@aar,com.facebook.fresco:memory-type-native:3.6.0@aar,com.facebook.fresco:memory-type-java:3.6.0@aar,com.facebook.fresco:imagepipeline-native:3.6.0@aar,com.facebook.fresco:memory-type-ashmem:3.6.0@aar,com.facebook.fresco:imagepipeline:3.6.0@aar,com.facebook.fresco:nativeimagetranscoder:3.6.0@aar,com.facebook.fresco:imagepipeline-base:3.6.0@aar,com.facebook.fresco:urimod:3.6.0@aar,com.facebook.fresco:vito-source:3.6.0@aar,com.facebook.fresco:middleware:3.6.0@aar,com.facebook.fresco:ui-common:3.6.0@aar,com.facebook.fresco:soloader:3.6.0@aar,com.facebook.fresco:fbcore:3.6.0@aar,host.exp.exponent:expo.modules.webbrowser:14.1.6@aar,androidx.browser:browser:1.6.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.transition:transition:1.2.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.media:media:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-service:2.6.2@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-process:2.6.2@aar,androidx.lifecycle:lifecycle-process:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-common-java8:2.6.2@jar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.6.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1@jar,com.google.android.gms:play-services-tasks:18.1.0@aar,com.google.android.gms:play-services-basement:18.3.0@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment-ktx:1.6.1@aar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.squareup.okhttp3:okhttp-brotli:4.9.2@jar,com.squareup.okhttp3:okhttp:4.9.2@jar,com.squareup.okio:okio:2.9.0@jar,:@@:expo-constants::release,:@@:expo-dev-client::release,:@@:expo-eas-client::release,:@@:expo-image-manipulator::release,:@@:expo-manifests::release,:@@:expo-json-utils::release,:@@:expo-linking::release,:@@:expo-updates-interface::release,host.exp.exponent:expo.modules.application:6.1.4@aar,expo.modules.asset:expo.modules.asset:11.1.5@aar,host.exp.exponent:expo.modules.crypto:14.1.4@aar,host.exp.exponent:expo.modules.keepawake:14.1.4@aar,host.exp.exponent:expo.modules.lineargradient:14.1.4@aar,io.insert-koin:koin-core-jvm:3.5.6@jar,co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar,co.touchlab:stately-concurrency-jvm:2.0.6@jar,co.touchlab:stately-strict-jvm:2.0.6@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar,org.jetbrains.kotlin:kotlin-reflect:2.0.21@jar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.tracing:tracing-ktx:1.2.0@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar,androidx.collection:collection-ktx:1.1.0@jar,com.facebook.fresco:ui-core:3.6.0@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,androidx.annotation:annotation-experimental:1.4.0@aar,com.facebook.fresco:vito-renderer:3.6.0@aar,com.facebook.react:hermes-android:0.79.2:release@aar,androidx.databinding:viewbinding:8.8.2@aar,androidx.exifinterface:exifinterface:1.3.7@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-datatransport:18.2.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.9@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.android.datatransport:transport-runtime:3.1.9@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,org.jetbrains:annotations:23.0.0@jar,com.facebook.yoga:proguard-annotations:1.19.0@jar,com.facebook.fbjni:fbjni:0.7.0@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.facebook.soloader:soloader:0.12.1@aar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.parse.bolts:bolts-tasks:1.4.0@jar,com.facebook.soloader:nativeloader:0.12.1@jar,commons-io:commons-io:2.6@jar,com.google.code.gson:gson:2.8.6@jar,commons-codec:commons-codec:1.10@jar,me.leolin:ShortcutBadger:1.1.22@aar,org.bouncycastle:bcutil-jdk15to18:1.78.1@jar,com.android.installreferrer:installreferrer:2.2@aar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.soloader:annotation:0.12.1@jar,com.google.guava:listenablefuture:1.0@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,com.google.errorprone:error_prone_annotations:2.26.0@jar,org.brotli:dec:0.1.2@jar,org.bouncycastle:bcprov-jdk15to18:1.78.1@jar,androidx.constraintlayout:constraintlayout-solver:2.0.1@jar">
    <dependency
        name=":@@:react-native-google-signin_google-signin::release"
        simpleName="SwipeSense:react-native-google-signin_google-signin"/>
    <dependency
        name=":@@:lottie-react-native::release"
        simpleName="SwipeSense:lottie-react-native"/>
    <dependency
        name=":@@:react-native-gesture-handler::release"
        simpleName="SwipeSense:react-native-gesture-handler"/>
    <dependency
        name=":@@:react-native-safe-area-context::release"
        simpleName="SwipeSense:react-native-safe-area-context"/>
    <dependency
        name=":@@:react-native-screens::release"
        simpleName="SwipeSense:react-native-screens"/>
    <dependency
        name=":@@:react-native-async-storage_async-storage::release"
        simpleName="SwipeSense:react-native-async-storage_async-storage"/>
    <dependency
        name=":@@:react-native-picker_picker::release"
        simpleName="SwipeSense:react-native-picker_picker"/>
    <dependency
        name=":@@:expo::release"
        simpleName="host.exp.exponent:expo"/>
    <dependency
        name=":@@:react-native-fast-image::release"
        simpleName="SwipeSense:react-native-fast-image"/>
    <dependency
        name=":@@:react-native-razorpay::release"
        simpleName="SwipeSense:react-native-razorpay"/>
    <dependency
        name=":@@:react-native-reanimated::release"
        simpleName="SwipeSense:react-native-reanimated"/>
    <dependency
        name=":@@:expo-dev-launcher::release"
        simpleName="host.exp.exponent:expo-dev-launcher"/>
    <dependency
        name=":@@:expo-dev-menu::release"
        simpleName="host.exp.exponent:expo-dev-menu"/>
    <dependency
        name=":@@:expo-dev-menu-interface::release"
        simpleName="host.exp.exponent:expo-dev-menu-interface"/>
    <dependency
        name=":@@:expo-modules-core::release"
        simpleName="host.exp.exponent:expo-modules-core"/>
    <dependency
        name=":@@:expo-updates::release"
        simpleName="host.exp.exponent:expo-updates"/>
    <dependency
        name="host.exp.exponent:expo.modules.font:13.3.1@aar"
        simpleName="host.exp.exponent:expo.modules.font"/>
    <dependency
        name="com.facebook.react:react-android:0.79.2:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="com.google.android.material:material:1.6.1@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name=":@@:expo-structured-headers::release"
        simpleName="host.exp.exponent:expo-structured-headers"/>
    <dependency
        name="host.exp.exponent:expo.modules.imagepicker:16.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.imagepicker"/>
    <dependency
        name="com.airbnb.android:lottie:6.5.2@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="com.vanniktech:android-image-cropper:4.6.0@aar"
        simpleName="com.vanniktech:android-image-cropper"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name=":@@:expo-file-system::release"
        simpleName="host.exp.exponent:expo-file-system"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name=":@@:expo-image-loader::release"
        simpleName="host.exp.exponent:expo-image-loader"/>
    <dependency
        name="com.github.bumptech.glide:okhttp3-integration:4.12.0@aar"
        simpleName="com.github.bumptech.glide:okhttp3-integration"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="com.razorpay:checkout:1.6.41@aar"
        simpleName="com.razorpay:checkout"/>
    <dependency
        name="com.razorpay:standard-core:1.6.50@aar"
        simpleName="com.razorpay:standard-core"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.1.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="com.google.android.gms:play-services-wallet:18.1.3@aar"
        simpleName="com.google.android.gms:play-services-wallet"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name=":@@:expo-notifications::release"
        simpleName="host.exp.exponent:expo-notifications"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.0.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.0.1@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-identity:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-identity"/>
    <dependency
        name="com.google.android.gms:play-services-maps:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-maps"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.firebase:firebase-installations:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="androidx.activity:activity:1.10.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="com.facebook.fresco:animated-gif:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-gif"/>
    <dependency
        name="com.facebook.fresco:webpsupport:3.6.0@aar"
        simpleName="com.facebook.fresco:webpsupport"/>
    <dependency
        name="com.facebook.fresco:fresco:3.6.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.facebook.fresco:animated-base:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-base"/>
    <dependency
        name="com.facebook.fresco:animated-drawable:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-drawable"/>
    <dependency
        name="com.facebook.fresco:vito-options:3.6.0@aar"
        simpleName="com.facebook.fresco:vito-options"/>
    <dependency
        name="com.facebook.fresco:drawee:3.6.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="com.facebook.fresco:urimod:3.6.0@aar"
        simpleName="com.facebook.fresco:urimod"/>
    <dependency
        name="com.facebook.fresco:vito-source:3.6.0@aar"
        simpleName="com.facebook.fresco:vito-source"/>
    <dependency
        name="com.facebook.fresco:middleware:3.6.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.facebook.fresco:soloader:3.6.0@aar"
        simpleName="com.facebook.fresco:soloader"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.6.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="host.exp.exponent:expo.modules.webbrowser:14.1.6@aar"
        simpleName="host.exp.exponent:expo.modules.webbrowser"/>
    <dependency
        name="androidx.browser:browser:1.6.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.media:media:1.0.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.6.2@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.1@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-brotli:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-brotli"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:2.9.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name=":@@:expo-constants::release"
        simpleName="host.exp.exponent:expo-constants"/>
    <dependency
        name=":@@:expo-dev-client::release"
        simpleName="host.exp.exponent:expo-dev-client"/>
    <dependency
        name=":@@:expo-eas-client::release"
        simpleName="host.exp.exponent:expo-eas-client"/>
    <dependency
        name=":@@:expo-image-manipulator::release"
        simpleName="host.exp.exponent:expo-image-manipulator"/>
    <dependency
        name=":@@:expo-manifests::release"
        simpleName="host.exp.exponent:expo-manifests"/>
    <dependency
        name=":@@:expo-json-utils::release"
        simpleName="host.exp.exponent:expo-json-utils"/>
    <dependency
        name=":@@:expo-linking::release"
        simpleName="host.exp.exponent:expo-linking"/>
    <dependency
        name=":@@:expo-updates-interface::release"
        simpleName="host.exp.exponent:expo-updates-interface"/>
    <dependency
        name="host.exp.exponent:expo.modules.application:6.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.application"/>
    <dependency
        name="expo.modules.asset:expo.modules.asset:11.1.5@aar"
        simpleName="expo.modules.asset:expo.modules.asset"/>
    <dependency
        name="host.exp.exponent:expo.modules.crypto:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.crypto"/>
    <dependency
        name="host.exp.exponent:expo.modules.keepawake:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.keepawake"/>
    <dependency
        name="host.exp.exponent:expo.modules.lineargradient:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.lineargradient"/>
    <dependency
        name="io.insert-koin:koin-core-jvm:3.5.6@jar"
        simpleName="io.insert-koin:koin-core-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrent-collections-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrency-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrency-jvm"/>
    <dependency
        name="co.touchlab:stately-strict-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-strict-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-reflect:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-reflect"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="com.facebook.fresco:ui-core:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-core"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.facebook.fresco:vito-renderer:3.6.0@aar"
        simpleName="com.facebook.fresco:vito-renderer"/>
    <dependency
        name="com.facebook.react:hermes-android:0.79.2:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="androidx.databinding:viewbinding:8.8.2@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.7@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.2.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.7.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.parse.bolts:bolts-tasks:1.4.0@jar"
        simpleName="com.parse.bolts:bolts-tasks"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="commons-io:commons-io:2.6@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.google.code.gson:gson:2.8.6@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="commons-codec:commons-codec:1.10@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="me.leolin:ShortcutBadger:1.1.22@aar"
        simpleName="me.leolin:ShortcutBadger"/>
    <dependency
        name="org.bouncycastle:bcutil-jdk15to18:1.78.1@jar"
        simpleName="org.bouncycastle:bcutil-jdk15to18"/>
    <dependency
        name="com.android.installreferrer:installreferrer:2.2@aar"
        simpleName="com.android.installreferrer:installreferrer"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="org.brotli:dec:0.1.2@jar"
        simpleName="org.brotli:dec"/>
    <dependency
        name="org.bouncycastle:bcprov-jdk15to18:1.78.1@jar"
        simpleName="org.bouncycastle:bcprov-jdk15to18"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-solver"/>
  </package>
</dependencies>
