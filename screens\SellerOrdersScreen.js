import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  SafeAreaView,
  Image,
  Alert,
  Modal,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { collection, query, where, orderBy, getDocs, doc, getDoc } from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { updateOrderStatus as updateOrderStatusUtil, markOrderAsShipped, markOrderAsDelivered, cancelOrder } from '../utils/orderUtils';
import { useFocusEffect } from '@react-navigation/native';

const SellerOrdersScreen = ({ navigation, route }) => {
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState([]);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [trackingNumber, setTrackingNumber] = useState('');
  const [deliveryService, setDeliveryService] = useState('');
  const [newStatus, setNewStatus] = useState('');
  const [validationError, setValidationError] = useState('');
  const [updatingStatus, setUpdatingStatus] = useState(false);

  // Track if we need to open the edit modal for a specific order
  const editOrderId = useRef(route.params?.editOrder || null);

  // Ref for tracking number input
  const trackingInputRef = useRef(null);

  const currentUserId = auth.currentUser?.uid;

  useEffect(() => {
    if (currentUserId) {
      fetchSellerOrders();
    } else {
      setLoading(false);
    }
  }, [currentUserId]);

  // Handle navigation params when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      const params = route.params;

      // Check if we need to edit a specific order
      if (params?.editOrder) {
        console.log('Opening edit modal for order:', params.editOrder);
        editOrderId.current = params.editOrder;

        // Find the order in our list
        const orderToEdit = orders.find(order => order.id === params.editOrder);
        if (orderToEdit) {
          // Open the edit modal for this order
          setSelectedOrder(orderToEdit);
          setNewStatus(orderToEdit.orderStatus || 'processing');
          setTrackingNumber(orderToEdit.trackingNumber || '');
          setDeliveryService(orderToEdit.deliveryService || '');
          setValidationError('');
          setStatusModalVisible(true);

          // Clear the parameter to prevent reopening on subsequent focuses
          navigation.setParams({ editOrder: undefined });
        } else {
          // If we can't find the order, refresh the list and try again
          fetchSellerOrders().then(() => {
            const refreshedOrder = orders.find(order => order.id === params.editOrder);
            if (refreshedOrder) {
              setSelectedOrder(refreshedOrder);
              setNewStatus(refreshedOrder.orderStatus || 'processing');
              setTrackingNumber(refreshedOrder.trackingNumber || '');
              setDeliveryService(refreshedOrder.deliveryService || '');
              setValidationError('');
              setStatusModalVisible(true);
            }
            // Clear the parameter
            navigation.setParams({ editOrder: undefined });
          });
        }
      }
    }, [route.params, orders])
  );

  const fetchSellerOrders = async () => {
    setLoading(true);
    try {
      // First, get all items uploaded by this seller
      const itemsRef = collection(db, 'clothingItems');
      const itemsQuery = query(itemsRef, where('uploaderId', '==', currentUserId));
      const itemsSnapshot = await getDocs(itemsQuery);

      const sellerItemIds = itemsSnapshot.docs.map(doc => doc.id);

      if (sellerItemIds.length === 0) {
        setOrders([]);
        setLoading(false);
        return;
      }

      // Now, get all orders that contain these items
      const ordersRef = collection(db, 'orders');
      const ordersSnapshot = await getDocs(ordersRef);

      const sellerOrders = [];

      // Filter orders that contain seller's items
      for (const orderDoc of ordersSnapshot.docs) {
        const orderData = orderDoc.data();
        const orderItems = orderData.items || [];

        // Check if any of the order items belong to this seller
        const sellerItems = orderItems.filter(item =>
          sellerItemIds.includes(item.itemId)
        );

        if (sellerItems.length > 0) {
          // Calculate total amount for seller's items
          const sellerTotal = sellerItems.reduce((total, item) => {
            return total + (item.price || 0) * (item.quantity || 1);
          }, 0);

          // Get buyer info
          let buyerInfo = { name: 'Customer' };
          if (orderData.userId) {
            const buyerDoc = await getDoc(doc(db, 'users', orderData.userId));
            if (buyerDoc.exists()) {
              buyerInfo = {
                id: buyerDoc.id,
                name: buyerDoc.data().name || 'Customer',
                email: buyerDoc.data().email || '',
                ...buyerDoc.data()
              };
            }
          }

          sellerOrders.push({
            id: orderDoc.id,
            ...orderData,
            items: sellerItems, // Only include seller's items
            totalAmount: sellerTotal, // Only seller's portion
            buyerInfo,
            createdAt: orderData.createdAt?.toDate() || new Date()
          });
        }
      }

      // Sort by date (newest first)
      sellerOrders.sort((a, b) => b.createdAt - a.createdAt);

      setOrders(sellerOrders);
    } catch (error) {
      console.error('Error fetching seller orders:', error);
      Alert.alert('Error', 'Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date) => {
    if (!date) return 'N/A';

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'processing':
        return '#FF9800';
      case 'shipped':
        return '#2196F3';
      case 'delivered':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  // Rename the function to avoid naming conflict with the imported function
  const handleOrderStatusUpdate = async () => {
    if (!selectedOrder || !newStatus) {
      setStatusModalVisible(false);
      return;
    }

    // Validate required fields for shipped status
    if (newStatus === 'shipped') {
      setValidationError('');

      if (!deliveryService.trim()) {
        setValidationError('Delivery service is required');
        return;
      }

      if (!trackingNumber.trim()) {
        setValidationError('Tracking number is required');
        return;
      }
    }

    // Show loading indicator
    setUpdatingStatus(true);

    try {
      console.log(`Attempting to update order ${selectedOrder.id} to status: ${newStatus}`);

      // Use the appropriate utility function based on the status
      switch (newStatus) {
        case 'shipped':
          console.log(`Using markOrderAsShipped for order ${selectedOrder.id}`);
          await markOrderAsShipped(selectedOrder.id, deliveryService.trim(), trackingNumber.trim());
          break;
        case 'delivered':
          console.log(`Using markOrderAsDelivered for order ${selectedOrder.id}`);
          await markOrderAsDelivered(selectedOrder.id);
          break;
        case 'cancelled':
          console.log(`Using cancelOrder for order ${selectedOrder.id}`);
          await cancelOrder(selectedOrder.id, 'Cancelled by seller');
          break;
        default:
          console.log(`Using updateOrderStatus for order ${selectedOrder.id} with status: ${newStatus}`);
          // For other statuses like 'processing', use the updateOrderStatus utility function
          const additionalData = {};

          // If we have tracking info from a previous update, make sure to include it
          if (selectedOrder.trackingNumber || selectedOrder.trackingId) {
            additionalData.trackingNumber = selectedOrder.trackingNumber || selectedOrder.trackingId;
            additionalData.trackingId = selectedOrder.trackingNumber || selectedOrder.trackingId;
          }

          if (selectedOrder.deliveryService) {
            additionalData.deliveryService = selectedOrder.deliveryService;
          }

          // Use the imported updateOrderStatus utility function
          console.log(`Order ID: ${selectedOrder.id}`);
          console.log(`New status: ${newStatus}`);
          console.log(`Additional data:`, additionalData);

          try {
            await updateOrderStatusUtil(selectedOrder.id, newStatus, additionalData);
          } catch (error) {
            console.error(`Detailed error when updating order status:`, error);
            console.error(`Error name: ${error.name}`);
            console.error(`Error message: ${error.message}`);
            console.error(`Error stack: ${error.stack}`);
            throw error; // Re-throw to be caught by the outer catch block
          }
      }

      // If we get here, the update was successful
      console.log(`Successfully updated order ${selectedOrder.id} to status: ${newStatus}`);

      // Update local state
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === selectedOrder.id
            ? {
                ...order,
                orderStatus: newStatus,
                trackingNumber: newStatus === 'shipped' ? trackingNumber : order.trackingNumber,
                deliveryService: newStatus === 'shipped' ? deliveryService : order.deliveryService,
                shippedAt: newStatus === 'shipped' ? new Date() : order.shippedAt
              }
            : order
        )
      );

      Alert.alert('Success', `Order status updated to ${newStatus}`);

      // Close the modal and reset state
      setStatusModalVisible(false);
      setSelectedOrder(null);
      setNewStatus('');
      setTrackingNumber('');
      setDeliveryService('');
      setValidationError('');
    } catch (error) {
      console.error(`Error updating order ${selectedOrder?.id} status to ${newStatus}:`, error);

      // Show a more specific error message if available
      const errorMessage = error.message || 'Failed to update order status';
      Alert.alert('Error', errorMessage);
    } finally {
      setUpdatingStatus(false);
    }
  };

  const renderOrderItem = ({ item }) => {
    const statusColor = getStatusColor(item.orderStatus);

    // Get the first item image to display as the order thumbnail
    const thumbnailImage = item.items && item.items.length > 0
      ? item.items[0].imageUrl
      : null;

    // Calculate total items
    const totalItems = item.items ? item.items.length : 0;

    return (
      <View style={styles.orderItem}>
        <View style={styles.orderHeader}>
          <View style={styles.orderIdContainer}>
            <Text style={styles.orderIdLabel}>Order ID:</Text>
            <Text style={styles.orderId}>{item.id.substring(0, 8)}...</Text>
          </View>
          <Text style={styles.orderDate}>{formatDate(item.createdAt)}</Text>
        </View>

        <View style={styles.orderContent}>
          {thumbnailImage ? (
            <Image
              source={{ uri: thumbnailImage }}
              style={styles.orderImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.placeholderImage}>
              <Ionicons name="image-outline" size={30} color="#ccc" />
            </View>
          )}

          <View style={styles.orderDetails}>
            <Text style={styles.buyerName}>
              Buyer: {item.buyerInfo?.name || 'Customer'}
            </Text>
            <Text style={styles.orderItemsCount}>
              {totalItems} {totalItems === 1 ? 'item' : 'items'}
            </Text>
            <Text style={styles.orderTotal}>
              Total: ₹{(item.totalAmount || 0).toFixed(2)}
            </Text>
            <View style={styles.statusContainer}>
              <Text style={[styles.orderStatus, { color: statusColor }]}>
                {item.orderStatus || 'Processing'}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.orderActions}>
          <TouchableOpacity
            style={styles.viewButton}
            onPress={() => navigation.navigate('SellerOrderDetails', { orderId: item.id })}
          >
            <Text style={styles.viewButtonText}>View Details</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.updateButton}
            onPress={() => {
              setSelectedOrder(item);
              setNewStatus(item.orderStatus || 'processing');
              setTrackingNumber(item.trackingNumber || '');
              setDeliveryService(item.deliveryService || '');
              setValidationError('');
              setStatusModalVisible(true);
            }}
          >
            <Text style={styles.updateButtonText}>Update Status</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderEmptyOrders = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="receipt-outline" size={80} color="#ccc" />
      <Text style={styles.emptyText}>No orders found</Text>
      <Text style={styles.emptySubText}>
        Orders for your products will appear here
      </Text>
    </View>
  );

  const renderStatusModal = () => (
    <Modal
      visible={statusModalVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setStatusModalVisible(false)}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={Keyboard.dismiss}
        >
          <View style={styles.modalContainer}>
            <ScrollView
              contentContainerStyle={styles.scrollViewContent}
              keyboardShouldPersistTaps="handled"
            >
              <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>Update Order Status</Text>
                  <TouchableOpacity
                    onPress={() => setStatusModalVisible(false)}
                    style={styles.closeButton}
                  >
                    <Ionicons name="close" size={24} color="#333" />
                  </TouchableOpacity>
                </View>

                <Text style={styles.modalLabel}>Order Status</Text>
                <View style={styles.statusOptions}>
                  {['processing', 'shipped', 'delivered', 'cancelled'].map(status => (
                    <TouchableOpacity
                      key={status}
                      style={[
                        styles.statusOption,
                        newStatus === status && { backgroundColor: getStatusColor(status) }
                      ]}
                      onPress={() => setNewStatus(status)}
                    >
                      <Text style={[
                        styles.statusOptionText,
                        newStatus === status && { color: '#fff' }
                      ]}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>

                {newStatus === 'shipped' && (
                  <>
                    <Text style={styles.modalLabel}>Delivery Service <Text style={styles.requiredStar}>*</Text></Text>
                    <TextInput
                      style={[styles.input, !deliveryService.trim() && validationError.includes('Delivery service') ? styles.inputError : null]}
                      value={deliveryService}
                      onChangeText={setDeliveryService}
                      placeholder="Enter delivery service (e.g., FedEx, DHL)"
                      returnKeyType="next"
                      onSubmitEditing={() => {
                        // Focus the tracking number input when done
                        if (trackingInputRef.current) {
                          trackingInputRef.current.focus();
                        }
                      }}
                    />

                    <Text style={styles.modalLabel}>Tracking Number <Text style={styles.requiredStar}>*</Text></Text>
                    <TextInput
                      ref={trackingInputRef}
                      style={[styles.input, !trackingNumber.trim() && validationError.includes('Tracking number') ? styles.inputError : null]}
                      value={trackingNumber}
                      onChangeText={setTrackingNumber}
                      placeholder="Enter tracking number"
                      returnKeyType="done"
                      onSubmitEditing={Keyboard.dismiss}
                    />

                    <Text style={styles.helperText}>
                      Both fields are required when marking an order as shipped
                    </Text>
                  </>
                )}

                {validationError ? (
                  <Text style={styles.errorText}>{validationError}</Text>
                ) : null}

                {updatingStatus ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#2196F3" />
                    <Text style={styles.loadingText}>Updating order status...</Text>
                  </View>
                ) : (
                  <TouchableOpacity
                    style={styles.updateStatusButton}
                    onPress={handleOrderStatusUpdate}
                  >
                    <Text style={styles.updateStatusButtonText}>Update Status</Text>
                  </TouchableOpacity>
                )}
              </View>
            </ScrollView>
          </View>
        </TouchableOpacity>
      </KeyboardAvoidingView>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Manage Orders</Text>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={fetchSellerOrders}
          >
            <Ionicons name="refresh" size={24} color="#333" />
          </TouchableOpacity>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B6B" />
            <Text style={styles.loadingText}>Loading orders...</Text>
          </View>
        ) : (
          <FlatList
            data={orders}
            renderItem={renderOrderItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={renderEmptyOrders}
            showsVerticalScrollIndicator={false}
          />
        )}

        {renderStatusModal()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
    fontSize: 16,
  },
  listContent: {
    padding: 15,
    flexGrow: 1,
  },
  orderItem: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    overflow: 'hidden',
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#f9f9f9',
  },
  orderIdContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  orderIdLabel: {
    fontSize: 12,
    color: '#666',
    marginRight: 5,
  },
  orderId: {
    fontSize: 12,
    color: '#333',
    fontWeight: '500',
  },
  orderDate: {
    fontSize: 12,
    color: '#666',
  },
  orderContent: {
    flexDirection: 'row',
    padding: 10,
  },
  orderImage: {
    width: 80,
    height: 80,
    borderRadius: 5,
  },
  placeholderImage: {
    width: 80,
    height: 80,
    borderRadius: 5,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  orderDetails: {
    flex: 1,
    marginLeft: 15,
    justifyContent: 'center',
  },
  buyerName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  orderItemsCount: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  orderTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  orderStatus: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  orderActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  viewButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderRightWidth: 1,
    borderRightColor: '#eee',
  },
  viewButtonText: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  updateButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  updateButtonText: {
    color: '#FF6B6B',
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 18,
    color: '#999',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    paddingHorizontal: 30,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '100%',
    maxHeight: '90%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: Platform.OS === 'ios' ? 30 : 20,
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  modalContent: {
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  modalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  statusOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  statusOption: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 10,
    marginBottom: 10,
  },
  statusOptionText: {
    color: '#333',
    fontWeight: '500',
  },
  input: {
    backgroundColor: '#f8f8f8',
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
    minHeight: 50,
  },
  updateStatusButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  updateStatusButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  requiredStar: {
    color: '#FF6B6B',
    fontWeight: 'bold',
  },
  errorText: {
    color: '#FF6B6B',
    marginBottom: 15,
    fontSize: 14,
  },
  helperText: {
    color: '#666',
    fontSize: 12,
    marginBottom: 15,
    fontStyle: 'italic',
  },
  inputError: {
    borderColor: '#FF6B6B',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 15,
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
    fontSize: 14,
  },
});

export default SellerOrdersScreen;