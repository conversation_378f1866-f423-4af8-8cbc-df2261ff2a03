# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at D:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp"
  "D:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp"
  "D:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp"
  "D:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp"
  "D:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp"
  "D:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/RNGoogleSignInCGen-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp"
  "D:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp"
  "D:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/Props.cpp"
  "D:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp"
  "D:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp"
  "D:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt:20 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerMeasurementsManager.cpp"
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerShadowNode.cpp"
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerState.cpp"
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerMeasurementsManager.cpp"
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerShadowNode.cpp"
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerState.cpp"
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/rnpicker.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp"
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp"
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp"
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp"
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp"
  "D:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/lottiereactnative-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp"
  "D:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/EventEmitters.cpp"
  "D:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/Props.cpp"
  "D:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/ShadowNodes.cpp"
  "D:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/States.cpp"
  "D:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp"
  "D:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp"
  "D:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp"
  "D:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp"
  "D:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp"
  "D:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp"
  "D:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp"
  "D:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp"
  "D:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp"
  "D:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp"
  "D:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"
  "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/safeareacontext-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"
  "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"
  "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"
  "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"
  "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"
  "D:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/utils/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:24 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"
  "D:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at D:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:42 (file)
# input_SRC at D:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "D:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/app/StyleApp/android/app/.cxx/Debug/4h302r3f/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()
