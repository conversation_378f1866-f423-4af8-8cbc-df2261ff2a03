import { useRef, useCallback, useMemo } from 'react';
import { Platform, Dimensions } from 'react-native';
import {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  runOnJS,
  withTiming
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const ROTATION_ANGLE = 12;
const UP_SWIPE_THRESHOLD = -15;
const SWIPE_SENSITIVITY = 0.005;

export const useSwipeGestures = ({
  items,
  currentIndex,
  onSwipeComplete,
  onResetAnimations,
  triggerLikeAnimation,
  triggerDislikeAnimation
}) => {
  // Animation values
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotate = useSharedValue(0);

  // Card animations for Tinder-like stack effect
  const cardAnimations = useRef(
    Array(3).fill(0).map((_, index) => ({
      scale: useSharedValue(1 - (index + 1) * 0.03),
      translateY: useSharedValue((index + 1) * 15),
      opacity: useSharedValue(1),
    }))
  ).current;

  // Reset card animations - animate cards to move forward after swipe
  const resetCardAnimations = useCallback(() => {
    try {
      // Faster timing for Tinder-like snappy animations
      const consistentTimingConfig = { duration: 150 }; // Reduced duration for snappier transition

      translateX.value = withTiming(0, consistentTimingConfig);
      translateY.value = withTiming(0, consistentTimingConfig);
      rotate.value = withTiming(0, consistentTimingConfig);

      // Animate cards to move forward from behind (Tinder-like effect)
      for (let i = 0; i < cardAnimations.length; i++) {
        cardAnimations[i].scale.value = withTiming(1 - i * 0.03, consistentTimingConfig);
        cardAnimations[i].translateY.value = withTiming(i * 15, consistentTimingConfig);
        cardAnimations[i].opacity.value = withTiming(1, consistentTimingConfig);
      }
    } catch (error) {
      console.error("[useSwipeGestures] Error in resetCardAnimations:", error);
      translateX.value = 0;
      translateY.value = 0;
      rotate.value = 0;
    }
  }, [translateX, translateY, rotate, cardAnimations]);

  // Process swipe completion
  const processSwipeCompletion = useCallback((direction, swipedItem) => {
    // Trigger animations based on swipe direction
    // Removed:
    // if (direction === 'right' && triggerLikeAnimation) {
    //   triggerLikeAnimation();
    // } else if (direction === 'left' && triggerDislikeAnimation) {
    //   triggerDislikeAnimation();
    // }
    // Note: Cart animation (direction === 'up') is handled by addToCart in useCartManager

    if (onSwipeComplete) {
      onSwipeComplete(direction, swipedItem);
    }

    // Reset animations for the next card after a short delay
    setTimeout(() => {
      resetCardAnimations();
    }, 50);
  }, [onSwipeComplete, resetCardAnimations]);

  // Create pan gesture - recreate when currentIndex or items change
  const panGesture = useMemo(() => Gesture.Pan()
    .onStart(() => {
      'worklet';
      const startX = translateX.value;
      const startY = translateY.value;

      translateX.value = startX;
      translateY.value = startY;

      if (Platform.OS === 'android') {
        rotate.value = 0;
      }
    })
    .enabled(currentIndex < items.length)
    .minDistance(Platform.OS === 'android' ? 5 : 0)
    .onUpdate((event) => {
      'worklet';
      const { translationX, translationY } = event;

      // Determine primary direction
      let direction;
      const directionThreshold = Platform.OS === 'android' ? 1.2 : 1.0;

      if (Math.abs(translationX) > Math.abs(translationY) * directionThreshold) {
        direction = 'horizontal';
      } else if (translationY < 0) {
        direction = 'up';
      } else {
        direction = 'down';
      }

      // Handle movement based on direction
      if (direction === 'horizontal') {
        translateX.value = translationX;
        translateY.value = 0;

        // Calculate rotation manually to avoid interpolate warnings
        const rotationProgress = Math.max(-1, Math.min(1, translationX / (SCREEN_WIDTH / 2)));
        rotate.value = rotationProgress * ROTATION_ANGLE;
      } else if (direction === 'up') {
        translateY.value = Math.min(0, translationY);
        translateX.value = 0;
      } else {
        translateY.value = 0;
        translateX.value = 0;
        return;
      }

      // Remove real-time card animation during swipe for Tinder-like effect
      // Cards should remain static behind the top card and only become visible when top card is swiped away
    })
    .onEnd((event) => {
      'worklet';
      try {
        if (currentIndex < 0 || currentIndex >= items.length) {
          console.warn(`[useSwipeGestures] Invalid currentIndex (${currentIndex}) in onEnd. Resetting.`);
          runOnJS(resetCardAnimations)();
          return;
        }

        const { translationX, translationY } = event;
        const swipedItem = items[currentIndex];

        if (!swipedItem) {
          console.warn(`[useSwipeGestures] swipedItem is null/undefined in onEnd for index ${currentIndex}. Resetting.`);
          runOnJS(resetCardAnimations)();
          return;
        }

        // Determine primary direction
        let direction;
        if (Math.abs(translationX) > Math.abs(translationY)) {
          direction = 'horizontal';
        } else if (translationY < 0) {
          direction = 'up';
        } else {
          direction = 'down';
        }        // Handle upward swipe
        if (direction === 'up' && translationY < UP_SWIPE_THRESHOLD) {
          console.log('[useSwipeGestures] Upward swipe detected');

          // Note: Cart animation will be triggered by addToCart in useCartManager
          // This prevents double animation triggers

          const upSwipeDuration = Platform.OS === 'android' ? 50 : 50;
          const targetY = -SCREEN_HEIGHT * 1.5;

          translateY.value = withTiming(targetY, { duration: upSwipeDuration }, (isFinished) => {
            if (isFinished) {
              runOnJS(processSwipeCompletion)('up', swipedItem);
            } else {
              runOnJS(resetCardAnimations)();
            }
          });

          translateX.value = withTiming(0, { duration: Platform.OS === 'android' ? upSwipeDuration / 2 : upSwipeDuration });

          if (Platform.OS === 'android') {
            rotate.value = withTiming(0, { duration: upSwipeDuration / 2 });
          }

          return;
        }

        // Handle horizontal swipe
        if (direction === 'horizontal') {
          const sensitiveThreshold = SCREEN_WIDTH * SWIPE_SENSITIVITY;
          if (Math.abs(translationX) > sensitiveThreshold) {
            const swipeDirection = translationX > 0 ? 'right' : 'left';
            const targetX = (swipeDirection === 'right' ? 1 : -1) * SCREEN_WIDTH * 1.5; const swipeDuration = Platform.OS === 'android' ? 50 : 50;
            const rotationDuration = Platform.OS === 'android' ? 50 : 50;
            const verticalDuration = Platform.OS === 'android' ? 50 : 50;

            translateX.value = withTiming(targetX, { duration: swipeDuration }, (isFinished) => {
              if (isFinished) {
                runOnJS(processSwipeCompletion)(swipeDirection, swipedItem);
              } else {
                runOnJS(resetCardAnimations)();
              }
            });

            rotate.value = withTiming((swipeDirection === 'right' ? 1 : -1) * 30, { duration: rotationDuration });
            translateY.value = withTiming(0, { duration: verticalDuration });
            return;
          }
        }

        // No significant swipe detected
        console.log('[useSwipeGestures] No significant swipe detected. Resetting.');
        runOnJS(resetCardAnimations)();

      } catch (err) {
        console.error("[useSwipeGestures] Error in swipe onEnd:", err);
        runOnJS(resetCardAnimations)();
      }
    }), [currentIndex, items.length, processSwipeCompletion, resetCardAnimations]);

  // Animated style for top card
  const topCardStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotate.value}deg` },
    ],
  }));

  return {
    panGesture,
    topCardStyle,
    resetCardAnimations,
    cardAnimations
  };
};
