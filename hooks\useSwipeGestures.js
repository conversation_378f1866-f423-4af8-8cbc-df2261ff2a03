import { useRef, useCallback, useMemo } from 'react';
import { Platform, Dimensions } from 'react-native';
import {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withTiming,
  withSpring,
  interpolate
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// TINDER-STYLE CONFIGURATION
const SWIPE_CONFIG = {
  // Gesture thresholds
  SWIPE_THRESHOLD: SCREEN_WIDTH * 0.3, // 30% of screen width
  VELOCITY_THRESHOLD: 800, // Minimum velocity for auto-complete
  ROTATION_FACTOR: 0.1, // Rotation intensity
  MAX_ROTATION: 15, // Maximum rotation degrees

  // Animation timing
  SPRING_CONFIG: {
    damping: 20,
    stiffness: 300,
    mass: 1,
  },

  // Visual feedback
  SCALE_FACTOR: 0.95, // Card scales down slightly during drag
  OVERLAY_OPACITY: 0.7, // Color overlay opacity

  // Stack behavior
  STACK_SCALE_FACTOR: 0.05, // How much cards behind scale
  STACK_OFFSET: 20, // Vertical offset between cards
};

export const useSwipeGestures = ({
  items,
  currentIndex,
  onSwipeComplete,
  onResetAnimations,
  triggerLikeAnimation,
  triggerDislikeAnimation
}) => {
  // TINDER-STYLE ANIMATION VALUES
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotate = useSharedValue(0);
  const scale = useSharedValue(1);
  const overlayOpacity = useSharedValue(0);

  // Stack animation values for cards behind
  const stackAnimations = useRef(
    Array(2).fill(0).map((_, index) => ({
      scale: useSharedValue(1 - (index + 1) * SWIPE_CONFIG.STACK_SCALE_FACTOR),
      translateY: useSharedValue((index + 1) * SWIPE_CONFIG.STACK_OFFSET),
      opacity: useSharedValue(1),
    }))
  ).current;

  // Gesture state tracking
  const isSwipeInProgress = useSharedValue(false);
  const swipeDirection = useSharedValue(null);

  // TINDER-STYLE: Animate stack forward (no reset to center)
  const animateStackForward = useCallback(() => {
    try {
      // Animate cards behind to move forward
      if (stackAnimations && stackAnimations.length > 0) {
        stackAnimations.forEach((animation, index) => {
          if (animation && animation.scale && animation.translateY) {
            animation.scale.value = withSpring(
              1 - index * SWIPE_CONFIG.STACK_SCALE_FACTOR,
              SWIPE_CONFIG.SPRING_CONFIG
            );
            animation.translateY.value = withSpring(
              index * SWIPE_CONFIG.STACK_OFFSET,
              SWIPE_CONFIG.SPRING_CONFIG
            );
          }
        });
      }
    } catch (error) {
      console.error("[TinderSwipe] Error in animateStackForward:", error);
    }
  }, [stackAnimations]);

  // TINDER-STYLE: Complete swipe animation (card exits screen)
  const completeSwipe = useCallback((direction, velocity = 0) => {
    'worklet';
    try {
      const exitX = direction === 'right'
        ? SCREEN_WIDTH * 1.5
        : -SCREEN_WIDTH * 1.5;

      const exitRotation = direction === 'right'
        ? SWIPE_CONFIG.MAX_ROTATION
        : -SWIPE_CONFIG.MAX_ROTATION;

      // Animate card off screen
      translateX.value = withSpring(exitX, {
        ...SWIPE_CONFIG.SPRING_CONFIG,
        velocity: Math.max(-2000, Math.min(2000, velocity * 0.5)), // Clamp velocity
      });

      rotate.value = withSpring(exitRotation, SWIPE_CONFIG.SPRING_CONFIG);
      scale.value = withSpring(0.8, SWIPE_CONFIG.SPRING_CONFIG);
      overlayOpacity.value = withTiming(0, { duration: 200 });

      // Safely animate stack forward
      if (stackAnimations && stackAnimations.length > 0) {
        runOnJS(animateStackForward)();
      }

      // Complete the swipe after animation - use runOnJS for setTimeout
      runOnJS(() => {
        setTimeout(() => {
          if (items && items[currentIndex]) {
            processSwipeCompletion(direction, items[currentIndex]);
          }
        }, 300);
      })();
    } catch (error) {
      console.error('[TinderSwipe] Error in completeSwipe:', error);
      runOnJS(resetCardPosition)();
    }
  }, [translateX, rotate, scale, overlayOpacity, animateStackForward, items, currentIndex, processSwipeCompletion, resetCardPosition]);

  // TINDER-STYLE: Reset card to initial position (for incomplete swipes)
  const resetCardPosition = useCallback(() => {
    try {
      translateX.value = withSpring(0, SWIPE_CONFIG.SPRING_CONFIG);
      translateY.value = withSpring(0, SWIPE_CONFIG.SPRING_CONFIG);
      rotate.value = withSpring(0, SWIPE_CONFIG.SPRING_CONFIG);
      scale.value = withSpring(1, SWIPE_CONFIG.SPRING_CONFIG);
      overlayOpacity.value = withTiming(0, { duration: 200 });
      isSwipeInProgress.value = false;
      swipeDirection.value = null;
    } catch (error) {
      console.error('[TinderSwipe] Error in resetCardPosition:', error);
    }
  }, [translateX, translateY, rotate, scale, overlayOpacity, isSwipeInProgress, swipeDirection]);

  // Process swipe completion (TINDER-STYLE: No reset, prepare next card)
  const processSwipeCompletion = useCallback((direction, swipedItem) => {
    try {
      console.log(`[TinderSwipe] Card swiped ${direction}`);

      // Trigger visual feedback animations safely
      if (direction === 'right' && triggerLikeAnimation && typeof triggerLikeAnimation === 'function') {
        triggerLikeAnimation();
      } else if (direction === 'left' && triggerDislikeAnimation && typeof triggerDislikeAnimation === 'function') {
        triggerDislikeAnimation();
      }

      // Call parent completion handler safely
      if (onSwipeComplete && typeof onSwipeComplete === 'function') {
        onSwipeComplete(direction, swipedItem);
      }

      // TINDER-STYLE: Prepare next card (no reset to center)
      setTimeout(() => {
        try {
          // Reset values for next card
          translateX.value = 0;
          translateY.value = 0;
          rotate.value = 0;
          scale.value = 1;
          overlayOpacity.value = 0;
          isSwipeInProgress.value = false;
          swipeDirection.value = null;
        } catch (resetError) {
          console.error('[TinderSwipe] Error resetting values:', resetError);
        }
      }, 100);
    } catch (error) {
      console.error('[TinderSwipe] Error in processSwipeCompletion:', error);
    }
  }, [onSwipeComplete, triggerLikeAnimation, triggerDislikeAnimation,
      translateX, translateY, rotate, scale, overlayOpacity, isSwipeInProgress, swipeDirection]);

  // TINDER-STYLE GESTURE SYSTEM
  const panGesture = useMemo(() => Gesture.Pan()
    .onStart(() => {
      'worklet';
      try {
        console.log('[TinderSwipe] Gesture started');
        isSwipeInProgress.value = true;

        // Slight scale down for visual feedback
        scale.value = withSpring(SWIPE_CONFIG.SCALE_FACTOR, {
          damping: 30,
          stiffness: 400,
        });
      } catch (error) {
        console.error('[TinderSwipe] Error in onStart:', error);
      }
    })
    .enabled(currentIndex < items.length)
    .onUpdate((event) => {
      'worklet';
      try {
        if (!event) return;

        const { translationX = 0, translationY = 0, velocityX = 0 } = event;

        // TINDER-STYLE: Only handle horizontal swipes for like/dislike
        // Vertical swipes can be handled separately for other actions

        // Update card position with bounds checking
        translateX.value = Math.max(-SCREEN_WIDTH * 2, Math.min(SCREEN_WIDTH * 2, translationX));
        translateY.value = Math.max(-SCREEN_HEIGHT, Math.min(SCREEN_HEIGHT, translationY * 0.3));

        // TINDER-STYLE: Calculate rotation based on horizontal movement
        const rotationProgress = Math.max(-1, Math.min(1, translationX / (SCREEN_WIDTH * 0.5)));
        rotate.value = rotationProgress * SWIPE_CONFIG.MAX_ROTATION;

        // TINDER-STYLE: Color overlay feedback
        const swipeProgress = Math.abs(translationX) / SWIPE_CONFIG.SWIPE_THRESHOLD;
        overlayOpacity.value = Math.min(swipeProgress * SWIPE_CONFIG.OVERLAY_OPACITY, SWIPE_CONFIG.OVERLAY_OPACITY);

        // Track swipe direction for visual feedback
        if (Math.abs(translationX) > 20) { // Minimum movement to show direction
          swipeDirection.value = translationX > 0 ? 'right' : 'left';
        }

        // TINDER-STYLE: Animate stack cards slightly as user swipes
        if (stackAnimations && stackAnimations.length > 0) {
          const stackProgress = Math.min(Math.abs(translationX) / (SCREEN_WIDTH * 0.4), 1);
          stackAnimations.forEach((animation, index) => {
            if (animation && animation.scale && animation.translateY) {
              const targetScale = 1 - (index + 1 - stackProgress * 0.3) * SWIPE_CONFIG.STACK_SCALE_FACTOR;
              const targetY = (index + 1 - stackProgress * 0.3) * SWIPE_CONFIG.STACK_OFFSET;

              animation.scale.value = Math.max(targetScale, 1 - (index + 1) * SWIPE_CONFIG.STACK_SCALE_FACTOR);
              animation.translateY.value = Math.max(targetY, index * SWIPE_CONFIG.STACK_OFFSET);
            }
          });
        }
      } catch (error) {
        console.error('[TinderSwipe] Error in onUpdate:', error);
      }
    })
    .onEnd((event) => {
      'worklet';
      try {
        if (!event) {
          console.warn('[TinderSwipe] No event data in onEnd');
          runOnJS(resetCardPosition)();
          return;
        }

        if (!items || currentIndex < 0 || currentIndex >= items.length) {
          console.warn(`[TinderSwipe] Invalid currentIndex (${currentIndex}) or no items`);
          runOnJS(resetCardPosition)();
          return;
        }

        const {
          translationX = 0,
          translationY = 0,
          velocityX = 0,
          velocityY = 0
        } = event;

        const swipedItem = items[currentIndex];

        if (!swipedItem) {
          console.warn(`[TinderSwipe] No item to swipe at index ${currentIndex}`);
          runOnJS(resetCardPosition)();
          return;
        }

        // TINDER-STYLE: Check for swipe completion conditions
        const absTranslationX = Math.abs(translationX);
        const absVelocityX = Math.abs(velocityX);

        // Condition 1: Distance threshold reached
        const distanceThresholdMet = absTranslationX > SWIPE_CONFIG.SWIPE_THRESHOLD;

        // Condition 2: Velocity threshold reached (fast swipe)
        const velocityThresholdMet = absVelocityX > SWIPE_CONFIG.VELOCITY_THRESHOLD;

        // TINDER-STYLE: Handle upward swipe for "add to cart" action
        if (translationY < -100 && Math.abs(velocityY) > 500) {
          console.log('[TinderSwipe] Upward swipe detected - Add to cart');

          // Animate card upward off screen
          translateY.value = withSpring(-SCREEN_HEIGHT * 1.2, SWIPE_CONFIG.SPRING_CONFIG);
          scale.value = withSpring(0.8, SWIPE_CONFIG.SPRING_CONFIG);
          overlayOpacity.value = withTiming(0, { duration: 200 });

          if (stackAnimations && stackAnimations.length > 0) {
            runOnJS(animateStackForward)();
          }

          // Use runOnJS for setTimeout
          runOnJS(() => {
            setTimeout(() => {
              processSwipeCompletion('up', swipedItem);
            }, 300);
          })();

          return;
        }

        // TINDER-STYLE: Handle horizontal swipes (like/dislike)
        if (distanceThresholdMet || velocityThresholdMet) {
          const direction = translationX > 0 ? 'right' : 'left';
          console.log(`[TinderSwipe] Swipe ${direction} completed - Distance: ${absTranslationX.toFixed(0)}, Velocity: ${absVelocityX.toFixed(0)}`);

          runOnJS(completeSwipe)(direction, velocityX);
          return;
        }

        // TINDER-STYLE: Swipe not completed - return to center
        console.log('[TinderSwipe] Swipe incomplete - returning to center');
        runOnJS(resetCardPosition)();

      } catch (err) {
        console.error("[TinderSwipe] Error in gesture end:", err);
        runOnJS(resetCardPosition)();
      }
    }), [currentIndex, items.length, processSwipeCompletion, resetCardPosition, completeSwipe, animateStackForward]);

  // TINDER-STYLE: Animated styles for top card
  const topCardStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotate.value}deg` },
      { scale: scale.value },
    ],
  }));

  // TINDER-STYLE: Color overlay for swipe feedback
  const overlayStyle = useAnimatedStyle(() => {
    const direction = swipeDirection.value;
    const opacity = overlayOpacity.value;

    if (opacity === 0) {
      return {
        opacity: 0,
        backgroundColor: 'transparent'
      };
    }

    return {
      opacity,
      backgroundColor: direction === 'right'
        ? 'rgba(76, 175, 80, 0.7)' // Green for like
        : 'rgba(244, 67, 54, 0.7)', // Red for dislike
    };
  });

  // TINDER-STYLE: Animated text styles for like/dislike indicators
  const likeTextStyle = useAnimatedStyle(() => {
    const direction = swipeDirection.value;
    const opacity = overlayOpacity.value;

    return {
      opacity: direction === 'right' && opacity > 0 ? 1 : 0,
    };
  });

  const dislikeTextStyle = useAnimatedStyle(() => {
    const direction = swipeDirection.value;
    const opacity = overlayOpacity.value;

    return {
      opacity: direction === 'left' && opacity > 0 ? 1 : 0,
    };
  });

  // TINDER-STYLE: Stack card animations
  const stackCardStyles = stackAnimations.map((animation) =>
    useAnimatedStyle(() => ({
      transform: [
        { scale: animation.scale.value },
        { translateY: animation.translateY.value },
      ],
      opacity: animation.opacity.value,
    }))
  );

  return {
    panGesture,
    topCardStyle,
    overlayStyle,
    likeTextStyle,
    dislikeTextStyle,
    stackCardStyles,
    resetCardPosition,
    // Don't read .value during render - use shared values directly
    isSwipeInProgress,
    swipeDirection,
  };
};
