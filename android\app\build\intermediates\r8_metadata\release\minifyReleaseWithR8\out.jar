{"options": {"hasObfuscationDictionary": false, "hasClassObfuscationDictionary": false, "hasPackageObfuscationDictionary": false, "keepAttributes": {"isAnnotationDefaultKept": true, "isEnclosingMethodKept": true, "isExceptionsKept": false, "isInnerClassesKept": true, "isLocalVariableTableKept": false, "isLocalVariableTypeTableKept": false, "isMethodParametersKept": false, "isPermittedSubclassesKept": false, "isRuntimeInvisibleAnnotationsKept": false, "isRuntimeInvisibleParameterAnnotationsKept": false, "isRuntimeInvisibleTypeAnnotationsKept": false, "isRuntimeVisibleAnnotationsKept": true, "isRuntimeVisibleParameterAnnotationsKept": true, "isRuntimeVisibleTypeAnnotationsKept": true, "isSignatureKept": true, "isSourceDebugExtensionKept": false, "isSourceDirKept": false, "isSourceFileKept": false, "isStackMapTableKept": false}, "isAccessModificationEnabled": false, "isFlattenPackageHierarchyEnabled": false, "isObfuscationEnabled": true, "isOptimizationsEnabled": false, "isProGuardCompatibilityModeEnabled": false, "isProtoLiteOptimizationEnabled": false, "isRepackageClassesEnabled": false, "isShrinkingEnabled": true, "apiModeling": {}, "minApiLevel": "24", "isDebugModeEnabled": false}, "baselineProfileRewriting": {}, "compilation": {"buildTimeNs": 112230158200, "numberOfThreads": 8}, "dexFiles": [{"checksum": "7548f15c6bfa43198409ab2d9648c4d7de06c9dd17cb8423a0192a19704a1dee", "startup": false}, {"checksum": "6f4add8a5ce8307c759fc70331708b249668692ac5ee695a1b15c7a4cb913896", "startup": false}], "stats": {"noObfuscationPercentage": 44.46, "noOptimizationPercentage": 100.0, "noShrinkingPercentage": 44.73}, "version": "8.8.34"}