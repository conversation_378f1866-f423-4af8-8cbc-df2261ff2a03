import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, ActivityIndicator, Alert, Platform, KeyboardAvoidingView, ScrollView, SafeAreaView, Dimensions } from 'react-native';
import { getAuth, signInWithEmailAndPassword, GoogleAuthProvider, signInWithCredential } from 'firebase/auth';
import { db } from '../firebase.config'; // Import db
import { Ionicons } from '@expo/vector-icons';
import { doc, setDoc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore'; // Import Firestore functions
import * as Google from 'expo-auth-session/providers/google';
import * as WebBrowser from 'expo-web-browser';
import authStateManager from '../utils/authState';

// Ensure the web browser closes correctly
WebBrowser.maybeCompleteAuthSession();

// Retrieve Client IDs from environment variables
// NOTE: Using WEB_CLIENT_ID for iosClientId works for Expo Go/dev client, but use a dedicated iOS ID for standalone builds.
const WEB_CLIENT_ID = process.env.FIREBASE_WEB_CLIENT_ID; // Crucial one from .env
const IOS_CLIENT_ID = process.env.FIREBASE_IOS_CLIENT_ID || WEB_CLIENT_ID; // Use specific iOS ID if available, otherwise fallback to Web ID for dev
const ANDROID_CLIENT_ID = process.env.FIREBASE_ANDROID_CLIENT_ID || WEB_CLIENT_ID; // Use Android ID from .env, fallback to web for dev

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Responsive constants
const INPUT_WIDTH = SCREEN_WIDTH * 0.9;
const INPUT_HEIGHT = SCREEN_HEIGHT * 0.06 > 50 ? SCREEN_HEIGHT * 0.06 : 50; // Min height 50
const BUTTON_HEIGHT = SCREEN_HEIGHT * 0.06 > 50 ? SCREEN_HEIGHT * 0.06 : 50; // Min height 50
const TITLE_FONT_SIZE = SCREEN_WIDTH * 0.07;
const INPUT_FONT_SIZE = SCREEN_WIDTH * 0.04;
const BUTTON_FONT_SIZE = SCREEN_WIDTH * 0.045;
const LINK_FONT_SIZE = SCREEN_WIDTH * 0.04;
const ACCOUNT_TYPE_BADGE_PADDING_VERTICAL = SCREEN_HEIGHT * 0.008;
const ACCOUNT_TYPE_BADGE_PADDING_HORIZONTAL = SCREEN_WIDTH * 0.03;
const ACCOUNT_TYPE_FONT_SIZE = SCREEN_WIDTH * 0.035;
const ACCOUNT_TYPE_ICON_SIZE = SCREEN_WIDTH * 0.045;

const LoginScreen = ({ navigation, route }) => {
  // Get isSeller parameter from route or default to false
  const { isSeller = false } = route.params || {};

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false); // Separate loading state for Google
  const auth = getAuth();

  // Configure Google Sign-In
  const [request, response, promptAsync] = Google.useIdTokenAuthRequest({
    webClientId: WEB_CLIENT_ID,
    iosClientId: IOS_CLIENT_ID, // Added iOS Client ID
    androidClientId: ANDROID_CLIENT_ID, // Now from .env
    useProxy: true, // Use Expo Auth Proxy for Google login
  });

  // Handle Google Sign-In response
  useEffect(() => {
    const handleGoogleSignIn = async () => {
      if (response?.type === 'success') {
        setGoogleLoading(true);
        const { id_token } = response.params;
        const credential = GoogleAuthProvider.credential(id_token);

        try {
          const userCredential = await signInWithCredential(auth, credential);
          const user = userCredential.user;
          console.log('Google Sign-In successful for:', user.email);

          // Check if user exists in Firestore
          const userDocRef = doc(db, 'users', user.uid);
          const docSnap = await getDoc(userDocRef);

          if (!docSnap.exists()) {
            console.log('New Google user. Creating basic Firestore document and navigating to complete profile.');
            // Create a basic user document first
            await setDoc(userDocRef, {
              uid: user.uid,
              email: user.email,
              name: user.displayName || '',
              profilePictureUrl: user.photoURL || null,
              createdAt: serverTimestamp(),
              isSeller: isSeller, // Use the isSeller parameter from route
              bio: '',
              address: null,
              website: '',
            });
            // Navigate to a new screen to complete the profile
            navigation.navigate('CompleteProfile', { userId: user.uid });
          } else {
            // Check if the user's seller status matches the current tab
            const userData = docSnap.data();
            const userIsSeller = userData.isSeller || false;

            if (userIsSeller !== isSeller) {
              // Set user type mismatch flag to prevent App.js from processing auth events
              authStateManager.setUserTypeMismatch(user.uid);

              // IMMEDIATELY sign out the user - no delays, no complex logic
              try {
                console.log('Google user type mismatch detected, signing out user immediately');
                await auth.signOut();
                console.log('Google user signed out due to account type mismatch');
              } catch (signOutError) {
                console.error('Error signing out Google user:', signOutError);
              }

              // Show appropriate error message and DO NOT navigate anywhere
              if (isSeller) {
                Alert.alert(
                  'Wrong Account Type',
                  'This Google account is registered as a Buyer. Please use the Buyer Login tab to log in with your seller account.',
                  [{ text: 'OK' }] // No navigation, just dismiss
                );
              } else {
                Alert.alert(
                  'Wrong Account Type',
                  'This Google account is registered as a Seller. Please use the Buyer Login tab to log in with your buyer account.',
                  [{ text: 'OK' }] // No navigation, just dismiss
                );
              }
              setGoogleLoading(false);
              return; // Exit early to prevent further processing
            } else {
              console.log('Firestore document already exists for user:', user.email);
            }
          }
        } catch (error) {
          console.error("Firebase Google Sign-In/Firestore Check Error:", error);

          // Provide user-friendly error messages for Google sign-in errors
          let errorTitle = 'Google Sign-In Failed';
          let errorMessage = 'Could not sign in with Google. Please try again.';

          if (error.code) {
            switch (error.code) {
              case 'auth/invalid-credential':
                errorTitle = 'Invalid Google Account';
                errorMessage = 'We couldn\'t verify your Google account. Please try again or use email login instead.';
                break;
              case 'auth/account-exists-with-different-credential':
                errorTitle = 'Account Already Exists';
                errorMessage = 'An account already exists with the same email address but different sign-in credentials. Try signing in with a different method.';
                break;
              case 'auth/popup-closed-by-user':
                errorTitle = 'Sign-In Cancelled';
                errorMessage = 'The Google sign-in was cancelled. Please try again if you want to sign in with Google.';
                break;
              case 'auth/network-request-failed':
                errorTitle = 'Network Error';
                errorMessage = 'A network error occurred. Please check your internet connection and try again.';
                break;
              default:
                if (error.message) {
                  errorMessage = error.message;
                }
                break;
            }
          }

          Alert.alert(errorTitle, errorMessage);
        } finally {
          setGoogleLoading(false);
        }
      } else if (response?.type === 'error') {
        console.error("Google Auth Error:", response.error);

        // Handle specific Google Auth errors
        let errorTitle = 'Google Sign-In Error';
        let errorMessage = 'An error occurred during Google sign-in. Please try again.';

        if (response.error && typeof response.error === 'string') {
          if (response.error.includes('popup_closed')) {
            errorTitle = 'Sign-In Cancelled';
            errorMessage = 'The Google sign-in was cancelled. Please try again if you want to sign in with Google.';
          } else if (response.error.includes('network')) {
            errorTitle = 'Network Error';
            errorMessage = 'A network error occurred. Please check your internet connection and try again.';
          } else {
            errorMessage = response.error;
          }
        }

        Alert.alert(errorTitle, errorMessage);
        setGoogleLoading(false);
      } else if (response?.type === 'cancel') {
        console.log('Google Sign-In cancelled by user.');
        setGoogleLoading(false);
      }
    };

    handleGoogleSignIn();
  }, [response, navigation, isSeller]);

  const handleLogin = async () => {
    // Prevent multiple submissions - set loading immediately
    if (loading) return;
    setLoading(true);

    try {
      if (!email || !password) {
        Alert.alert('Error', 'Please enter both email and password.');
        return;
      }

      // Sign in with email and password
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Check if user exists in Firestore and verify seller/buyer status
      const userDocRef = doc(db, 'users', user.uid);
      const docSnap = await getDoc(userDocRef);

      if (docSnap.exists()) {
        const userData = docSnap.data();
        const userIsSeller = userData.isSeller || false;

        // Check if user type matches the login tab
        if (userIsSeller !== isSeller) {
          // Set user type mismatch flag to prevent App.js from processing auth events
          authStateManager.setUserTypeMismatch(user.uid);

          // IMMEDIATELY sign out the user - no delays, no complex logic
          try {
            console.log('User type mismatch detected, signing out user immediately');
            await auth.signOut();
            console.log('User signed out due to account type mismatch');
          } catch (signOutError) {
            console.error('Error signing out user:', signOutError);
          }

          // Show appropriate error message and DO NOT navigate anywhere
          if (isSeller) {
            Alert.alert(
              'Wrong Account Type',
              'This is a Buyer account. Please use the Buyer Login tab to log in with your seller account.',
              [{ text: 'OK' }] // No navigation, just dismiss
            );
          } else {
            Alert.alert(
              'Wrong Account Type',
              'This is a Seller account. Please use the Buyer Login tab to log in with your buyer account.',
              [{ text: 'OK' }] // No navigation, just dismiss
            );
          }
          setLoading(false);
          return; // Exit early to prevent further processing
        } else {
          // Check email verification for both buyer and seller accounts
          if (!user.emailVerified) {
            // Check if we have a record of verification in Firestore
            const isVerifiedInFirestore = userData.emailVerified === true;

            if (!isVerifiedInFirestore) {
              // Email not verified, redirect to verification screen
              console.log('Email not verified, redirecting to verification screen');
              navigation.navigate('VerifyEmail', { isSeller: isSeller });
              return;
            } else {
              // If verified in Firestore but not in Auth, update Auth record
              // This is a fallback in case the Auth record is out of sync
              console.log('Email verified in Firestore but not in Auth, updating Auth record');
            }
          } else {
            // If verified in Auth but not in Firestore, update Firestore
            if (userData.emailVerified !== true) {
              await updateDoc(userDocRef, { emailVerified: true });
            }
          }

          // For seller accounts, check verification status
          if (isSeller) {
            // First check if email is verified
            if (!userData.emailVerified && !user.emailVerified) {
              console.log('Seller email not verified, redirecting to verification screen');
              navigation.navigate('VerifyEmail', { isSeller: true });
              return;
            }

            // Check seller verification status
            const sellerVerificationStatus = userData.sellerVerificationStatus || '';

            // If email verified but needs to complete seller info
            if (sellerVerificationStatus === 'needsEmailVerification' || sellerVerificationStatus === 'needsSellerInfo') {
              console.log('Seller needs to complete verification form');
              // Use reset to ensure we're at the root of the navigation stack
              navigation.reset({
                index: 0,
                routes: [{ name: 'SellerVerification' }],
              });
              return;
            }

            // Check if seller has completed the verification form (fallback for older accounts)
            const hasCompletedForm = userData.address && userData.address.city &&
              userData.phoneNumber && userData.website;
            if (!hasCompletedForm) {
              console.log('Seller needs to complete verification form (fallback)');
              // Use reset to ensure we're at the root of the navigation stack
              navigation.reset({
                index: 0,
                routes: [{ name: 'SellerVerification' }],
              });
              return;
            }

            // If seller needs to enter verification code
            if (sellerVerificationStatus === 'needsCode') {
              console.log('Seller needs to enter verification code, redirecting to verification pending screen');
              // Use reset to ensure we're at the root of the navigation stack
              navigation.reset({
                index: 0,
                routes: [{ name: 'VerificationPending' }],
              });
              return;
            }

            // If status is 'verified', continue to main app (handled by auth state listener)
          }

          console.log('User logged in successfully!');
        }
      } else {
        // If user exists in Auth but not in Firestore, create a new document
        await setDoc(userDocRef, {
          uid: user.uid,
          email: user.email,
          name: '',
          createdAt: serverTimestamp(),
          isSeller: isSeller,
          emailVerified: user.emailVerified,
          lastUsernameChangeDate: serverTimestamp(),
        });

        // For buyer accounts, check email verification
        if (!isSeller && !user.emailVerified) {
          navigation.navigate('VerifyEmail');
          return;
        }

        console.log('New user document created in Firestore');
      }
    } catch (error) {
      console.error("Login error:", error);

      // Provide user-friendly error messages based on error code
      let errorTitle = 'Login Failed';
      let errorMessage = 'An error occurred during login. Please try again.';

      switch (error.code) {
        case 'auth/invalid-credential':
          errorTitle = 'Invalid Credentials';
          errorMessage = 'The email or password you entered is incorrect. If you don\'t have an account yet, please sign up first.';
          break;
        case 'auth/user-not-found':
          errorTitle = 'Account Not Found';
          errorMessage = 'No account exists with this email address. Please check your email or sign up for a new account.';
          break;
        case 'auth/wrong-password':
          errorTitle = 'Incorrect Password';
          errorMessage = 'The password you entered is incorrect. Please try again or use the "Forgot Password" option.';
          break;
        case 'auth/invalid-email':
          errorTitle = 'Invalid Email';
          errorMessage = 'Please enter a valid email address.';
          break;
        case 'auth/user-disabled':
          errorTitle = 'Account Disabled';
          errorMessage = 'This account has been disabled. Please contact support for assistance.';
          break;
        case 'auth/too-many-requests':
          errorTitle = 'Too Many Attempts';
          errorMessage = 'Access to this account has been temporarily disabled due to many failed login attempts. You can try again later or reset your password.';
          break;
        case 'auth/network-request-failed':
          errorTitle = 'Network Error';
          errorMessage = 'A network error occurred. Please check your internet connection and try again.';
          break;
        default:
          // Use the error message from Firebase if available
          if (error.message) {
            errorMessage = error.message;
          }
          break;
      }

      Alert.alert(errorTitle, errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#fff' }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={0}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={true}
        >
          <View style={styles.container}>
            <Text style={styles.title}>Log In</Text>

            {/* Account Type Indicator */}
            <View style={styles.accountTypeContainer}>
              <View style={styles.accountTypeBadge}>
                <Ionicons
                  name={isSeller ? "storefront-outline" : "person-outline"}
                  size={18}
                  color="#fff"
                />
                <Text style={styles.accountTypeText}>
                  {isSeller ? "Seller Account" : "Buyer Account"}
                </Text>
              </View>
            </View>

            <TextInput
              style={styles.input}
              placeholder="Email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              editable={!loading && !googleLoading}
            />
            <TextInput
              style={styles.input}
              placeholder="Password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              editable={!loading && !googleLoading}
            />

            {/* Forgot Password Link - Show for both buyer and seller accounts */}
            <TouchableOpacity
              style={styles.forgotPasswordContainer}
              onPress={() => navigation.navigate('ForgotPassword', { isSeller })}
              disabled={loading || googleLoading}
            >
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>

            {loading ? (
              <ActivityIndicator size="large" color="#FF6B6B" style={styles.activityIndicator} />
            ) : (
              <TouchableOpacity
                style={styles.button}
                onPress={handleLogin}
                disabled={loading || googleLoading}
              >
                <Text style={styles.buttonText}>Log In</Text>
              </TouchableOpacity>
            )}

            {/* Show Google Sign-In only for buyer accounts */}
            {/**
            {!isSeller && (
              googleLoading ? (
                <ActivityIndicator size="large" color="#4285F4" style={styles.activityIndicator} />
              ) : (
                <TouchableOpacity
                  style={[styles.button, styles.googleButton]}
                  onPress={() => {
                    setGoogleLoading(true);
                    promptAsync();
                  }}
                  disabled={loading}
                >
                  <Ionicons name="logo-google" size={20} color="#fff" style={styles.googleIcon} />
                  <Text style={styles.buttonText}>Log In with Google</Text>
                </TouchableOpacity>
              )
            )}
            */}

            <TouchableOpacity onPress={() => navigation.navigate('SignUp', { isSeller })} disabled={loading || googleLoading}>
              <Text style={styles.linkText}>Don't have an account? Sign Up</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center', // Center content vertically
    alignItems: 'center', // Center content horizontally
    paddingVertical: 20,
  },
  container: {
    width: SCREEN_WIDTH, // Ensure container takes full width
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SCREEN_WIDTH * 0.05, // Use percentage for padding
    backgroundColor: '#fff',
  },
  title: {
    fontSize: TITLE_FONT_SIZE,
    fontWeight: 'bold',
    marginBottom: SCREEN_HEIGHT * 0.03, // Responsive margin
    color: '#333',
    textAlign: 'center',
  },
  accountTypeContainer: {
    marginBottom: SCREEN_HEIGHT * 0.025, // Responsive margin
    alignItems: 'center', // Center the badge
  },
  accountTypeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF8787', // Changed from #6c757d to a pink shade
    paddingVertical: ACCOUNT_TYPE_BADGE_PADDING_VERTICAL,
    paddingHorizontal: ACCOUNT_TYPE_BADGE_PADDING_HORIZONTAL,
    borderRadius: 15,
  },
  accountTypeText: {
    color: '#fff',
    fontSize: ACCOUNT_TYPE_FONT_SIZE,
    marginLeft: 8,
    fontWeight: '500',
  },
  input: {
    width: INPUT_WIDTH,
    height: INPUT_HEIGHT,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: INPUT_HEIGHT / 2, // Make it more pill-shaped
    paddingHorizontal: INPUT_WIDTH * 0.05,
    marginBottom: SCREEN_HEIGHT * 0.02, // Responsive margin
    fontSize: INPUT_FONT_SIZE,
    backgroundColor: '#f8f8f8',
  },
  button: {
    width: INPUT_WIDTH,
    height: BUTTON_HEIGHT,
    backgroundColor: '#FF6B6B',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BUTTON_HEIGHT / 2, // Make it more pill-shaped
    marginTop: SCREEN_HEIGHT * 0.01, // Responsive margin
    flexDirection: 'row', // Keep for icon if any
    elevation: 2, // Add some elevation for Android
    shadowColor: '#000', // Shadow for iOS
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  buttonText: {
    color: '#fff',
    fontSize: BUTTON_FONT_SIZE,
    fontWeight: 'bold',
  },
  googleButton: {
    backgroundColor: '#4285F4',
    marginTop: SCREEN_HEIGHT * 0.015, // Responsive margin
  },
  googleIcon: {
    marginRight: 10,
  },
  linkText: {
    marginTop: SCREEN_HEIGHT * 0.02, // Responsive margin
    color: '#FF6B6B',
    fontSize: LINK_FONT_SIZE,
    textAlign: 'center',
  },
  forgotPasswordContainer: {
    alignSelf: 'flex-end', // Align to the right, within the input_width
    marginBottom: SCREEN_HEIGHT * 0.02,
    marginRight: (SCREEN_WIDTH - INPUT_WIDTH) / 2, // Adjust to align with input field edge
  },
  forgotPasswordText: {
    color: '#007bff', // Standard link color
    fontSize: LINK_FONT_SIZE * 0.9, // Slightly smaller
  },
  activityIndicator: {
    marginTop: SCREEN_HEIGHT * 0.02,
  },
  // Ensure Ionicons in AccountTypeBadge are sized responsively
  accountTypeIcon: { // You might need to apply this style to the Ionicon directly if not inheriting
    fontSize: ACCOUNT_TYPE_ICON_SIZE,
    color: '#fff',
  }
});

export default LoginScreen;
