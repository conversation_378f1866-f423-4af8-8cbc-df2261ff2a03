# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: RelWithDebInfo
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = RelWithDebInfo
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen cmake_object_order_depends_target_react_codegen_lottiereactnative cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen cmake_object_order_depends_target_react_codegen_rnpicker cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/D_/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_RelWithDebInfo D$:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\D_\app\StyleApp\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\D_\app\StyleApp\android\app\build\generated\autolinking\src\main\jni

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libappmodules.so

build D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_RelWithDebInfo rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o CMakeFiles/appmodules.dir/D_/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnpicker.so D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_safeareacontext.so D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnscreens.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so || D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnpicker.so D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnscreens.so D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_safeareacontext.so RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen lottiereactnative_autolinked_build/react_codegen_lottiereactnative rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen rnreanimated_autolinked_build/react_codegen_rnreanimated
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = D:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnpicker.so  D:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_safeareacontext.so  D:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnscreens.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libappmodules.so
  TARGET_PDB = appmodules.so.dbg
  RSP_FILE = CMakeFiles\appmodules.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\app\StyleApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\rnasyncstorage_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\rnasyncstorage_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\app\StyleApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNGoogleSignInCGen


#############################################
# Order-only phony target for react_codegen_RNGoogleSignInCGen

build cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen: phony || RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/RNGoogleSignInCGen-generated.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\RNGoogleSignInCGen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/Props.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\RNGoogleSignInCGenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/States.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen



#############################################
# Object library react_codegen_RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen: phony RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o


#############################################
# Utility command for edit_cache

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\RNGoogleSignInCGen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNGoogleSignInCGen_autolinked_build/edit_cache: phony RNGoogleSignInCGen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\RNGoogleSignInCGen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\app\StyleApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNGoogleSignInCGen_autolinked_build/rebuild_cache: phony RNGoogleSignInCGen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnpicker


#############################################
# Order-only phony target for react_codegen_rnpicker

build cmake_object_order_depends_target_react_codegen_rnpicker: phony || rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerMeasurementsManager.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDialogPickerMeasurementsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDialogPickerShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerState.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDialogPickerState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerMeasurementsManager.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDropdownPickerMeasurementsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDropdownPickerShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerState.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDropdownPickerState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/rnpicker.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\rnpicker.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/024900284a0067438a91cc6cc40252aa/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\024900284a0067438a91cc6cc40252aa\source\codegen\jni\react\renderer\components\rnpicker\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\024900284a0067438a91cc6cc40252aa\source\codegen\jni\react\renderer\components\rnpicker

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/024900284a0067438a91cc6cc40252aa/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\024900284a0067438a91cc6cc40252aa\source\codegen\jni\react\renderer\components\rnpicker\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\024900284a0067438a91cc6cc40252aa\source\codegen\jni\react\renderer\components\rnpicker

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/c4d7ad40e364722459916eeab18d92df/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\c4d7ad40e364722459916eeab18d92df\build\generated\source\codegen\jni\react\renderer\components\rnpicker\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\c4d7ad40e364722459916eeab18d92df\build\generated\source\codegen\jni\react\renderer\components\rnpicker

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/6262661d85b07a4516ae598c40c16cbd/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\6262661d85b07a4516ae598c40c16cbd\generated\source\codegen\jni\react\renderer\components\rnpicker\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\6262661d85b07a4516ae598c40c16cbd\generated\source\codegen\jni\react\renderer\components\rnpicker

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/c4d7ad40e364722459916eeab18d92df/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\c4d7ad40e364722459916eeab18d92df\build\generated\source\codegen\jni\react\renderer\components\rnpicker\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\c4d7ad40e364722459916eeab18d92df\build\generated\source\codegen\jni\react\renderer\components\rnpicker

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/024900284a0067438a91cc6cc40252aa/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\024900284a0067438a91cc6cc40252aa\source\codegen\jni\react\renderer\components\rnpicker\rnpickerJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\024900284a0067438a91cc6cc40252aa\source\codegen\jni\react\renderer\components\rnpicker


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnpicker


#############################################
# Link the shared library D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libreact_codegen_rnpicker.so

build D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnpicker.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnpicker_RelWithDebInfo rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/024900284a0067438a91cc6cc40252aa/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/024900284a0067438a91cc6cc40252aa/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/c4d7ad40e364722459916eeab18d92df/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/6262661d85b07a4516ae598c40c16cbd/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/c4d7ad40e364722459916eeab18d92df/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/024900284a0067438a91cc6cc40252aa/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnpicker.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libreact_codegen_rnpicker.so
  TARGET_PDB = react_codegen_rnpicker.so.dbg


#############################################
# Utility command for edit_cache

build rnpicker_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\rnpicker_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnpicker_autolinked_build/edit_cache: phony rnpicker_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnpicker_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\rnpicker_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\app\StyleApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnpicker_autolinked_build/rebuild_cache: phony rnpicker_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_lottiereactnative


#############################################
# Order-only phony target for react_codegen_lottiereactnative

build cmake_object_order_depends_target_react_codegen_lottiereactnative: phony || lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_RelWithDebInfo D$:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/lottiereactnative-generated.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\lottiereactnative-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_RelWithDebInfo D$:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_RelWithDebInfo D$:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_RelWithDebInfo D$:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/Props.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_RelWithDebInfo D$:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_RelWithDebInfo D$:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/States.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_RelWithDebInfo D$:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\lottiereactnativeJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative



#############################################
# Object library react_codegen_lottiereactnative

build lottiereactnative_autolinked_build/react_codegen_lottiereactnative: phony lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp.o


#############################################
# Utility command for edit_cache

build lottiereactnative_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\lottiereactnative_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build lottiereactnative_autolinked_build/edit_cache: phony lottiereactnative_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build lottiereactnative_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\lottiereactnative_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\app\StyleApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build lottiereactnative_autolinked_build/rebuild_cache: phony lottiereactnative_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rngesturehandler_codegen


#############################################
# Order-only phony target for react_codegen_rngesturehandler_codegen

build cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen: phony || rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\rngesturehandler_codegenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\rngesturehandler_codegen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir



#############################################
# Object library react_codegen_rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o


#############################################
# Utility command for edit_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\rngesturehandler_codegen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rngesturehandler_codegen_autolinked_build/edit_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\rngesturehandler_codegen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\app\StyleApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/rebuild_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\rnreanimated_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\rnreanimated_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\app\StyleApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/489f1c3c785b29855feac2224d0527f7/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\489f1c3c785b29855feac2224d0527f7\renderer\components\safeareacontext\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\489f1c3c785b29855feac2224d0527f7\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/39895e341ebcc71f66b592787c6abfab/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\39895e341ebcc71f66b592787c6abfab\react\renderer\components\safeareacontext\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\39895e341ebcc71f66b592787c6abfab\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ff1f2489bcf3b2e31e49aad29d8fea76/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ff1f2489bcf3b2e31e49aad29d8fea76\react\renderer\components\safeareacontext\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ff1f2489bcf3b2e31e49aad29d8fea76\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7e298eca2726e4de430824781ad7074a/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\7e298eca2726e4de430824781ad7074a\jni\react\renderer\components\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\7e298eca2726e4de430824781ad7074a\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/10910abdbbee2f1568aa12fbb1bc7d89/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\10910abdbbee2f1568aa12fbb1bc7d89\codegen\jni\react\renderer\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\10910abdbbee2f1568aa12fbb1bc7d89\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7e298eca2726e4de430824781ad7074a/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\7e298eca2726e4de430824781ad7074a\jni\react\renderer\components\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\7e298eca2726e4de430824781ad7074a\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/10910abdbbee2f1568aa12fbb1bc7d89/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\10910abdbbee2f1568aa12fbb1bc7d89\codegen\jni\react\renderer\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\10910abdbbee2f1568aa12fbb1bc7d89\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bdb2e07ed73abd4b5d69484b8e280914/components/safeareacontext/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\bdb2e07ed73abd4b5d69484b8e280914\components\safeareacontext\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\bdb2e07ed73abd4b5d69484b8e280914\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8e3ab5c9eff1f489bcd59e39c91888bb/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8e3ab5c9eff1f489bcd59e39c91888bb\build\generated\source\codegen\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8e3ab5c9eff1f489bcd59e39c91888bb\build\generated\source\codegen\jni


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libreact_codegen_safeareacontext.so

build D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_RelWithDebInfo safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/489f1c3c785b29855feac2224d0527f7/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/39895e341ebcc71f66b592787c6abfab/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ff1f2489bcf3b2e31e49aad29d8fea76/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7e298eca2726e4de430824781ad7074a/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/10910abdbbee2f1568aa12fbb1bc7d89/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7e298eca2726e4de430824781ad7074a/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/10910abdbbee2f1568aa12fbb1bc7d89/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bdb2e07ed73abd4b5d69484b8e280914/components/safeareacontext/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8e3ab5c9eff1f489bcd59e39c91888bb/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libreact_codegen_safeareacontext.so
  TARGET_PDB = react_codegen_safeareacontext.so.dbg


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\app\StyleApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fc3da0873a65ab96a91ee3840a26004c/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\fc3da0873a65ab96a91ee3840a26004c\cpp\react\renderer\components\rnscreens\RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\fc3da0873a65ab96a91ee3840a26004c\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/dda1087935bd9f01d500ac250b456a92/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\dda1087935bd9f01d500ac250b456a92\common\cpp\react\renderer\components\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\dda1087935bd9f01d500ac250b456a92\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/dda1087935bd9f01d500ac250b456a92/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\dda1087935bd9f01d500ac250b456a92\common\cpp\react\renderer\components\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\dda1087935bd9f01d500ac250b456a92\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/36e762b3b0beee47724fe080fbf32c71/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\36e762b3b0beee47724fe080fbf32c71\react\renderer\components\rnscreens\RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\36e762b3b0beee47724fe080fbf32c71\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fc3da0873a65ab96a91ee3840a26004c/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\fc3da0873a65ab96a91ee3840a26004c\cpp\react\renderer\components\rnscreens\RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\fc3da0873a65ab96a91ee3840a26004c\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/36e762b3b0beee47724fe080fbf32c71/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\36e762b3b0beee47724fe080fbf32c71\react\renderer\components\rnscreens\RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\36e762b3b0beee47724fe080fbf32c71\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fc3da0873a65ab96a91ee3840a26004c/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\fc3da0873a65ab96a91ee3840a26004c\cpp\react\renderer\components\rnscreens\RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\fc3da0873a65ab96a91ee3840a26004c\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/dda1087935bd9f01d500ac250b456a92/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\dda1087935bd9f01d500ac250b456a92\common\cpp\react\renderer\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\dda1087935bd9f01d500ac250b456a92\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e53b248386f4a6ece72c11d87e9982a/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0e53b248386f4a6ece72c11d87e9982a\codegen\jni\react\renderer\components\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0e53b248386f4a6ece72c11d87e9982a\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/941a2d13243008312d94b735874a3ab8/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\941a2d13243008312d94b735874a3ab8\source\codegen\jni\react\renderer\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\941a2d13243008312d94b735874a3ab8\source\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0a1c7c95b09bc78e080e6bb6d373f810/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0a1c7c95b09bc78e080e6bb6d373f810\generated\source\codegen\jni\react\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0a1c7c95b09bc78e080e6bb6d373f810\generated\source\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/941a2d13243008312d94b735874a3ab8/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\941a2d13243008312d94b735874a3ab8\source\codegen\jni\react\renderer\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\941a2d13243008312d94b735874a3ab8\source\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0a1c7c95b09bc78e080e6bb6d373f810/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0a1c7c95b09bc78e080e6bb6d373f810\generated\source\codegen\jni\react\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0a1c7c95b09bc78e080e6bb6d373f810\generated\source\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e53b248386f4a6ece72c11d87e9982a/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/app/StyleApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0e53b248386f4a6ece72c11d87e9982a\codegen\jni\react\renderer\components\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/. -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0e53b248386f4a6ece72c11d87e9982a\codegen\jni\react\renderer\components\rnscreens


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libreact_codegen_rnscreens.so

build D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_RelWithDebInfo rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fc3da0873a65ab96a91ee3840a26004c/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/dda1087935bd9f01d500ac250b456a92/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/dda1087935bd9f01d500ac250b456a92/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/36e762b3b0beee47724fe080fbf32c71/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fc3da0873a65ab96a91ee3840a26004c/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/36e762b3b0beee47724fe080fbf32c71/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fc3da0873a65ab96a91ee3840a26004c/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/dda1087935bd9f01d500ac250b456a92/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e53b248386f4a6ece72c11d87e9982a/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/941a2d13243008312d94b735874a3ab8/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0a1c7c95b09bc78e080e6bb6d373f810/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/941a2d13243008312d94b735874a3ab8/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0a1c7c95b09bc78e080e6bb6d373f810/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e53b248386f4a6ece72c11d87e9982a/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/45da6d36d3d2ef1e836ae57a6da35b9a/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/4bccd564c4f2a425ba7527fe4eb35e1f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libreact_codegen_rnscreens.so
  TARGET_PDB = react_codegen_rnscreens.so.dbg


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\app\StyleApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libappmodules.so

build libappmodules.so: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libappmodules.so

build libreact_codegen_rnpicker.so: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnpicker.so

build libreact_codegen_rnscreens.so: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnscreens.so

build libreact_codegen_safeareacontext.so: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

build react_codegen_RNGoogleSignInCGen: phony RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen

build react_codegen_lottiereactnative: phony lottiereactnative_autolinked_build/react_codegen_lottiereactnative

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

build react_codegen_rnpicker: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnpicker.so

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnscreens.so

build react_codegen_safeareacontext: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a

build all: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libappmodules.so rnasyncstorage_autolinked_build/all RNGoogleSignInCGen_autolinked_build/all rnpicker_autolinked_build/all lottiereactnative_autolinked_build/all rngesturehandler_codegen_autolinked_build/all rnreanimated_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all

# =============================================================================

#############################################
# Folder: D:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/RNGoogleSignInCGen_autolinked_build

build RNGoogleSignInCGen_autolinked_build/all: phony RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen

# =============================================================================

#############################################
# Folder: D:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/lottiereactnative_autolinked_build

build lottiereactnative_autolinked_build/all: phony lottiereactnative_autolinked_build/react_codegen_lottiereactnative

# =============================================================================

#############################################
# Folder: D:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: D:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/rngesturehandler_codegen_autolinked_build

build rngesturehandler_codegen_autolinked_build/all: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

# =============================================================================

#############################################
# Folder: D:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/rnpicker_autolinked_build

build rnpicker_autolinked_build/all: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnpicker.so

# =============================================================================

#############################################
# Folder: D:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: D:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: D:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony D$:/app/StyleApp/android/app/build/intermediates/cxx/RelWithDebInfo/f367w611/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/CMakeFiles/cmake.verify_globs | C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake D$:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake D$:/app/StyleApp/android/app/.cxx/RelWithDebInfo/f367w611/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake D$:/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/app/StyleApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/app/StyleApp/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
