import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { collection, getDocs, addDoc, serverTimestamp, query, where, doc, updateDoc, increment } from 'firebase/firestore';
import { db, auth } from '../firebase.config';

const CollectionSelectionModal = ({
  visible,
  onClose,
  onSelectCollection,
  itemId,
  itemData
}) => {
  const [collections, setCollections] = useState([]);
  const [selectedCollections, setSelectedCollections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [showNewCollectionInput, setShowNewCollectionInput] = useState(false);
  const [checkingExistingCollections, setCheckingExistingCollections] = useState(false);
  const currentUserId = auth.currentUser?.uid;

  // Fetch user's collections and check which ones the item is already in
  useEffect(() => {
    if (visible && currentUserId && itemId) {
      fetchCollections();
      checkExistingCollections();
    }
  }, [visible, currentUserId, itemId]);

  // Check which collections this item is already in
  const checkExistingCollections = async () => {
    if (!currentUserId || !itemId) return;

    setCheckingExistingCollections(true);
    try {
      // Query wishlist items for this item
      const wishlistRef = collection(db, 'users', currentUserId, 'wishlist');
      const q = query(wishlistRef, where('itemId', '==', itemId));
      const querySnapshot = await getDocs(q);

      // Get collection IDs where this item exists
      const existingCollectionIds = querySnapshot.docs.map(doc => doc.data().collectionId);

      // Set these as selected
      setSelectedCollections(existingCollectionIds);
    } catch (error) {
      console.error('Error checking existing collections:', error);
    } finally {
      setCheckingExistingCollections(false);
    }
  };

  const fetchCollections = async () => {
    if (!currentUserId) return;

    setLoading(true);
    try {
      const collectionsRef = collection(db, 'users', currentUserId, 'collections');
      const querySnapshot = await getDocs(collectionsRef);

      const collectionsData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // We no longer add a default collection - users must create their own collections

      setCollections(collectionsData);
    } catch (error) {
      console.error('Error fetching collections:', error);
      Alert.alert('Error', 'Failed to load your collections');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCollection = async () => {
    if (!newCollectionName.trim()) {
      Alert.alert('Error', 'Please enter a collection name');
      return;
    }

    if (!currentUserId) return;

    setLoading(true);
    try {
      // Check if collection with this name already exists
      if (collections.some(c => c.name.toLowerCase() === newCollectionName.trim().toLowerCase())) {
        Alert.alert('Error', 'A collection with this name already exists');
        setLoading(false);
        return;
      }

      // Create new collection
      const collectionsRef = collection(db, 'users', currentUserId, 'collections');
      const newCollection = {
        name: newCollectionName.trim(),
        createdAt: serverTimestamp(),
        itemCount: 0
      };

      const docRef = await addDoc(collectionsRef, newCollection);

      // Add the new collection to the list
      const createdCollection = {
        id: docRef.id,
        ...newCollection,
        createdAt: new Date() // Use local date for immediate display
      };

      setCollections(prev => [...prev, createdCollection]);
      setNewCollectionName('');
      setShowNewCollectionInput(false);

      // Add the new collection to selected collections
      setSelectedCollections(prev => [...prev, createdCollection.id]);
    } catch (error) {
      console.error('Error creating collection:', error);
      Alert.alert('Error', 'Failed to create new collection');
    } finally {
      setLoading(false);
    }
  };

  // Save item to all selected collections
  const handleSaveToCollections = async () => {
    if (!currentUserId || !itemId || selectedCollections.length === 0) return;

    setLoading(true);
    try {
      // First, check if the item is already in any collections to avoid duplicates
      const wishlistRef = collection(db, 'users', currentUserId, 'wishlist');
      const existingQuery = query(wishlistRef, where('itemId', '==', itemId));
      const existingSnapshot = await getDocs(existingQuery);

      // Create a map of existing collection IDs to avoid duplicates
      const existingCollectionIds = new Map();
      existingSnapshot.forEach(doc => {
        const collectionId = doc.data().collectionId;
        existingCollectionIds.set(collectionId, doc.id);
      });

      console.log(`Item ${itemId} is already in ${existingCollectionIds.size} collections`);

      // Get the collections data for the selected collections
      const selectedCollectionsData = collections.filter(c =>
        selectedCollections.includes(c.id)
      );

      // Save to each selected collection
      await Promise.all(selectedCollectionsData.map(async (selectedCollection) => {
        // Skip if already in this collection
        if (existingCollectionIds.has(selectedCollection.id)) {
          console.log(`Item ${itemId} already exists in collection ${selectedCollection.id}, skipping`);
          return;
        }

        console.log(`Adding item ${itemId} to collection ${selectedCollection.id}`);

        // Add to the selected collection
        await addDoc(wishlistRef, {
          itemId: itemId,
          addedAt: new Date(),
          collectionId: selectedCollection.id
        });

        // Update collection item count
        const collectionRef = doc(db, 'users', currentUserId, 'collections', selectedCollection.id);

        // Update the collection with the latest item's image URL and item count
        await updateDoc(collectionRef, {
          itemCount: increment(1),
          latestItemImageUrl: itemData.imageUrl // Store the latest item's image URL
        });

        // Update the saveCount in the clothingItems collection
        const itemRef = doc(db, 'clothingItems', itemId);
        await updateDoc(itemRef, {
          saveCount: increment(1)
        });
        console.log(`Updated saveCount for item ${itemId}`);
      }));

      // Call the onSelectCollection with the first selected collection (for backward compatibility)
      if (selectedCollectionsData.length > 0) {
        onSelectCollection(selectedCollectionsData[0], itemId, itemData);
      }

      // Close the modal
      onClose();
    } catch (error) {
      console.error('Error saving to collections:', error);
      Alert.alert('Error', 'Failed to save to collections');
    } finally {
      setLoading(false);
    }
  };

  // Toggle collection selection
  const toggleCollectionSelection = (collectionId) => {
    setSelectedCollections(prev => {
      if (prev.includes(collectionId)) {
        return prev.filter(id => id !== collectionId);
      } else {
        return [...prev, collectionId];
      }
    });
  };

  const renderCollectionItem = ({ item }) => {
    const isSelected = selectedCollections.includes(item.id);

    return (
      <TouchableOpacity
        style={styles.collectionItem}
        onPress={() => toggleCollectionSelection(item.id)}
      >
        <View style={styles.collectionInfo}>
          <Text style={styles.collectionName}>{item.name}</Text>
          {item.itemCount !== undefined && (
            <Text style={styles.itemCount}>{item.itemCount} items</Text>
          )}
        </View>
        <View style={styles.checkboxContainer}>
          {isSelected ? (
            <Ionicons name="checkbox" size={24} color="#FF6B6B" />
          ) : (
            <Ionicons name="square-outline" size={24} color="#999" />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'position'}
        style={styles.modalOverlay}
        keyboardVerticalOffset={Platform.OS === 'ios' ? -150 : -180}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Save to Collection</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color="#555" />
              </TouchableOpacity>
            </View>

            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#FF6B6B" />
                <Text style={styles.loadingText}>Loading collections...</Text>
              </View>
            ) : (
              <>
                <FlatList
                  data={collections}
                  renderItem={renderCollectionItem}
                  keyExtractor={item => item.id}
                  key="modal-collections-list" // Add a key to ensure proper rendering
                  contentContainerStyle={styles.collectionsList}
                  ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                      <Text style={styles.emptyText}>No collections found</Text>
                      <Text style={styles.emptySubText}>Create a collection to save this item</Text>
                    </View>
                  }
                />

                {showNewCollectionInput ? (
                  <View style={styles.newCollectionContainer}>
                    <TextInput
                      style={styles.newCollectionInput}
                      placeholder="Collection name"
                      value={newCollectionName}
                      onChangeText={setNewCollectionName}
                      autoFocus={true}
                      maxLength={30}
                    />
                    <View style={styles.newCollectionButtons}>
                      <TouchableOpacity
                        style={[styles.newCollectionButton, styles.cancelButton]}
                        onPress={() => {
                          setShowNewCollectionInput(false);
                          setNewCollectionName('');
                        }}
                      >
                        <Text style={styles.cancelButtonText}>Cancel</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[styles.newCollectionButton, styles.createButton]}
                        onPress={handleCreateCollection}
                      >
                        <Text style={styles.createButtonText}>Create</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : (
                  <>
                    <TouchableOpacity
                      style={styles.createCollectionButton}
                      onPress={() => setShowNewCollectionInput(true)}
                    >
                      <Ionicons name="add-circle-outline" size={20} color="#FF6B6B" />
                      <Text style={styles.createCollectionText}>Create New Collection</Text>
                    </TouchableOpacity>

                    <View style={styles.saveButtonContainer}>
                      {selectedCollections.length === 0 ? (
                        <Text style={styles.noSelectionText}>
                          Please select or create a collection
                        </Text>
                      ) : (
                        <TouchableOpacity
                          style={styles.saveButton}
                          onPress={handleSaveToCollections}
                        >
                          <Text style={styles.saveButtonText}>
                            Save to {selectedCollections.length} {selectedCollections.length === 1 ? 'Collection' : 'Collections'}
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  </>
                )}
              </>
            )}
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center', // Changed from 'flex-end' to 'center'
    alignItems: 'center',
    // paddingBottom: Platform.OS === 'ios' ? 100 : 80, // Removed platform-specific padding
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingBottom: 20, // Increased paddingBottom for consistency
    // maxHeight: Platform.OS === 'ios' ? '60%' : '50%', // Removed platform-specific maxHeight
    maxHeight: '85%', // Set a consistent maxHeight, increased from 80%
    minHeight: 350, // Ensure consistent minimum height
    // marginBottom: 10, // Removed marginBottom
    marginHorizontal: 10,
    width: '90%',
    elevation: 5, // Added elevation for Android shadow
    shadowColor: '#000', // Added shadow for iOS
    shadowOffset: { width: 0, height: 2 }, // Added shadow for iOS
    shadowOpacity: 0.25, // Added shadow for iOS
    shadowRadius: 3.84, // Added shadow for iOS
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 15, // Increased paddingTop
    paddingBottom: 10, // Decreased paddingBottom
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  loadingContainer: {
    padding: 30,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
  },
  collectionsList: {
    paddingBottom: 10, // Add some padding at the bottom of the list
    minHeight: 200, // Ensure minimum height even with collections
    flexGrow: 1, // Allow the list to grow
  },
  collectionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12, // Increased padding
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  collectionInfo: {
    flex: 1, // Allow text to take available space
    marginRight: 10, // Add some space before the checkbox
  },
  collectionName: {
    fontSize: 18, // Increased font size further
    fontWeight: '500',
    color: '#333',
    flexShrink: 1, // Allow text to shrink if needed, but prioritize showing full name
  },
  itemCount: {
    fontSize: 13, // Adjusted font size
    color: '#777',
    marginTop: 3, // Add a little space below the name
  },
  emptyContainer: {
    padding: 50,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  emptyText: {
    color: '#999',
    marginBottom: 5,
  },
  emptySubText: {
    color: '#999',
    fontSize: 12,
    textAlign: 'center',
  },
  createCollectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15, // Increased paddingVertical
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    marginTop: 10, // Added marginTop
  },
  createCollectionText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#FF6B6B',
    fontWeight: '500',
  },
  newCollectionContainer: {
    paddingVertical: 15, // Increased paddingVertical
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    // marginBottom: Platform.OS === 'ios' ? 5 : 10, // Removed platform-specific margin
    marginTop: 10, // Added marginTop
  },
  newCollectionInput: {
    borderWidth: 1,
    borderColor: '#FF6B6B',
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    marginBottom: 10,
    backgroundColor: '#fff',
  },
  newCollectionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  newCollectionButton: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 8,
    marginLeft: 10,
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
  },
  cancelButtonText: {
    color: '#666',
  },
  createButton: {
    backgroundColor: '#FF6B6B',
  },
  createButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  checkboxContainer: {
    padding: 5,
  },
  saveButtonContainer: {
    paddingVertical: 20, // Increased paddingVertical
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    alignItems: 'center',
    marginTop: 10, // Added marginTop
  },
  saveButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 15, // Increased paddingVertical
    paddingHorizontal: 25, // Increased paddingHorizontal
    borderRadius: 30, // Increased borderRadius for a more rounded look
    minWidth: 220, // Increased minWidth
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#f0f0f0',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  saveButtonTextDisabled: {
    color: '#999',
  },
  noSelectionText: {
    color: '#999',
    fontSize: 14, // Increased fontSize
    fontStyle: 'italic', // Added italic style
    textAlign: 'center', // Centered text
    paddingHorizontal: 10, // Added horizontal padding
  },
});

export default CollectionSelectionModal;
