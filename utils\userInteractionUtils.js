import { db } from '../firebase.config';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  increment,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore';
import { getCurrentUserId } from './authUtils';
import { safeGetCollection, safeAddDocument, safeUpdateDocument, safeDeleteDocument } from './firestoreUtils';

/**
 * Fetch comments for an item
 * @param {string} itemId - Item ID
 * @param {string} sortOption - Sort option (newest, oldest, mostLiked)
 * @returns {Promise<Array>} Array of comments
 */
export const fetchItemComments = async (itemId, sortOption = 'newest') => {
  if (!itemId) {
    console.error('fetchItemComments: No itemId provided');
    return [];
  }

  try {
    const commentsPath = `clothingItems/${itemId}/comments`;
    let queryOptions = {};

    switch (sortOption) {
      case 'oldest':
        queryOptions = { orderBy: { field: 'timestamp', direction: 'asc' } };
        break;
      case 'mostLiked':
        queryOptions = { orderBy: { field: 'likeCount', direction: 'desc' } };
        break;
      case 'newest':
      default:
        queryOptions = { orderBy: { field: 'timestamp', direction: 'desc' } };
        break;
    }

    const comments = await safeGetCollection(commentsPath, queryOptions);

    // Process comments to include user info and replies
    const processedComments = await Promise.all(comments.map(async (comment) => {
      // Get user info
      let userInfo = null;
      if (comment.userId) {
        const userDoc = await getDoc(doc(db, 'users', comment.userId));
        if (userDoc.exists()) {
          userInfo = {
            id: userDoc.id,
            name: userDoc.data().name || 'Unknown User',
            profilePicture: userDoc.data().profilePicture || null,
            isSeller: userDoc.data().isSeller || false
          };
        }
      }

      // Get replies
      let replies = [];
      try {
        const repliesPath = `clothingItems/${itemId}/comments/${comment.id}/replies`;
        replies = await safeGetCollection(repliesPath, { orderBy: { field: 'timestamp', direction: 'asc' } });

        // Add user info to replies
        replies = await Promise.all(replies.map(async (reply) => {
          let replyUserInfo = null;
          if (reply.userId) {
            const userDoc = await getDoc(doc(db, 'users', reply.userId));
            if (userDoc.exists()) {
              replyUserInfo = {
                id: userDoc.id,
                name: userDoc.data().name || 'Unknown User',
                profilePicture: userDoc.data().profilePicture || null,
                isSeller: userDoc.data().isSeller || false
              };
            }
          }
          return { ...reply, user: replyUserInfo };
        }));
      } catch (error) {
        console.error(`Error fetching replies for comment ${comment.id}:`, error);
        replies = [];
      }

      return { ...comment, user: userInfo, replies };
    }));

    return processedComments;
  } catch (error) {
    console.error('Error fetching comments:', error);
    return [];
  }
};

/**
 * Toggle like on an item
 * @param {string} itemId - Item ID
 * @returns {Promise<Object>} Result with success status and updated like state
 */
export const toggleItemLike = async (itemId) => {
  const userId = getCurrentUserId();
  if (!userId || !itemId) {
    console.error('toggleItemLike: Missing userId or itemId');
    return { success: false, isLiked: false };
  }

  try {
    // Check if user already liked the item
    const userLikesPath = `users/${userId}/likes`;
    const likesQuery = { where: { field: 'itemId', operator: '==', value: itemId } };
    const existingLikes = await safeGetCollection(userLikesPath, likesQuery);

    const isLiked = existingLikes.length > 0;

    if (isLiked) {
      // Unlike: Remove from user's likes collection
      await safeDeleteDocument(userLikesPath, existingLikes[0].id);

      // Update item's like count
      await safeUpdateDocument('clothingItems', itemId, {
        likeCount: increment(-1),
        likedBy: arrayRemove(userId)
      });

      return { success: true, isLiked: false };
    } else {
      // Like: Add to user's likes collection
      await safeAddDocument(userLikesPath, {
        itemId,
        timestamp: serverTimestamp()
      });

      // Update item's like count
      await safeUpdateDocument('clothingItems', itemId, {
        likeCount: increment(1),
        likedBy: arrayUnion(userId)
      });

      return { success: true, isLiked: true };
    }
  } catch (error) {
    console.error('Error toggling like:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Fetch user collections
 * @returns {Promise<Array>} Array of collections
 */
export const fetchUserCollections = async () => {
  const userId = getCurrentUserId();
  if (!userId) {
    console.error('fetchUserCollections: No userId available');
    return [];
  }

  try {
    const collectionsPath = `users/${userId}/collections`;
    return await safeGetCollection(collectionsPath, { orderBy: { field: 'createdAt', direction: 'desc' } });
  } catch (error) {
    console.error('Error fetching collections:', error);
    return [];
  }
};

/**
 * Add item to cart
 * @param {string} itemId - Item ID
 * @param {Object} itemData - Item data
 * @returns {Promise<Object>} Result with success status
 */
export const addToCart = async (itemId, itemData) => {
  const userId = getCurrentUserId();
  if (!userId || !itemId) {
    console.error('addToCart: Missing userId or itemId');
    return { success: false };
  }

  try {
    // Ensure all required fields are present and not undefined
    const cartItem = {
      itemId,
      addedAt: serverTimestamp(),
      title: itemData.title || 'Untitled Item',
      category: itemData.category || 'Uncategorized',
      brand: itemData.brand || 'Unknown Brand',
      imageUrl: itemData.imageUrl || null,
      size: itemData.size || 'One Size',
      price: itemData.price || 0,
      quantity: 1
    };

    const cartPath = `users/${userId}/cart`;
    const docId = await safeAddDocument(cartPath, cartItem);

    return { success: true, docId };
  } catch (error) {
    console.error('Error updating cart:', error);
    return { success: false, error: error.message };
  }
};
