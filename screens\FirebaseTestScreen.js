import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator,
  SafeAreaView,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth } from '../firebase.config';
import { signOut } from 'firebase/auth';
import { runAllTests } from '../utils/FirebaseTestUtils';

const FirebaseTestScreen = ({ navigation }) => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState(null);

  useEffect(() => {
    setUser(auth.currentUser);
  }, []);

  const handleRunTests = async () => {
    setLoading(true);
    try {
      const testResults = await runAllTests();
      setResults(testResults);
    } catch (error) {
      console.error('Error running tests:', error);
      Alert.alert('Error', 'Failed to run tests: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      Alert.alert('Success', 'Signed out successfully');
      navigation.navigate('Welcome');
    } catch (error) {
      console.error('Error signing out:', error);
      Alert.alert('Error', 'Failed to sign out: ' + error.message);
    }
  };

  const getStatusIcon = (success) => {
    if (success === undefined) return null;
    return success ? 
      <Ionicons name="checkmark-circle" size={24} color="green" /> : 
      <Ionicons name="close-circle" size={24} color="red" />;
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Firebase Connection Test</Text>
      </View>

      <View style={styles.userInfo}>
        <Text style={styles.userInfoText}>
          {user ? `Logged in as: ${user.email}` : 'Not logged in'}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.button}
          onPress={handleRunTests}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Run All Tests</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, { backgroundColor: '#f44336' }]}
          onPress={handleSignOut}
          disabled={loading || !user}
        >
          <Text style={styles.buttonText}>Sign Out</Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6200ee" />
          <Text style={styles.loadingText}>Running tests...</Text>
        </View>
      ) : (
        <ScrollView style={styles.resultsContainer}>
          {results.length === 0 ? (
            <Text style={styles.noResultsText}>No test results yet. Press "Run All Tests" to begin.</Text>
          ) : (
            results.map((result, index) => (
              <View key={index} style={styles.resultItem}>
                <View style={styles.resultHeader}>
                  {getStatusIcon(result.success)}
                  <Text style={[
                    styles.resultTitle, 
                    { color: result.success ? 'green' : 'red' }
                  ]}>
                    {result.test}
                  </Text>
                </View>
                <Text style={styles.resultMessage}>{result.message}</Text>
                {result.data && (
                  <View style={styles.resultData}>
                    {Object.entries(result.data).map(([key, value]) => (
                      <Text key={key} style={styles.resultDataItem}>
                        {key}: {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </Text>
                    ))}
                  </View>
                )}
              </View>
            ))
          )}
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  userInfo: {
    padding: 16,
    backgroundColor: '#e0e0e0',
    borderRadius: 8,
    margin: 16,
  },
  userInfoText: {
    fontSize: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  button: {
    backgroundColor: '#6200ee',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  resultsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  noResultsText: {
    textAlign: 'center',
    marginTop: 32,
    fontSize: 16,
    color: '#666',
  },
  resultItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  resultMessage: {
    fontSize: 16,
    marginBottom: 8,
  },
  resultData: {
    backgroundColor: '#f5f5f5',
    padding: 8,
    borderRadius: 4,
  },
  resultDataItem: {
    fontSize: 14,
    marginBottom: 4,
  },
});

export default FirebaseTestScreen;
