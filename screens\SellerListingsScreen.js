import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  StyleSheet,
  ActivityIndicator,
  SafeAreaView,
  Platform,
  StatusBar,
  TouchableOpacity,
  Alert,
  Dimensions,
  TextInput
} from 'react-native';
import { collection, query, where, getDocs, orderBy, doc, getDoc, deleteDoc } from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');
const ITEM_WIDTH = width / 2 - 24; // 2 items per row with padding

const SellerListingsScreen = ({ navigation }) => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sortOrder, setSortOrder] = useState('newest');
  const [searchQuery, setSearchQuery] = useState('');
  const currentUserId = auth.currentUser?.uid;

  // Fetch the user's uploads
  const fetchUploads = useCallback(async () => {
    setLoading(true);
    try {
      // Create query based on sort order
      let q = query(
        collection(db, 'clothingItems'),
        where('userId', '==', currentUserId)
      );
      
      if (sortOrder === 'newest') {
        q = query(q, orderBy('createdAt', 'desc'));
      } else if (sortOrder === 'oldest') {
        q = query(q, orderBy('createdAt', 'asc'));
      } else if (sortOrder === 'mostLiked') {
        q = query(q, orderBy('likeCount', 'desc'));
      }
      
      const snapshot = await getDocs(q);
      setItems(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));
    } catch (e) {
      console.error("Error fetching uploads:", e);
      setItems([]);
    } finally {
      setLoading(false);
    }
  }, [currentUserId, sortOrder]);

  // Use useFocusEffect to fetch data when the screen is focused
  useFocusEffect(
    useCallback(() => {
      fetchUploads();
    }, [fetchUploads])
  );

  // Handle item deletion
  const handleDeleteItem = (itemId) => {
    Alert.alert(
      "Delete Item",
      "Are you sure you want to delete this item? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        { 
          text: "Delete", 
          style: "destructive",
          onPress: async () => {
            try {
              await deleteDoc(doc(db, 'clothingItems', itemId));
              // Update the local state to remove the deleted item
              setItems(prevItems => prevItems.filter(item => item.id !== itemId));
              Alert.alert("Success", "Item deleted successfully");
            } catch (error) {
              console.error("Error deleting item:", error);
              Alert.alert("Error", "Failed to delete item. Please try again.");
            }
          }
        }
      ]
    );
  };

  // Handle item editing
  const handleEditItem = (itemId) => {
    navigation.navigate('EditItem', { itemId });
  };

  // Filter items based on search query
  const filteredItems = items.filter(item => {
    const searchLower = searchQuery.toLowerCase();
    return (
      (item.title && item.title.toLowerCase().includes(searchLower)) ||
      (item.description && item.description.toLowerCase().includes(searchLower)) ||
      (item.category && item.category.toLowerCase().includes(searchLower)) ||
      (item.brand && item.brand.toLowerCase().includes(searchLower))
    );
  });

  // Render each item in the grid
  const renderItem = ({ item }) => (
    <View style={styles.itemContainer}>
      <TouchableOpacity
        onPress={() => navigation.navigate('ItemDetails', { itemId: item.id })}
        style={styles.itemContent}
      >
        <Image source={{ uri: item.imageUrl }} style={styles.image} />
        <View style={styles.itemInfo}>
          <Text style={styles.title} numberOfLines={1}>{item.title || 'Untitled'}</Text>
          <Text style={styles.category}>{item.category}</Text>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Ionicons name="heart" size={14} color="#FF6B6B" />
              <Text style={styles.statText}>{item.likeCount || 0}</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="bookmark" size={14} color="#4A90E2" />
              <Text style={styles.statText}>{item.saveCount || 0}</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>

      <View style={styles.actionButtons}>
        <TouchableOpacity
          onPress={() => handleEditItem(item.id)}
          style={styles.actionButton}
        >
          <Ionicons name="create-outline" size={22} color="#4A90E2" />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => handleDeleteItem(item.id)}
          style={styles.actionButton}
        >
          <Ionicons name="trash-outline" size={22} color="#FF6B6B" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>My Listings</Text>
          <TouchableOpacity 
            style={styles.uploadButton}
            onPress={() => navigation.navigate('Upload')}
          >
            <Ionicons name="add-circle" size={24} color="#FF6B6B" />
            <Text style={styles.uploadButtonText}>New</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search your listings..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#999" />
            </TouchableOpacity>
          ) : null}
        </View>

        <View style={styles.sortContainer}>
          <Text style={styles.sortLabel}>Sort by:</Text>
          <View style={styles.sortButtons}>
            <TouchableOpacity
              style={[styles.sortButton, sortOrder === 'newest' && styles.activeSortButton]}
              onPress={() => setSortOrder('newest')}
            >
              <Text style={[styles.sortButtonText, sortOrder === 'newest' && styles.activeSortText]}>Newest</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.sortButton, sortOrder === 'oldest' && styles.activeSortButton]}
              onPress={() => setSortOrder('oldest')}
            >
              <Text style={[styles.sortButtonText, sortOrder === 'oldest' && styles.activeSortText]}>Oldest</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.sortButton, sortOrder === 'mostLiked' && styles.activeSortButton]}
              onPress={() => setSortOrder('mostLiked')}
            >
              <Text style={[styles.sortButtonText, sortOrder === 'mostLiked' && styles.activeSortText]}>Most Liked</Text>
            </TouchableOpacity>
          </View>
        </View>

        {loading ? (
          <ActivityIndicator style={styles.loader} size="large" color="#FF6B6B" />
        ) : (
          <FlatList
            data={filteredItems}
            renderItem={renderItem}
            keyExtractor={item => item.id}
            numColumns={2}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="images-outline" size={60} color="#ccc" />
                <Text style={styles.emptyText}>
                  {searchQuery ? 'No items match your search' : 'No listings yet'}
                </Text>
                {!searchQuery && (
                  <TouchableOpacity 
                    style={styles.emptyButton}
                    onPress={() => navigation.navigate('Upload')}
                  >
                    <Text style={styles.emptyButtonText}>Upload Your First Item</Text>
                  </TouchableOpacity>
                )}
              </View>
            }
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 10 : 20,
    paddingBottom: 15,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B6B',
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF0F0',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  uploadButtonText: {
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginLeft: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    marginHorizontal: 16,
    marginTop: 16,
    paddingHorizontal: 12,
    height: 40,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
  },
  sortLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#555',
    marginRight: 10,
  },
  sortButtons: {
    flexDirection: 'row',
  },
  sortButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 15,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  activeSortButton: {
    backgroundColor: '#FF6B6B',
  },
  sortButtonText: {
    fontSize: 14,
    color: '#555',
  },
  activeSortText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  listContent: {
    padding: 12,
  },
  itemContainer: {
    width: ITEM_WIDTH,
    margin: 8,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  itemContent: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: ITEM_WIDTH * 1.2,
    backgroundColor: '#f0f0f0',
  },
  itemInfo: {
    padding: 10,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  category: {
    fontSize: 14,
    color: '#666',
    marginBottom: 6,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 14,
    color: '#777',
    marginLeft: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    color: '#999',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 20,
  },
  emptyButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default SellerListingsScreen;
