import { auth } from '../firebase.config';
import { onAuthStateChanged } from 'firebase/auth';

/**
 * Check if a user is currently authenticated
 * @returns {Promise<boolean>} True if user is authenticated, false otherwise
 */
export const isUserAuthenticated = () => {
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      unsubscribe(); // Unsubscribe immediately after getting the result
      resolve(!!user); // Convert user object to boolean
    });
  });
};

/**
 * Get the current user ID if authenticated
 * @returns {string|null} User ID if authenticated, null otherwise
 */
export const getCurrentUserId = () => {
  return auth.currentUser?.uid || null;
};

/**
 * Wrapper function to handle Firebase operations with authentication check
 * @param {Function} operation - The Firebase operation to perform
 * @param {Function} onError - Error handler function
 * @returns {Promise<any>} Result of the operation or null if error
 */
export const withAuthCheck = async (operation, onError = console.error) => {
  try {
    const isAuthenticated = await isUserAuthenticated();
    if (!isAuthenticated) {
      throw new Error('User is not authenticated');
    }
    return await operation();
  } catch (error) {
    onError(error);
    return null;
  }
};
