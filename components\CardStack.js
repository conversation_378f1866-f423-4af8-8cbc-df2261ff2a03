import React, { useCallback, useEffect } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, PixelRatio, Animated as RNAnimated } from 'react-native';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';
import ClothingCard from './ClothingCard';
import { isCustomCategory } from '../utils/feedHelpers';
import { defaultCategories, PRELOAD_THRESHOLD } from '../utils/feedConstants';
import { useSwipeGestures } from '../hooks/useSwipeGestures';
import { useSwipeAnimations } from '../hooks/useSwipeAnimations';
import { styles } from './CardStack.styles';

const CardStack = ({
  items,
  loading,
  error,
  noMoreItems,
  currentIndex,
  setCurrentIndex,
  isCurrentItemWishlisted,
  currentUserId,
  onItemPress,
  onWishlistToggle,
  onAddToCart,
  onRefresh,
  activeCategory,
  setActiveCategory,
  onSwipeComplete,
  fetchClothingItems,
  backgroundLoading,
  triggerLikeAnimation: triggerLikeAnimationProp,
  triggerDislikeAnimation: triggerDislikeAnimationProp
}) => {
  // Initialize swipe gestures and animations
  const handleSwipeComplete = useCallback(async (direction, swipedItem) => {
    try {
      if (!swipedItem) {
        console.log("[CardStack] No item to process for swipe completion");
        return;
      }

      // Update current index first
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);

      // Call the parent's swipe completion handler
      if (onSwipeComplete) {
        await onSwipeComplete(direction, swipedItem);
      }

      // Check if we need to load more items
      if (items.length - nextIndex <= PRELOAD_THRESHOLD && !noMoreItems && !backgroundLoading) {
        console.log(`[CardStack] Approaching end, loading more...`);
        if (fetchClothingItems) {
          fetchClothingItems(false, true);
        }
      }
    } catch (error) {
      console.error('[CardStack] Error in handleSwipeComplete:', error);
    }
  }, [currentIndex, setCurrentIndex, onSwipeComplete, items.length, noMoreItems, backgroundLoading, fetchClothingItems]);

  const { panGesture, topCardStyle, resetCardAnimations, cardAnimations } = useSwipeGestures({
    items,
    currentIndex,
    onSwipeComplete: handleSwipeComplete,
    onResetAnimations: () => {
      // Additional reset logic if needed
    },
    triggerLikeAnimation: triggerLikeAnimationProp,
    triggerDislikeAnimation: triggerDislikeAnimationProp
  });

  const {
    likeAnimationOpacity,
    dislikeAnimationOpacity,
    likeAnimationScale,
    dislikeAnimationScale
  } = useSwipeAnimations();

  // Reset animations when currentIndex changes or when not loading
  useEffect(() => {
    if (!loading) {
      resetCardAnimations();
    }
  }, [currentIndex, loading, resetCardAnimations]);

  // Show loading state
  if (loading) {
    return (
      <View style={styles.statusContainer}>
        <ActivityIndicator size="large" color="#FF6B6B" />
        <Text style={styles.loadingText}>Loading amazing items...</Text>
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View style={styles.statusContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={onRefresh}
        >
          <Text style={styles.refreshButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show empty state
  if (!items || items.length === 0 || currentIndex >= items.length) {
    const isCustomCat = isCustomCategory(activeCategory, defaultCategories);

    return (
      <View style={styles.statusContainer}>
        <Text style={styles.emptyText}>
          {noMoreItems
            ? (isCustomCat
              ? `No items found in "${activeCategory}"`
              : "You've seen all items!")
            : "No items available"
          }
        </Text>
        <Text style={styles.emptySubText}>
          {noMoreItems
            ? (isCustomCat
              ? "Try browsing other categories or check back later for new items."
              : "Check back later for new items!")
            : "Please try again later"
          }
        </Text>

        <View style={styles.emptyButtonsContainer}>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={onRefresh}
          >
            <Text style={styles.refreshButtonText}>Refresh Feed</Text>
          </TouchableOpacity>

          {isCustomCat && (
            <TouchableOpacity
              style={[styles.refreshButton, { marginLeft: 10, backgroundColor: '#4CAF50' }]}
              onPress={() => setActiveCategory('All')}
            >
              <Text style={styles.refreshButtonText}>View All Items</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  }

  // Create animated styles for cards behind the top card (must be at component level)
  const card1AnimatedStyle = useAnimatedStyle(() => {
    if (cardAnimations && cardAnimations[0]) {
      return {
        transform: [
          { scale: cardAnimations[0].scale.value },
          { translateY: cardAnimations[0].translateY.value }
        ],
        opacity: cardAnimations[0].opacity.value
      };
    }
    return {
      transform: [
        { scale: 1 - (1 * 0.03) },
        { translateY: 1 * 15 }
      ]
    };
  });

  const card2AnimatedStyle = useAnimatedStyle(() => {
    if (cardAnimations && cardAnimations[1]) {
      return {
        transform: [
          { scale: cardAnimations[1].scale.value },
          { translateY: cardAnimations[1].translateY.value }
        ],
        opacity: cardAnimations[1].opacity.value
      };
    }
    return {
      transform: [
        { scale: 1 - (2 * 0.03) },
        { translateY: 2 * 15 }
      ]
    };
  });

  // Render card stack
  const visibleCards = 3;
  const cardsToRender = items.slice(currentIndex, currentIndex + visibleCards);

  return (
    <>
      <View style={styles.cardAreaContainer}>
        {cardsToRender.map((item, stackIndex) => {
          const isTopCard = stackIndex === 0;

          // Determine which animated style to use
          let cardAnimatedStyle = null;
          if (!isTopCard) {
            if (stackIndex === 1) {
              cardAnimatedStyle = card1AnimatedStyle;
            } else if (stackIndex === 2) {
              cardAnimatedStyle = card2AnimatedStyle;
            }
          }

          return (
            <ClothingCard
              key={item.id}
              item={item}
              isTopCard={isTopCard}
              isCurrentItemWishlisted={isTopCard ? isCurrentItemWishlisted : false}
              currentUserId={currentUserId}
              onPress={onItemPress}
              onWishlistToggle={onWishlistToggle}
              panGesture={isTopCard ? panGesture : null}
              animatedStyle={isTopCard ? topCardStyle : cardAnimatedStyle}
              style={[
                styles.card,
                {
                  zIndex: visibleCards - stackIndex,
                  // Fallback static transform for cards beyond the animated ones
                  ...(stackIndex > 2 ? {
                    transform: [
                      { scale: 1 - (stackIndex * 0.03) },
                      { translateY: stackIndex * 15 }
                    ]
                  } : {})
                }
              ]}
            />
          );
        })}
      </View>

      {/* Render animations */}
      <View style={styles.animationsContainer}>
        {/* Like Animation */}
        <RNAnimated.View
          style={[
            styles.animationContainer,
            {
              opacity: likeAnimationOpacity,
              transform: [{ scale: likeAnimationScale }],
            }
          ]}
          pointerEvents="none"
        >
          <Text style={styles.animationText}>❤️</Text>
        </RNAnimated.View>

        {/* Dislike Animation */}
        <RNAnimated.View
          style={[
            styles.animationContainer,
            {
              opacity: dislikeAnimationOpacity,
              transform: [{ scale: dislikeAnimationScale }],
            }
          ]}
          pointerEvents="none"
        >
          <Text style={styles.animationText}>👎</Text>
        </RNAnimated.View>


      </View>
    </>
  );
};

export default CardStack;
