import React, { useCallback, useEffect } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, PixelRatio, Animated as RNAnimated } from 'react-native';
import Animated from 'react-native-reanimated';
import ClothingCard from './ClothingCard';
import { isCustomCategory } from '../utils/feedHelpers';
import { defaultCategories, PRELOAD_THRESHOLD } from '../utils/feedConstants';
import { useSwipeGestures } from '../hooks/useSwipeGestures';
import { useSwipeAnimations } from '../hooks/useSwipeAnimations';
import { styles } from './CardStack.styles';

const CardStack = ({
  items,
  loading,
  error,
  noMoreItems,
  currentIndex,
  setCurrentIndex,
  isCurrentItemWishlisted,
  currentUserId,
  onItemPress,
  onWishlistToggle,
  onAddToCart,
  onRefresh,
  activeCategory,
  setActiveCategory,
  onSwipeComplete,
  fetchClothingItems,
  backgroundLoading,
  triggerLikeAnimation: triggerLikeAnimationProp,
  triggerDislikeAnimation: triggerDislikeAnimationProp
}) => {
  // Initialize swipe gestures and animations
  const handleSwipeComplete = useCallback(async (direction, swipedItem) => {
    try {
      if (!swipedItem) {
        console.log("[CardStack] No item to process for swipe completion");
        return;
      }

      // Update current index first
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);

      // Call the parent's swipe completion handler
      if (onSwipeComplete) {
        await onSwipeComplete(direction, swipedItem);
      }

      // Check if we need to load more items
      if (items.length - nextIndex <= PRELOAD_THRESHOLD && !noMoreItems && !backgroundLoading) {
        console.log(`[CardStack] Approaching end, loading more...`);
        if (fetchClothingItems) {
          fetchClothingItems(false, true);
        }
      }
    } catch (error) {
      console.error('[CardStack] Error in handleSwipeComplete:', error);
    }
  }, [currentIndex, setCurrentIndex, onSwipeComplete, items.length, noMoreItems, backgroundLoading, fetchClothingItems]);

  // TINDER-STYLE: Get gesture handlers and animations
  const {
    panGesture,
    topCardStyle,
    overlayStyle,
    likeTextStyle,
    dislikeTextStyle,
    stackCardStyles,
    resetCardPosition,
    isSwipeInProgress,
    swipeDirection
  } = useSwipeGestures({
    items,
    currentIndex,
    onSwipeComplete: handleSwipeComplete,
    onResetAnimations: () => {
      // Additional reset logic if needed
    },
    triggerLikeAnimation: triggerLikeAnimationProp,
    triggerDislikeAnimation: triggerDislikeAnimationProp
  });

  const {
    likeAnimationOpacity,
    dislikeAnimationOpacity,
    likeAnimationScale,
    dislikeAnimationScale
  } = useSwipeAnimations();

  // TINDER-STYLE: Reset card position when needed (not automatic reset)
  useEffect(() => {
    if (!loading) {
      // Reset position when index changes (after swipe completion)
      resetCardPosition();
    }
  }, [currentIndex, loading, resetCardPosition]);

  // Show loading state
  if (loading) {
    return (
      <View style={styles.statusContainer}>
        <ActivityIndicator size="large" color="#FF6B6B" />
        <Text style={styles.loadingText}>Loading amazing items...</Text>
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View style={styles.statusContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={onRefresh}
        >
          <Text style={styles.refreshButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show empty state
  if (!items || items.length === 0 || currentIndex >= items.length) {
    const isCustomCat = isCustomCategory(activeCategory, defaultCategories);

    return (
      <View style={styles.statusContainer}>
        <Text style={styles.emptyText}>
          {noMoreItems
            ? (isCustomCat
              ? `No items found in "${activeCategory}"`
              : "You've seen all items!")
            : "No items available"
          }
        </Text>
        <Text style={styles.emptySubText}>
          {noMoreItems
            ? (isCustomCat
              ? "Try browsing other categories or check back later for new items."
              : "Check back later for new items!")
            : "Please try again later"
          }
        </Text>

        <View style={styles.emptyButtonsContainer}>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={onRefresh}
          >
            <Text style={styles.refreshButtonText}>Refresh Feed</Text>
          </TouchableOpacity>

          {isCustomCat && (
            <TouchableOpacity
              style={[styles.refreshButton, { marginLeft: 10, backgroundColor: '#4CAF50' }]}
              onPress={() => setActiveCategory('All')}
            >
              <Text style={styles.refreshButtonText}>View All Items</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  }

  // Render card stack
  const visibleCards = 3;
  const cardsToRender = items.slice(currentIndex, currentIndex + visibleCards);

  return (
    <>
      <View style={styles.cardAreaContainer}>
        {cardsToRender.map((item, stackIndex) => {
          const isTopCard = stackIndex === 0;
          const isStackCard = stackIndex > 0 && stackIndex <= 2;

          return (
            <View key={item.id} style={styles.cardWrapper}>
              <ClothingCard
                item={item}
                isTopCard={isTopCard}
                isCurrentItemWishlisted={isTopCard ? isCurrentItemWishlisted : false}
                currentUserId={currentUserId}
                onPress={onItemPress}
                onWishlistToggle={onWishlistToggle}
                panGesture={isTopCard ? panGesture : null}
                animatedStyle={
                  isTopCard
                    ? topCardStyle
                    : isStackCard && stackCardStyles[stackIndex - 1]
                      ? stackCardStyles[stackIndex - 1]
                      : null
                }
                style={[
                  styles.card,
                  {
                    zIndex: visibleCards - stackIndex,
                    // TINDER-STYLE: Static fallback for cards beyond animated ones
                    ...(!isTopCard && (!isStackCard || !stackCardStyles[stackIndex - 1]) ? {
                      transform: [
                        { scale: 1 - (stackIndex * 0.05) },
                        { translateY: stackIndex * 20 }
                      ]
                    } : {})
                  }
                ]}
              />

              {/* TINDER-STYLE: Color overlay for swipe feedback (only on top card) */}
              {isTopCard && (
                <Animated.View
                  style={[
                    styles.swipeOverlay,
                    overlayStyle
                  ]}
                  pointerEvents="none"
                >
                  <Animated.View style={[styles.swipeIndicator, likeTextStyle]}>
                    <Text style={styles.swipeText}>
                      ❤️ LIKE
                    </Text>
                  </Animated.View>
                  <Animated.View style={[styles.swipeIndicator, dislikeTextStyle]}>
                    <Text style={styles.swipeText}>
                      👎 PASS
                    </Text>
                  </Animated.View>
                </Animated.View>
              )}
            </View>
          );
        })}
      </View>

      {/* Render animations */}
      <View style={styles.animationsContainer}>
        {/* Like Animation */}
        <RNAnimated.View
          style={[
            styles.animationContainer,
            {
              opacity: likeAnimationOpacity,
              transform: [{ scale: likeAnimationScale }],
            }
          ]}
          pointerEvents="none"
        >
          <Text style={styles.animationText}>❤️</Text>
        </RNAnimated.View>

        {/* Dislike Animation */}
        <RNAnimated.View
          style={[
            styles.animationContainer,
            {
              opacity: dislikeAnimationOpacity,
              transform: [{ scale: dislikeAnimationScale }],
            }
          ]}
          pointerEvents="none"
        >
          <Text style={styles.animationText}>👎</Text>
        </RNAnimated.View>


      </View>
    </>
  );
};

export default CardStack;
