import { useCallback } from 'react';
import { collection, doc, updateDoc, increment, arrayUnion, arrayRemove, addDoc, deleteDoc, setDoc, getDocs, getDoc, query, where } from 'firebase/firestore';
import { db, auth } from '../firebase.config';

export const useSwipeActions = ({
  currentUserId,
  setInteractedItemIds,
  addToCart
}) => {
  // Process swipe completion
  const processSwipeCompletion = useCallback(async (direction, swipedItem) => {
    try {
      if (!swipedItem) {
        console.log("[useSwipeActions] No item to process for swipe completion");
        return;
      }

      // Check authentication first
      if (!currentUserId) {
        console.log("[useSwipeActions] No authenticated user, skipping Firestore operations");
        // Still update local state
        setInteractedItemIds(prevIds => new Set(prevIds).add(swipedItem.id));
        return;
      }

      // Double-check Firebase auth state
      if (!auth.currentUser || auth.currentUser.uid !== currentUserId) {
        console.log("[useSwipeActions] Firebase auth state mismatch, skipping Firestore operations");
        // Still update local state
        setInteractedItemIds(prevIds => new Set(prevIds).add(swipedItem.id));
        return;
      }

      // Verify authentication token is valid
      try {
        await auth.currentUser.getIdToken(true);
      } catch (tokenError) {
        console.error('[useSwipeActions] Failed to get auth token:', tokenError);
        // Still update local state
        setInteractedItemIds(prevIds => new Set(prevIds).add(swipedItem.id));
        return;
      }

      console.log(`[useSwipeActions] Processing ${direction} swipe for item ${swipedItem.id} by user ${currentUserId}`);

      // Handle upward swipe (cart) - Optimized for performance
      if (direction === 'up') {
        console.log(`[useSwipeActions] Processing upward swipe for item ${swipedItem.id}`);

        // Immediate local state updates for instant UI feedback
        setInteractedItemIds(prevIds => new Set(prevIds).add(swipedItem.id));

        // Add to cart immediately with optimistic update
        if (addToCart) {
          addToCart(swipedItem);
        }

        // Run all database operations in parallel (non-blocking)
        // This prevents sequential delays and improves perceived performance
        Promise.allSettled([
          // Operation 1: Store interacted item
          (async () => {
            try {
              const interactedQuery = query(
                collection(db, 'users', currentUserId, 'interactedItems'),
                where('itemId', '==', swipedItem.id)
              );
              const interactedSnapshot = await getDocs(interactedQuery);

              if (interactedSnapshot.empty) {
                await addDoc(collection(db, 'users', currentUserId, 'interactedItems'), {
                  itemId: swipedItem.id,
                  interactedAt: new Date(),
                  interactionType: 'cart'
                });
                console.log(`[useSwipeActions] Added item ${swipedItem.id} to interactedItems`);
              }
            } catch (error) {
              console.error('[useSwipeActions] Error storing interacted item:', error);
            }
          })(),

          // Operation 2: Like the item
          (async () => {
            try {
              const itemRef = doc(db, 'clothingItems', swipedItem.id);
              const itemDoc = await getDoc(itemRef);

              if (!itemDoc.exists()) {
                console.log(`[useSwipeActions] Item ${swipedItem.id} does not exist, skipping like operation`);
                return;
              }

              const userLikesRef = doc(db, 'users', currentUserId, 'likes', swipedItem.id);
              const userDislikesRef = doc(db, 'users', currentUserId, 'dislikes', swipedItem.id);

              // Update item and user likes in parallel
              await Promise.all([
                updateDoc(itemRef, {
                  likedBy: arrayUnion(currentUserId),
                  dislikedBy: arrayRemove(currentUserId),
                  likeCount: increment(1)
                }),
                setDoc(userLikesRef, { itemId: swipedItem.id, likedAt: new Date() })
              ]);

              // Clean up dislikes (non-critical, can fail silently)
              try {
                await deleteDoc(userDislikesRef);
              } catch (e) {
                // Ignore if doesn't exist
              }

              console.log(`[useSwipeActions] User ${currentUserId} liked item ${swipedItem.id} via cart swipe`);
            } catch (error) {
              console.error('[useSwipeActions] Error updating like status:', error);
            }
          })()
        ]).then((results) => {
          // Log any failures but don't block the user experience
          results.forEach((result, index) => {
            if (result.status === 'rejected') {
              console.error(`[useSwipeActions] Background operation ${index + 1} failed:`, result.reason);
            }
          });
        });

        return;
      }

      // Handle left/right swipes
      setInteractedItemIds(prevIds => new Set(prevIds).add(swipedItem.id));

      // Store interacted item in Firestore to persist across refreshes
      try {
        await addDoc(collection(db, 'users', currentUserId, 'interactedItems'), {
          itemId: swipedItem.id,
          interactedAt: new Date(),
          interactionType: direction === 'right' ? 'like' : 'dislike'
        });
        console.log(`[useSwipeActions] Added item ${swipedItem.id} to interactedItems with type '${direction === 'right' ? 'like' : 'dislike'}'`);
      } catch (error) {
        console.error('[useSwipeActions] Error storing interacted item:', error);
        // Don't throw - continue with other operations
      }

      // Handle like/dislike actions
      try {
        const itemRef = doc(db, 'clothingItems', swipedItem.id);

        // First check if the item exists
        const itemDoc = await getDoc(itemRef);
        if (!itemDoc.exists()) {
          console.log(`[useSwipeActions] Item ${swipedItem.id} does not exist, skipping Firestore updates`);
          return;
        }

        if (direction === 'right') {
          // Like action
          const userLikesRef = doc(db, 'users', currentUserId, 'likes', swipedItem.id);
          const userDislikesRef = doc(db, 'users', currentUserId, 'dislikes', swipedItem.id);

          await updateDoc(itemRef, {
            likedBy: arrayUnion(currentUserId),
            dislikedBy: arrayRemove(currentUserId),
            likeCount: increment(1)
          });
          await setDoc(userLikesRef, { itemId: swipedItem.id, likedAt: new Date() });
          try { await deleteDoc(userDislikesRef); } catch (e) { /* Ignore if doesn't exist */ }

          console.log(`[useSwipeActions] User ${currentUserId} liked item ${swipedItem.id}`);
        } else {
          // Dislike action
          const userDislikesRef = doc(db, 'users', currentUserId, 'dislikes', swipedItem.id);
          const userLikesRef = doc(db, 'users', currentUserId, 'likes', swipedItem.id);

          await updateDoc(itemRef, {
            dislikedBy: arrayUnion(currentUserId),
            likedBy: arrayRemove(currentUserId),
            dislikeCount: increment(1)
          });
          await setDoc(userDislikesRef, { itemId: swipedItem.id, dislikedAt: new Date() });
          try { await deleteDoc(userLikesRef); } catch (e) { /* Ignore if doesn't exist */ }

          console.log(`[useSwipeActions] User ${currentUserId} disliked item ${swipedItem.id}`);
        }

        // Update view count
        await updateDoc(itemRef, {
          viewCount: increment(1)
        });
      } catch (error) {
        console.error('[useSwipeActions] Error updating item interaction:', error);
        // Don't throw - this is not critical for the user experience
      }
    } catch (error) {
      console.error('[useSwipeActions] Error in processSwipeCompletion:', error);
    }
  }, [currentUserId, setInteractedItemIds, addToCart]);

  return {
    processSwipeCompletion
  };
};
