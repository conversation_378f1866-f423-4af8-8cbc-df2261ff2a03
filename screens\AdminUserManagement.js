import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  ActivityIndicator,
  RefreshControl,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  updateDoc,
  doc,
  deleteDoc
} from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import { checkAdminStatus } from '../utils/adminUtils';
import StatusDropdown from '../components/StatusDropdown';

const AdminUserManagement = ({ navigation, route }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState(route?.params?.filter || 'all');
  const [selectedUser, setSelectedUser] = useState(null);
  const [userModalVisible, setUserModalVisible] = useState(false);

  const filters = [
    { id: 'all', title: 'All Users', icon: 'people-outline' },
    { id: 'sellers', title: 'Sellers', icon: 'storefront-outline' },
    { id: 'buyers', title: 'Buyers', icon: 'person-outline' },
    { id: 'admins', title: 'Admins', icon: 'shield-outline' },
    { id: 'suspended', title: 'Suspended', icon: 'ban-outline' }
  ];

  // Dropdown options for user filter
  const userFilterOptions = [
    { value: 'all', label: 'All Users', icon: 'people-outline', color: '#666' },
    { value: 'sellers', label: 'Sellers', icon: 'storefront-outline', color: '#FF6B6B' },
    { value: 'buyers', label: 'Buyers', icon: 'person-outline', color: '#4CAF50' },
    { value: 'admins', label: 'Admins', icon: 'shield-outline', color: '#4ECDC4' },
    { value: 'suspended', label: 'Suspended', icon: 'ban-outline', color: '#FF9F43' }
  ];

  useEffect(() => {
    checkAdminAccess();
  }, []);

  useEffect(() => {
    if (isAdmin) {
      fetchUsers();
    }
  }, [isAdmin, selectedFilter]);

  useEffect(() => {
    filterUsers();
  }, [users, searchQuery, selectedFilter]);

  const checkAdminAccess = async () => {
    try {
      const adminStatus = await checkAdminStatus(auth.currentUser.uid);
      setIsAdmin(adminStatus);

      if (!adminStatus) {
        Alert.alert(
          'Access Denied',
          'You do not have administrator privileges.',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);

      let q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));

      // Apply filter-specific queries
      if (selectedFilter === 'sellers') {
        q = query(collection(db, 'users'), where('isSeller', '==', true), orderBy('createdAt', 'desc'));
      } else if (selectedFilter === 'buyers') {
        q = query(collection(db, 'users'), where('isSeller', '==', false), orderBy('createdAt', 'desc'));
      } else if (selectedFilter === 'admins') {
        q = query(collection(db, 'users'), where('isAdmin', '==', true), orderBy('createdAt', 'desc'));
      } else if (selectedFilter === 'suspended') {
        q = query(collection(db, 'users'), where('isSuspended', '==', true), orderBy('createdAt', 'desc'));
      }

      const snapshot = await getDocs(q);
      const usersData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      }));

      setUsers(usersData);
    } catch (error) {
      console.error('Error fetching users:', error);
      Alert.alert('Error', 'Failed to fetch users');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const filterUsers = () => {
    let filtered = users;

    if (searchQuery.trim()) {
      filtered = users.filter(user =>
        user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.uid?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredUsers(filtered);
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchUsers();
  };

  const openUserModal = (user) => {
    setSelectedUser(user);
    setUserModalVisible(true);
  };

  const toggleUserSuspension = async (user) => {
    try {
      const newSuspendedStatus = !user.isSuspended;

      Alert.alert(
        `${newSuspendedStatus ? 'Suspend' : 'Unsuspend'} User`,
        `Are you sure you want to ${newSuspendedStatus ? 'suspend' : 'unsuspend'} ${user.name}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: newSuspendedStatus ? 'Suspend' : 'Unsuspend',
            style: 'destructive',
            onPress: async () => {
              try {
                await updateDoc(doc(db, 'users', user.id), {
                  isSuspended: newSuspendedStatus,
                  suspendedAt: newSuspendedStatus ? new Date() : null,
                  suspendedBy: newSuspendedStatus ? auth.currentUser.uid : null
                });

                Alert.alert(
                  'Success',
                  `User ${newSuspendedStatus ? 'suspended' : 'unsuspended'} successfully`
                );

                setUserModalVisible(false);
                fetchUsers();
              } catch (error) {
                console.error('Error updating user suspension:', error);
                Alert.alert('Error', 'Failed to update user status');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error toggling user suspension:', error);
      Alert.alert('Error', 'Failed to update user status');
    }
  };

  const toggleAdminStatus = async (user) => {
    try {
      const newAdminStatus = !user.isAdmin;

      Alert.alert(
        `${newAdminStatus ? 'Grant' : 'Revoke'} Admin Access`,
        `Are you sure you want to ${newAdminStatus ? 'grant admin access to' : 'revoke admin access from'} ${user.name}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: newAdminStatus ? 'Grant' : 'Revoke',
            style: 'destructive',
            onPress: async () => {
              try {
                await updateDoc(doc(db, 'users', user.id), {
                  isAdmin: newAdminStatus,
                  adminGrantedAt: newAdminStatus ? new Date() : null,
                  adminGrantedBy: newAdminStatus ? auth.currentUser.uid : null
                });

                Alert.alert(
                  'Success',
                  `Admin access ${newAdminStatus ? 'granted' : 'revoked'} successfully`
                );

                setUserModalVisible(false);
                fetchUsers();
              } catch (error) {
                console.error('Error updating admin status:', error);
                Alert.alert('Error', 'Failed to update admin status');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error toggling admin status:', error);
      Alert.alert('Error', 'Failed to update admin status');
    }
  };



  const renderUserItem = ({ item }) => (
    <TouchableOpacity
      style={styles.userCard}
      onPress={() => openUserModal(item)}
    >
      <View style={styles.userHeader}>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{item.name || 'No Name'}</Text>
          <Text style={styles.userEmail}>{item.email}</Text>
          <Text style={styles.userDate}>
            Joined: {item.createdAt.toLocaleDateString()}
          </Text>
        </View>
        <View style={styles.userBadges}>
          {item.isSeller && (
            <View style={[styles.badge, { backgroundColor: '#FF6B6B' }]}>
              <Text style={styles.badgeText}>Seller</Text>
            </View>
          )}
          {item.isAdmin && (
            <View style={[styles.badge, { backgroundColor: '#4ECDC4' }]}>
              <Text style={styles.badgeText}>Admin</Text>
            </View>
          )}
          {item.isSuspended && (
            <View style={[styles.badge, { backgroundColor: '#FF9F43' }]}>
              <Text style={styles.badgeText}>Suspended</Text>
            </View>
          )}
        </View>
      </View>

      <View style={styles.userActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => openUserModal(item)}
        >
          <Ionicons name="eye-outline" size={16} color="#007AFF" />
          <Text style={styles.actionText}>View</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: item.isSuspended ? '#4CAF50' : '#FF6B6B' }]}
          onPress={() => toggleUserSuspension(item)}
        >
          <Ionicons
            name={item.isSuspended ? "checkmark-circle-outline" : "ban-outline"}
            size={16}
            color="white"
          />
          <Text style={[styles.actionText, { color: 'white' }]}>
            {item.isSuspended ? 'Unsuspend' : 'Suspend'}
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
          <Text style={styles.loadingText}>Loading users...</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  if (!isAdmin) {
    return (
      <SafeAreaWrapper>
        <View style={styles.accessDenied}>
          <Ionicons name="lock-closed-outline" size={80} color="#ccc" />
          <Text style={styles.accessDeniedText}>Access Denied</Text>
          <Text style={styles.accessDeniedSubtext}>
            You don't have administrator privileges.
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>User Management</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
          <Ionicons name="refresh" size={24} color="#FF6B6B" />
        </TouchableOpacity>
      </SafeAreaHeader>

      <View style={styles.container}>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search users by name, email, or ID..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        {/* User Filter Dropdown */}
        <View style={styles.filterContainer}>
          <StatusDropdown
            options={userFilterOptions}
            selectedValue={selectedFilter}
            onValueChange={setSelectedFilter}
            label="Filter Users by Type:"
            style={styles.userDropdown}
          />
        </View>

        {/* Users List */}
        <FlatList
          data={filteredUsers}
          renderItem={renderUserItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="people-outline" size={80} color="#ccc" />
              <Text style={styles.emptyText}>No users found</Text>
              <Text style={styles.emptySubtext}>
                {searchQuery ? 'Try adjusting your search' : 'Users will appear here'}
              </Text>
            </View>
          }
          contentContainerStyle={styles.listContainer}
        />

        {/* User Details Modal */}
        <Modal
          visible={userModalVisible}
          animationType="slide"
          onRequestClose={() => setUserModalVisible(false)}
        >
          <SafeAreaWrapper>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <TouchableOpacity
                  onPress={() => setUserModalVisible(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
                <Text style={styles.modalTitle}>User Details</Text>
              </View>

              {selectedUser && (
                <View style={styles.modalContent}>
                  <View style={styles.userDetailCard}>
                    <Text style={styles.detailLabel}>Name</Text>
                    <Text style={styles.detailValue}>{selectedUser.name || 'No Name'}</Text>
                  </View>

                  <View style={styles.userDetailCard}>
                    <Text style={styles.detailLabel}>Email</Text>
                    <Text style={styles.detailValue}>{selectedUser.email}</Text>
                  </View>

                  <View style={styles.userDetailCard}>
                    <Text style={styles.detailLabel}>User ID</Text>
                    <Text style={styles.detailValue}>{selectedUser.uid}</Text>
                  </View>

                  <View style={styles.userDetailCard}>
                    <Text style={styles.detailLabel}>Account Type</Text>
                    <Text style={styles.detailValue}>
                      {selectedUser.isSeller ? 'Seller' : 'Buyer'}
                    </Text>
                  </View>

                  <View style={styles.userDetailCard}>
                    <Text style={styles.detailLabel}>Status</Text>
                    <Text style={[
                      styles.detailValue,
                      { color: selectedUser.isSuspended ? '#FF6B6B' : '#4CAF50' }
                    ]}>
                      {selectedUser.isSuspended ? 'Suspended' : 'Active'}
                    </Text>
                  </View>

                  <View style={styles.modalActions}>
                    <TouchableOpacity
                      style={[styles.modalActionButton, { backgroundColor: '#4ECDC4' }]}
                      onPress={() => toggleAdminStatus(selectedUser)}
                    >
                      <Text style={styles.modalActionText}>
                        {selectedUser.isAdmin ? 'Revoke Admin' : 'Make Admin'}
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        styles.modalActionButton,
                        { backgroundColor: selectedUser.isSuspended ? '#4CAF50' : '#FF6B6B' }
                      ]}
                      onPress={() => toggleUserSuspension(selectedUser)}
                    >
                      <Text style={styles.modalActionText}>
                        {selectedUser.isSuspended ? 'Unsuspend User' : 'Suspend User'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
          </SafeAreaWrapper>
        </Modal>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  filterContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  userDropdown: {
    marginBottom: 0,
  },
  listContainer: {
    paddingHorizontal: 16,
  },
  userCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  userDate: {
    fontSize: 12,
    color: '#999',
  },
  userBadges: {
    alignItems: 'flex-end',
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 4,
  },
  badgeText: {
    fontSize: 10,
    color: 'white',
    fontWeight: 'bold',
  },
  userActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    flex: 1,
    marginHorizontal: 4,
    justifyContent: 'center',
  },
  actionText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  accessDenied: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  accessDeniedText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 20,
  },
  accessDeniedSubtext: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 10,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  modalCloseButton: {
    padding: 8,
    marginRight: 12,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalContent: {
    padding: 16,
  },
  userDetailCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#333',
  },
  modalActions: {
    marginTop: 20,
  },
  modalActionButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  modalActionText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default AdminUserManagement;
