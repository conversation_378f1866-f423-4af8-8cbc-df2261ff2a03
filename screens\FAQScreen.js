import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';

const FAQScreen = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedItems, setExpandedItems] = useState(new Set());

  const faqData = [
    {
      id: 1,
      category: 'Account',
      question: 'How do I create an account?',
      answer: 'To create an account, tap on "Sign Up" on the welcome screen. Choose between Buyer or Seller account, enter your email and password, and verify your email address. For sellers, additional verification steps are required.'
    },
    {
      id: 2,
      category: 'Account',
      question: 'I forgot my password. How can I reset it?',
      answer: 'On the login screen, tap "Forgot Password?". Enter your email address and select your account type (Buyer or Seller). You\'ll receive a password reset email with instructions.'
    },
    {
      id: 3,
      category: 'Account',
      question: 'How do I become a verified seller?',
      answer: 'After creating a seller account and verifying your email, you\'ll need to complete the seller verification form with your business details, address, and website. Our team will review your application and send you a verification code.'
    },
    {
      id: 4,
      category: 'Orders',
      question: 'How do I place an order?',
      answer: 'Browse items, add them to your cart, then proceed to checkout. Ensure your shipping address is correct, select payment method, and complete the payment. You\'ll receive an order confirmation email.'
    },
    {
      id: 5,
      category: 'Orders',
      question: 'Can I track my order?',
      answer: 'Yes! Go to "Order History" in your profile to view all your orders. You can see the current status and tracking information if provided by the seller.'
    },
    {
      id: 6,
      category: 'Orders',
      question: 'How do I cancel an order?',
      answer: 'You can cancel an order before it\'s shipped. Go to Order History, select your order, and tap "Cancel Order". If already shipped, you\'ll need to contact the seller or our support team.'
    },
    {
      id: 7,
      category: 'Payment',
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit/debit cards, net banking, UPI, and digital wallets through Razorpay. All payments are secure and encrypted.'
    },
    {
      id: 8,
      category: 'Payment',
      question: 'Is my payment information secure?',
      answer: 'Yes, we use Razorpay for secure payment processing. We don\'t store your card details on our servers. All transactions are encrypted and PCI DSS compliant.'
    },
    {
      id: 9,
      category: 'Payment',
      question: 'When will I be charged?',
      answer: 'Your payment is processed immediately when you place an order. For failed payments, you can retry or use a different payment method.'
    },
    {
      id: 10,
      category: 'Selling',
      question: 'How do I list an item for sale?',
      answer: 'As a verified seller, tap the "+" button in the feed, upload photos, fill in item details including title, description, price, category, and brand. Your item will be live immediately after posting.'
    },
    {
      id: 11,
      category: 'Selling',
      question: 'How do I manage my orders as a seller?',
      answer: 'Go to "Seller Orders" in your profile to view all incoming orders. You can update order status, add tracking information, and communicate with buyers.'
    },
    {
      id: 12,
      category: 'Selling',
      question: 'What are the seller fees?',
      answer: 'SwipeSense takes a 10% commission on each sale. You receive 90% of the order amount. Payments are processed after successful delivery confirmation.'
    },
    {
      id: 13,
      category: 'Returns',
      question: 'What is your return policy?',
      answer: 'Returns are handled by individual sellers. Check the seller\'s return policy on the item page. Generally, items can be returned within 7-14 days if they don\'t match the description.'
    },
    {
      id: 14,
      category: 'Returns',
      question: 'How do I request a refund?',
      answer: 'Contact the seller first through the order page. If unresolved, create a support ticket with your order details. We\'ll help mediate between you and the seller.'
    },
    {
      id: 15,
      category: 'Technical',
      question: 'The app is crashing or running slowly',
      answer: 'Try closing and reopening the app. If the problem persists, restart your device. Ensure you have the latest app version and stable internet connection. Contact support if issues continue.'
    },
    {
      id: 16,
      category: 'Technical',
      question: 'I\'m not receiving email notifications',
      answer: 'Check your spam/junk folder. Ensure <EMAIL> is added to your contacts. Verify your email address in app settings. Some email providers may block automated emails.'
    },
    {
      id: 17,
      category: 'Technical',
      question: 'How do I update my profile information?',
      answer: 'Go to "My Profile" > "Edit Profile" to update your name, bio, profile picture, and address. Note that usernames can only be changed once every 6 months.'
    },
    {
      id: 18,
      category: 'Features',
      question: 'How does the wishlist work?',
      answer: 'Tap the heart icon on any item to add it to your wishlist. Access your saved items from "My Profile" > "Wishlist". You\'ll get notifications if the price drops.'
    },
    {
      id: 19,
      category: 'Features',
      question: 'Can I message other users?',
      answer: 'Yes, you can message other users by going to their profile and tapping the message button. This helps you ask questions about items or discuss purchases.'
    },
    {
      id: 20,
      category: 'Features',
      question: 'How do comments and ratings work?',
      answer: 'You can comment on items to ask questions or share feedback. Sellers can respond to comments. Like comments by tapping the heart icon. Comments help build community trust.'
    }
  ];

  const categories = [...new Set(faqData.map(item => item.category))];

  const toggleExpanded = (id) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const filteredFAQs = faqData.filter(item =>
    item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderFAQItem = (item) => {
    const isExpanded = expandedItems.has(item.id);

    return (
      <TouchableOpacity
        key={item.id}
        style={styles.faqItem}
        onPress={() => toggleExpanded(item.id)}
        activeOpacity={0.7}
      >
        <View style={styles.questionContainer}>
          <View style={styles.questionHeader}>
            <Text style={styles.category}>{item.category}</Text>
            <Ionicons
              name={isExpanded ? 'chevron-up' : 'chevron-down'}
              size={20}
              color="#666"
            />
          </View>
          <Text style={styles.question}>{item.question}</Text>
        </View>

        {isExpanded && (
          <View style={styles.answerContainer}>
            <Text style={styles.answer}>{item.answer}</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderCategorySection = (category) => {
    const categoryItems = filteredFAQs.filter(item => item.category === category);

    if (categoryItems.length === 0) return null;

    return (
      <View key={category} style={styles.categorySection}>
        <Text style={styles.categoryTitle}>{category}</Text>
        {categoryItems.map(renderFAQItem)}
      </View>
    );
  };

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Frequently Asked Questions</Text>
        <View style={styles.headerSpacer} />
      </SafeAreaHeader>

      <View style={styles.container}>

        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search FAQs..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {searchQuery ? (
            // Show search results
            <View style={styles.searchResults}>
              {filteredFAQs.length === 0 ? (
                <View style={styles.noResults}>
                  <Ionicons name="search" size={48} color="#CCC" />
                  <Text style={styles.noResultsText}>No FAQs found</Text>
                  <Text style={styles.noResultsSubtext}>
                    Try different keywords or browse categories below
                  </Text>
                </View>
              ) : (
                <View>
                  <Text style={styles.resultsCount}>
                    {filteredFAQs.length} result{filteredFAQs.length !== 1 ? 's' : ''} found
                  </Text>
                  {filteredFAQs.map(renderFAQItem)}
                </View>
              )}
            </View>
          ) : (
            // Show categories
            <View>
              <View style={styles.introSection}>
                <Text style={styles.introTitle}>How can we help you?</Text>
                <Text style={styles.introText}>
                  Find answers to common questions about SwipeSense. Can't find what you're looking for? Contact our support team.
                </Text>
              </View>

              {categories.map(renderCategorySection)}

              <View style={styles.contactSection}>
                <Text style={styles.contactTitle}>Still need help?</Text>
                <Text style={styles.contactText}>
                  If you couldn't find the answer you're looking for, our support team is here to help.
                </Text>
                <TouchableOpacity
                  style={styles.contactButton}
                  onPress={() => navigation.navigate('Support')}
                >
                  <Ionicons name="headset-outline" size={20} color="#FFF" />
                  <Text style={styles.contactButtonText}>Contact Support</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </ScrollView>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSpacer: {
    width: 40, // Same width as back button for centering
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    margin: 20,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  introSection: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  introTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  introText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  categorySection: {
    marginBottom: 20,
  },
  categoryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    paddingLeft: 5,
  },
  faqItem: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  questionContainer: {
    padding: 20,
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  category: {
    fontSize: 12,
    color: '#4ECDC4',
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  question: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    lineHeight: 22,
  },
  answerContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderTopWidth: 1,
    borderTopColor: '#F8F9FA',
  },
  answer: {
    fontSize: 15,
    color: '#666',
    lineHeight: 22,
    paddingTop: 15,
  },
  searchResults: {
    marginBottom: 20,
  },
  resultsCount: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    fontStyle: 'italic',
  },
  noResults: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  noResultsText: {
    fontSize: 18,
    color: '#999',
    marginTop: 15,
    fontWeight: '600',
  },
  noResultsSubtext: {
    fontSize: 14,
    color: '#CCC',
    marginTop: 5,
    textAlign: 'center',
  },
  contactSection: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 30,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  contactTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  contactText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4ECDC4',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  contactButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default FAQScreen;
