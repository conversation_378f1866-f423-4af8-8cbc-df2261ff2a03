{"buildFiles": ["D:\\app\\StyleApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\app\\StyleApp\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\app\\StyleApp\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\app\\StyleApp\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\app\\StyleApp\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\app\\StyleApp\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\app\\StyleApp\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\app\\StyleApp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\app\\StyleApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\app\\StyleApp\\android\\app\\.cxx\\Debug\\4h302r3f\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\app\\StyleApp\\android\\app\\.cxx\\Debug\\4h302r3f\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "arm64-v8a", "output": "D:\\app\\StyleApp\\android\\app\\build\\intermediates\\cxx\\Debug\\4h302r3f\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bccd564c4f2a425ba7527fe4eb35e1f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\abe814a713e512e574ccc0dc7022ef0d\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\abe814a713e512e574ccc0dc7022ef0d\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "arm64-v8a", "output": "D:\\app\\StyleApp\\android\\app\\build\\intermediates\\cxx\\Debug\\4h302r3f\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\abe814a713e512e574ccc0dc7022ef0d\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\abe814a713e512e574ccc0dc7022ef0d\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bccd564c4f2a425ba7527fe4eb35e1f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "arm64-v8a", "output": "D:\\app\\StyleApp\\android\\app\\build\\intermediates\\cxx\\Debug\\4h302r3f\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["D:\\app\\StyleApp\\android\\app\\build\\intermediates\\cxx\\Debug\\4h302r3f\\obj\\arm64-v8a\\libreact_codegen_rnpicker.so", "D:\\app\\StyleApp\\android\\app\\build\\intermediates\\cxx\\Debug\\4h302r3f\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "D:\\app\\StyleApp\\android\\app\\build\\intermediates\\cxx\\Debug\\4h302r3f\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bccd564c4f2a425ba7527fe4eb35e1f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\abe814a713e512e574ccc0dc7022ef0d\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\abe814a713e512e574ccc0dc7022ef0d\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb": {"artifactName": "react_codegen_lottiereactnative", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNGoogleSignInCGen::@337b7b353bd94a4215c0": {"artifactName": "react_codegen_RNGoogleSignInCGen", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnpicker::@e8bb2e9e833f47d0d516": {"artifactName": "react_codegen_rnpicker", "abi": "arm64-v8a", "output": "D:\\app\\StyleApp\\android\\app\\build\\intermediates\\cxx\\Debug\\4h302r3f\\obj\\arm64-v8a\\libreact_codegen_rnpicker.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\abe814a713e512e574ccc0dc7022ef0d\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\abe814a713e512e574ccc0dc7022ef0d\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bccd564c4f2a425ba7527fe4eb35e1f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}}}