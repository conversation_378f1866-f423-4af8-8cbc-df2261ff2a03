import { useState, useEffect, useRef } from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, ScrollView, Linking, ActivityIndicator, Share, Dimensions, Alert, TextInput, KeyboardAvoidingView, Platform, FlatList, Keyboard } from 'react-native';
import { doc, getDoc, updateDoc, increment, collection, addDoc, getDocs, query, where, deleteDoc, serverTimestamp, orderBy, arrayUnion, arrayRemove } from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import CollectionSelectionModal from '../components/CollectionSelectionModal';
import { getHighQualityImageUrl } from '../utils/imageUtils';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const ItemDetailsScreen = ({ route, navigation }) => {
  const { itemId } = route.params;
  const [item, setItem] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isInCart, setIsInCart] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [uploaderInfo, setUploaderInfo] = useState(null);
  const [isOwner, setIsOwner] = useState(false);
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [loadingComments, setLoadingComments] = useState(false);
  const [commentSortOption, setCommentSortOption] = useState('newest'); // 'newest', 'oldest', 'mostLiked'
  const [replyingTo, setReplyingTo] = useState(null); // Comment ID being replied to
  const [editingComment, setEditingComment] = useState(null); // Comment being edited
  const [editCommentText, setEditCommentText] = useState('');
  const [showSortOptions, setShowSortOptions] = useState(false);
  const [showCollectionModal, setShowCollectionModal] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0); // Track active image in carousel
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const commentInputRef = useRef(null);
  const flatListRef = useRef(null);
  const currentUserId = auth.currentUser?.uid;
  const insets = useSafeAreaInsets();
  useEffect(() => {
    fetchItemDetails();
    if (currentUserId) {
      checkWishlistStatus();
      checkLikeStatus();
      checkCartStatus();
    }
    fetchComments(true); // Initial load, show loading indicator

    // Add keyboard event listeners with Android-specific handling
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === 'android' ? 'keyboardDidShow' : 'keyboardWillShow',
      (event) => {
        setKeyboardVisible(true);
        if (Platform.OS === 'android') {
          setKeyboardHeight(event.endCoordinates.height);
          // Add a slight delay for Android to properly position elements
          setTimeout(() => {
            requestAnimationFrame(() => {
              // Force layout update after keyboard appears
            });
          }, 50);
        }
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === 'android' ? 'keyboardDidHide' : 'keyboardWillHide',
      () => {
        if (Platform.OS === 'android') {
          // Add a slight delay for Android to properly handle dismissal
          setTimeout(() => {
            setKeyboardVisible(false);
            setKeyboardHeight(0);
          }, 50);
        } else {
          setKeyboardVisible(false);
          setKeyboardHeight(0);
        }
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, [itemId, currentUserId]);

  useEffect(() => {
    if (item) {
      navigation.setOptions({
        headerTransparent: true,
        headerTitle: '',
        headerLeft: Platform.OS === 'android'
          ? () => null
          : () => (
            <View style={{ paddingTop: insets.top, backgroundColor: 'transparent' }}>
              <TouchableOpacity onPress={() => navigation.goBack()} style={styles.headerButton}>
                <Ionicons name="chevron-down-outline" size={32} color="#fff" />
              </TouchableOpacity>
            </View>
          ),
        headerBackground: () => (
          <View style={styles.headerBackground} />
        ),
      });
    }
  }, [item, navigation, insets.top]);

  const fetchItemDetails = async () => {
    setLoading(true);
    try {
      const itemDoc = await getDoc(doc(db, 'clothingItems', itemId));

      if (itemDoc.exists()) {
        const itemData = {
          id: itemDoc.id,
          ...itemDoc.data()
        };
        setItem(itemData);
        setLikeCount(itemData.likeCount || 0);

        // Check if current user is the owner of this item
        setIsOwner(currentUserId && itemData.userId === currentUserId);

        // Fetch uploader info
        if (itemData.uploaderId) {
          try {
            const uploaderDocRef = doc(db, 'users', itemData.uploaderId);
            const uploaderDoc = await getDoc(uploaderDocRef);

            if (uploaderDoc.exists()) {
              const uploaderData = uploaderDoc.data();
              setUploaderInfo({
                id: uploaderDoc.id,
                name: uploaderData.name || 'User',
                displayName: uploaderData.name || 'User',
                profilePictureUrl: uploaderData.profilePictureUrl || null,
                photoURL: uploaderData.profilePictureUrl || null,
                isSeller: uploaderData.isSeller || false,
                ...uploaderData
              });
            } else {
              // If user document doesn't exist, create a basic profile
              setUploaderInfo({
                id: itemData.uploaderId,
                name: 'User',
                displayName: 'User',
                profilePictureUrl: null,
                photoURL: null,
                isSeller: false
              });
            }
          } catch (error) {
            console.error('Error fetching uploader info:', error);
            setUploaderInfo({
              id: itemData.uploaderId,
              name: 'User',
              displayName: 'User',
              profilePictureUrl: null,
              photoURL: null,
              isSeller: false
            });
          }
        }
      } else {
        alert('Item not found');
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error fetching item details:', error);
      alert('Error loading item details');
    } finally {
      setLoading(false);
    }
  };

  const checkWishlistStatus = async () => {
    if (!auth.currentUser) return;

    try {
      const wishlistQuery = query(
        collection(db, 'users', auth.currentUser.uid, 'wishlist'),
        where('itemId', '==', itemId)
      );

      const querySnapshot = await getDocs(wishlistQuery);
      setIsWishlisted(!querySnapshot.empty);
    } catch (error) {
      console.error('Error checking wishlist status:', error);
    }
  };

  const checkLikeStatus = async () => {
    if (!currentUserId) return;
    try {
      const likeQuery = query(
        collection(db, 'users', currentUserId, 'likes'),
        where('itemId', '==', itemId)
      );
      const likeSnapshot = await getDocs(likeQuery);
      setIsLiked(!likeSnapshot.empty);
    } catch (error) {
      console.error('Error checking like status:', error);
    }
  };

  const checkCartStatus = async () => {
    if (!currentUserId) return;
    try {
      const cartQuery = query(
        collection(db, 'users', currentUserId, 'cart'),
        where('itemId', '==', itemId)
      );
      const cartSnapshot = await getDocs(cartQuery);
      setIsInCart(!cartSnapshot.empty);
    } catch (error) {
      console.error('Error checking cart status:', error);
    }
  };

  const toggleWishlist = async () => {
    if (!auth.currentUser) {
      navigation.navigate('Auth');
      return;
    }

    const currentUserId = auth.currentUser.uid; // Ensure we have currentUserId

    try {
      if (isWishlisted) {
        // If already wishlisted, remove from all collections it might be in
        const wishlistQuery = query(
          collection(db, 'users', currentUserId, 'wishlist'), // Corrected: Added comma here
          where('itemId', '==', itemId)
        );

        const querySnapshot = await getDocs(wishlistQuery);
        let saveCountDecremented = false; // Flag to ensure saveCount is decremented only once

        for (const wishlistDoc of querySnapshot.docs) {
          const wishlistData = wishlistDoc.data();
          const collectionId = wishlistData.collectionId;

          // Delete the wishlist entry
          await deleteDoc(doc(db, 'users', currentUserId, 'wishlist', wishlistDoc.id));
          console.log(`[ItemDetailsScreen] Removed item ${itemId} from wishlist doc ${wishlistDoc.id} (collection: ${collectionId})`);          // Decrement itemCount in the specific collection
          if (collectionId) {
            const collectionRef = doc(db, 'users', currentUserId, 'collections', collectionId);
            try {
              // Get the current collection to check if this was the last item
              const collectionDoc = await getDoc(collectionRef);

              if (collectionDoc.exists()) {
                const currentItemCount = collectionDoc.data().itemCount || 0;

                // If this was the last item, clear the latestItemImageUrl
                if (currentItemCount <= 1) {
                  await updateDoc(collectionRef, {
                    itemCount: 0,
                    latestItemImageUrl: null
                  });
                } else {
                  // Otherwise just decrement the count
                  await updateDoc(collectionRef, {
                    itemCount: increment(-1)
                  });

                  // If this was the item used as the collection thumbnail, update it
                  if (collectionDoc.data().latestItemImageUrl === item?.imageUrl) {
                    // We need to find another item in this collection to use as thumbnail
                    // Query for other items in this collection
                    const otherItemsQuery = query(
                      collection(db, 'users', currentUserId, 'wishlist'),
                      where('collectionId', '==', collectionId),
                      where('itemId', '!=', itemId)
                    );
                    const otherItemsSnapshot = await getDocs(otherItemsQuery);

                    if (!otherItemsSnapshot.empty) {
                      // Get the first other item's details to use its image
                      const firstOtherItem = otherItemsSnapshot.docs[0].data();
                      const otherItemRef = doc(db, 'clothingItems', firstOtherItem.itemId);
                      const otherItemDoc = await getDoc(otherItemRef);

                      if (otherItemDoc.exists()) {
                        await updateDoc(collectionRef, {
                          latestItemImageUrl: otherItemDoc.data().imageUrl
                        });
                      }
                    }
                  }
                }
              }

              console.log(`[ItemDetailsScreen] Updated collection ${collectionId} after item removal`);
            } catch (e) {
              console.error(`[ItemDetailsScreen] Error updating collection ${collectionId}:`, e);
            }
          }

          // Decrement saveCount on the clothingItem (only once)
          if (!saveCountDecremented) {
            const itemRef = doc(db, 'clothingItems', itemId);
            try {
              await updateDoc(itemRef, { saveCount: increment(-1) });
              console.log(`[ItemDetailsScreen] Decremented saveCount for item ${itemId}`);
              saveCountDecremented = true;
            } catch (e) {
              console.error(`[ItemDetailsScreen] Error decrementing saveCount for item ${itemId}:`, e);
            }
          }
        }

        setIsWishlisted(false);
        // Potentially refresh UserWishlist if it's in the navigation stack and focused
        // This might require a more global state management or event system if direct refresh is needed.
        // For now, the UserWishlist screen will refresh on focus.

      } else {
        // If not wishlisted, show collection selection modal
        setShowCollectionModal(true);
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      alert('Failed to update wishlist');
    }
  };

  // Handle collection selection
  const handleSelectCollection = async (selectedCollection, itemId) => {
    if (!currentUserId || !itemId) return;

    try {
      // First check if the item is already in this collection to avoid duplicates
      const userWishlistRef = collection(db, 'users', currentUserId, 'wishlist');
      const existingQuery = query(
        userWishlistRef,
        where('itemId', '==', itemId),
        where('collectionId', '==', selectedCollection.id)
      );
      const existingSnapshot = await getDocs(existingQuery);

      // Skip if already in this collection
      if (!existingSnapshot.empty) {
        console.log(`Item ${itemId} already exists in collection ${selectedCollection.id}, skipping`);
        setIsWishlisted(true);
        setShowCollectionModal(false);
        return;
      }

      // Add to the selected collection
      await addDoc(userWishlistRef, {
        itemId: itemId,
        addedAt: new Date(),
        collectionId: selectedCollection.id
      });

      // Update collection item count
      const collectionRef = doc(db, 'users', currentUserId, 'collections', selectedCollection.id);

      // Update the collection with the latest item's image URL and item count
      await updateDoc(collectionRef, {
        itemCount: increment(1),
        latestItemImageUrl: item.imageUrl // Store the latest item's image URL
      });

      // Update the saveCount in the clothingItems collection
      const itemRef = doc(db, 'clothingItems', itemId);
      await updateDoc(itemRef, {
        saveCount: increment(1)
      });
      console.log(`Updated saveCount for item ${itemId}`);

      setIsWishlisted(true);
      setShowCollectionModal(false);
      console.log(`Item ${itemId} added to collection ${selectedCollection.name}`);
    } catch (error) {
      console.error('Error adding to collection:', error);
      Alert.alert('Error', 'Failed to add to collection');
    }
  };

  const toggleLike = async () => {
    if (!currentUserId) {
      navigation.navigate('Auth');
      return;
    }

    const itemRef = doc(db, 'clothingItems', itemId);
    const userLikesRef = collection(db, 'users', currentUserId, 'likes');
    const likeQuery = query(userLikesRef, where('itemId', '==', itemId));

    try {
      const likeSnapshot = await getDocs(likeQuery);

      if (likeSnapshot.empty) {
        await addDoc(userLikesRef, { itemId: itemId, timestamp: new Date() });
        await updateDoc(itemRef, { likeCount: increment(1) });
        setIsLiked(true);
        setLikeCount(likeCount + 1);
      } else {
        likeSnapshot.forEach(async (document) => {
          await deleteDoc(doc(db, 'users', currentUserId, 'likes', document.id));
        });
        await updateDoc(itemRef, { likeCount: increment(-1) });
        setIsLiked(false);
        setLikeCount(likeCount - 1);
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      alert('Failed to update like status');
    }
  };

  const handleBuyNow = () => {
    if (item.purchaseUrl) {
      Linking.openURL(item.purchaseUrl);
    } else {
      alert('No purchase link available');
    }
  };

  const addToCart = async () => {
    if (!currentUserId) {
      navigation.navigate('Auth');
      return;
    }

    try {
      if (isInCart) {
        // If already in cart, remove it
        const cartQuery = query(
          collection(db, 'users', currentUserId, 'cart'),
          where('itemId', '==', itemId)
        );

        const querySnapshot = await getDocs(cartQuery);
        querySnapshot.forEach(async (document) => {
          await deleteDoc(doc(db, 'users', currentUserId, 'cart', document.id));
        });

        setIsInCart(false);
        Alert.alert('Success', 'Item removed from cart');

        // Refresh cart count in ClothingFeedScreen by triggering a cart update event
        // The onSnapshot listener in ClothingFeedScreen will automatically detect this change
        // This will trigger the cart listener in ClothingFeedScreen
      } else {
        // Add to cart
        await addDoc(collection(db, 'users', currentUserId, 'cart'), {
          itemId: itemId,
          addedAt: new Date(),
          title: item.title || 'Untitled Item',
          category: item.category || 'Uncategorized',
          brand: item.brand || 'Unknown Brand',
          imageUrl: item.imageUrl || null,
          size: item.size || 'One Size',
          price: item.price || 0,
          quantity: 1
        });

        // Update the viewCount in the clothingItems collection
        const itemRef = doc(db, 'clothingItems', itemId);
        await updateDoc(itemRef, {
          viewCount: increment(1)
        });
        console.log(`Updated viewCount for item ${itemId}`);

        setIsInCart(true);
        Alert.alert('Success', 'Item added to cart');

        // Store as interacted item
        try {
          await addDoc(collection(db, 'users', currentUserId, 'interactedItems'), {
            itemId: itemId,
            interactedAt: new Date(),
            interactionType: 'cart'
          });
        } catch (error) {
          console.error('Error storing interacted item:', error);
        }
      }
    } catch (error) {
      console.error('Error updating cart:', error);
      Alert.alert('Error', 'Failed to update cart');
    }
  };

  const handleNavigateToUploaderProfile = () => {
    if (item?.uploaderId) {
      navigation.navigate('UserProfile', { userId: item.uploaderId });
    } else {
      alert('Uploader information not available.');
    }
  };

  const shareItem = async () => {
    try {
      await Share.share({
        message: `Check out this ${item.title || item.category} on SwipeSense! ${item.purchaseUrl || ''}`,
        title: `SwipeSense - ${item.title || item.category}`
      });
    } catch (error) {
      console.error('Error sharing item:', error);
    }
  };

  const handleEditItem = () => {
    navigation.navigate('EditItem', { itemId: itemId });
  };

  const handleDeleteItem = () => {
    Alert.alert(
      'Delete Item',
      'Are you sure you want to delete this item? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            try {
              setLoading(true);

              deleteDoc(doc(db, 'clothingItems', itemId))
                .then(() => {
                  Alert.alert(
                    'Success',
                    'Item deleted successfully',
                    [{ text: 'OK', onPress: () => navigation.goBack() }]
                  );
                })
                .catch((error) => {
                  console.error('Error deleting item:', error);
                  Alert.alert('Error', 'Failed to delete item');
                  setLoading(false);
                });
            } catch (error) {
              console.error('Error in delete handler:', error);
              Alert.alert('Error', 'An unexpected error occurred');
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const fetchComments = async (showLoading = true) => {
    if (!itemId) return;

    // Only show loading indicator for initial load, not for background refreshes
    if (showLoading) {
      setLoadingComments(true);
    }

    try {
      const commentsRef = collection(db, 'clothingItems', itemId, 'comments');

      // Set up query based on sort option
      let q;
      switch (commentSortOption) {
        case 'oldest':
          q = query(commentsRef, orderBy('timestamp', 'asc'));
          break;
        case 'mostLiked':
          q = query(commentsRef, orderBy('likeCount', 'desc'), orderBy('timestamp', 'desc'));
          break;
        case 'newest':
        default:
          q = query(commentsRef, orderBy('timestamp', 'desc'));
          break;
      }

      const querySnapshot = await getDocs(q);

      const sellerComments = [];
      const regularComments = [];

      for (const commentDoc of querySnapshot.docs) {
        const commentData = commentDoc.data();
        // Set default values for new fields if they don't exist
        const processedComment = {
          id: commentDoc.id,
          ...commentData,
          likeCount: commentData.likeCount || 0,
          likes: commentData.likes || [],
          edited: commentData.edited || false,
          replies: commentData.replies || [],
        };

        // Fetch user info for each comment
        if (commentData.userId) {
          try {
            const userDocRef = doc(db, 'users', commentData.userId);
            const userDoc = await getDoc(userDocRef);

            if (userDoc.exists()) {
              const userData = userDoc.data();
              const isSeller = userData.isSeller || false;

              const commentWithUser = {
                ...processedComment,
                user: {
                  id: userDoc.id,
                  displayName: userData.name || 'User Name',
                  photoURL: userData.profilePictureUrl || null,
                  isSeller: isSeller,
                  ...userData
                }
              };

              // Separate seller comments from regular comments
              if (isSeller) {
                sellerComments.push(commentWithUser);
              } else {
                regularComments.push(commentWithUser);
              }
            } else {
              // If user document doesn't exist, create a basic profile
              regularComments.push({
                ...processedComment,
                user: {
                  id: commentData.userId,
                  displayName: 'User ' + commentData.userId.substring(0, 5),
                  photoURL: null,
                  isSeller: false
                }
              });
            }
          } catch (error) {
            console.error('Error fetching comment user info:', error);
            regularComments.push({
              ...processedComment,
              user: {
                id: commentData.userId,
                displayName: 'User ' + commentData.userId.substring(0, 5),
                photoURL: null,
                isSeller: false
              }
            });
          }
        } else {
          // Handle comments without userId
          regularComments.push({
            ...processedComment,
            user: {
              displayName: 'Anonymous',
              photoURL: null,
              isSeller: false
            }
          });
        }
      }

      // Fetch replies for each comment
      const fetchReplies = async (commentId) => {
        const repliesRef = collection(db, 'clothingItems', itemId, 'comments', commentId, 'replies');
        const repliesQuery = query(repliesRef, orderBy('timestamp', 'asc'));
        const repliesSnapshot = await getDocs(repliesQuery);

        const repliesData = [];
        for (const replyDoc of repliesSnapshot.docs) {
          const replyData = replyDoc.data();

          // Fetch user info for the reply
          if (replyData.userId) {
            try {
              const userDocRef = doc(db, 'users', replyData.userId);
              const userDoc = await getDoc(userDocRef);

              if (userDoc.exists()) {
                const userData = userDoc.data();
                repliesData.push({
                  id: replyDoc.id,
                  ...replyData,
                  likeCount: replyData.likeCount || 0,
                  likes: replyData.likes || [],
                  user: {
                    id: userDoc.id,
                    displayName: userData.name || 'User Name',
                    photoURL: userData.profilePictureUrl || null,
                    isSeller: userData.isSeller || false,
                    ...userData
                  }
                });
              } else {
                repliesData.push({
                  id: replyDoc.id,
                  ...replyData,
                  likeCount: replyData.likeCount || 0,
                  likes: replyData.likes || [],
                  user: {
                    id: replyData.userId,
                    displayName: 'User ' + replyData.userId.substring(0, 5),
                    photoURL: null,
                    isSeller: false
                  }
                });
              }
            } catch (error) {
              console.error('Error fetching reply user info:', error);
              repliesData.push({
                id: replyDoc.id,
                ...replyData,
                likeCount: replyData.likeCount || 0,
                likes: replyData.likes || [],
                user: {
                  id: replyData.userId,
                  displayName: 'User ' + replyData.userId.substring(0, 5),
                  photoURL: null,
                  isSeller: false
                }
              });
            }
          }
        }

        return repliesData;
      };

      // Fetch replies for all comments
      const allComments = [...sellerComments, ...regularComments];
      for (let i = 0; i < allComments.length; i++) {
        const replies = await fetchReplies(allComments[i].id);
        allComments[i].replies = replies;
      }

      // Combine seller comments and regular comments, with seller comments at the top
      setComments([...sellerComments, ...regularComments]);
    } catch (error) {
      console.error('Error fetching comments:', error);
    } finally {
      setLoadingComments(false);
    }
  };
  const addComment = async () => {
    if (!currentUserId) {
      navigation.navigate('Auth');
      return;
    }

    if (!newComment.trim()) {
      return;
    }

    // Dismiss keyboard immediately for better Android experience
    if (Platform.OS === 'android') {
      Keyboard.dismiss();
    }

    // Get current user info for optimistic update
    const currentUserInfo = {
      id: currentUserId,
      displayName: auth.currentUser?.displayName || 'User',
      photoURL: auth.currentUser?.photoURL || null,
      isSeller: false // Default value, will be updated when fetched from server
    };

    // Create a temporary ID for optimistic update
    const tempId = 'temp-' + Date.now();
    const commentText = newComment.trim();

    // Clear input immediately for better UX
    setNewComment('');

    try {
      // If replying to a comment
      if (replyingTo) {
        // Find the parent comment
        const parentComment = comments.find(comment => comment.id === replyingTo);

        if (parentComment) {
          // Create optimistic reply
          const optimisticReply = {
            id: tempId,
            userId: currentUserId,
            text: commentText,
            timestamp: new Date(), // Use client timestamp for immediate display
            likeCount: 0,
            likes: [],
            user: currentUserInfo,
            isOptimistic: true // Flag to identify optimistic updates
          };

          // Update local state immediately
          const updatedComments = comments.map(comment => {
            if (comment.id === replyingTo) {
              return {
                ...comment,
                replies: [...(comment.replies || []), optimisticReply]
              };
            }
            return comment;
          });

          setComments(updatedComments);
          setReplyingTo(null);

          // Then update server
          const replyRef = collection(db, 'clothingItems', itemId, 'comments', replyingTo, 'replies');
          await addDoc(replyRef, {
            userId: currentUserId,
            text: commentText,
            timestamp: serverTimestamp(),
            likeCount: 0,
            likes: []
          });

          // Fetch updated comments in the background without showing loading indicator
          fetchComments(false);
        } else {
          // Parent comment not found, fallback to regular fetch
          const replyRef = collection(db, 'clothingItems', itemId, 'comments', replyingTo, 'replies');
          await addDoc(replyRef, {
            userId: currentUserId,
            text: commentText,
            timestamp: serverTimestamp(),
            likeCount: 0,
            likes: []
          });

          setReplyingTo(null);
          fetchComments(false);
        }
      } else {
        // Adding a new top-level comment
        // Create optimistic comment
        const optimisticComment = {
          id: tempId,
          userId: currentUserId,
          text: commentText,
          timestamp: new Date(), // Use client timestamp for immediate display
          likeCount: 0,
          likes: [],
          edited: false,
          replies: [],
          user: currentUserInfo,
          isOptimistic: true // Flag to identify optimistic updates
        };

        // Update local state immediately
        setComments([optimisticComment, ...comments]);

        // Then update server
        const commentsRef = collection(db, 'clothingItems', itemId, 'comments');
        await addDoc(commentsRef, {
          userId: currentUserId,
          text: commentText,
          timestamp: serverTimestamp(),
          likeCount: 0,
          likes: [],
          edited: false
        });

        // Fetch updated comments in the background without showing loading indicator
        fetchComments(false);
      }
    } catch (error) {
      console.error('Error adding comment:', error);
      Alert.alert('Error', 'Failed to add comment');
      // Remove optimistic update on error
      if (replyingTo) {
        const updatedComments = comments.map(comment => {
          if (comment.id === replyingTo) {
            return {
              ...comment,
              replies: (comment.replies || []).filter(reply => reply.id !== tempId)
            };
          }
          return comment;
        });
        setComments(updatedComments);
      } else {
        setComments(comments.filter(comment => comment.id !== tempId));
      }
    }
  };

  const editComment = async () => {
    if (!editingComment || !editCommentText.trim()) {
      setEditingComment(null);
      setEditCommentText('');
      return;
    }

    const editedText = editCommentText.trim();

    // Store original values in case we need to revert
    const originalText = editingComment.text;

    try {
      // Update local state immediately for better UX
      if (editingComment.parentId) {
        // It's a reply - update in the local state
        const updatedComments = comments.map(comment => {
          if (comment.id === editingComment.parentId) {
            return {
              ...comment,
              replies: (comment.replies || []).map(reply => {
                if (reply.id === editingComment.id) {
                  return {
                    ...reply,
                    text: editedText,
                    edited: true,
                    lastEditTimestamp: new Date() // Use client timestamp for immediate display
                  };
                }
                return reply;
              })
            };
          }
          return comment;
        });

        setComments(updatedComments);

        // Then update server
        const replyRef = doc(db, 'clothingItems', itemId, 'comments', editingComment.parentId, 'replies', editingComment.id);
        await updateDoc(replyRef, {
          text: editedText,
          edited: true,
          lastEditTimestamp: serverTimestamp()
        });
      } else {
        // It's a top-level comment - update in the local state
        const updatedComments = comments.map(comment => {
          if (comment.id === editingComment.id) {
            return {
              ...comment,
              text: editedText,
              edited: true,
              lastEditTimestamp: new Date() // Use client timestamp for immediate display
            };
          }
          return comment;
        });

        setComments(updatedComments);

        // Then update server
        const commentRef = doc(db, 'clothingItems', itemId, 'comments', editingComment.id);
        await updateDoc(commentRef, {
          text: editedText,
          edited: true,
          lastEditTimestamp: serverTimestamp()
        });
      }

      // Clear editing state
      setEditingComment(null);
      setEditCommentText('');
      Keyboard.dismiss();

      // Fetch updated comments in the background to ensure consistency without showing loading indicator
      fetchComments(false);
    } catch (error) {
      console.error('Error editing comment:', error);
      Alert.alert('Error', 'Failed to edit comment');

      // Revert to original state on error
      if (editingComment.parentId) {
        const updatedComments = comments.map(comment => {
          if (comment.id === editingComment.parentId) {
            return {
              ...comment,
              replies: (comment.replies || []).map(reply => {
                if (reply.id === editingComment.id) {
                  return {
                    ...reply,
                    text: originalText,
                    edited: editingComment.edited || false,
                    lastEditTimestamp: editingComment.lastEditTimestamp
                  };
                }
                return reply;
              })
            };
          }
          return comment;
        });

        setComments(updatedComments);
      } else {
        const updatedComments = comments.map(comment => {
          if (comment.id === editingComment.id) {
            return {
              ...comment,
              text: originalText,
              edited: editingComment.edited || false,
              lastEditTimestamp: editingComment.lastEditTimestamp
            };
          }
          return comment;
        });

        setComments(updatedComments);
      }
    }
  };

  const deleteComment = async (commentId, isReply = false, parentId = null) => {
    // Store the current comments state for potential rollback
    const previousComments = [...comments];

    try {
      // Update local state immediately for better UX
      if (isReply && parentId) {
        // Remove reply from local state
        const updatedComments = comments.map(comment => {
          if (comment.id === parentId) {
            return {
              ...comment,
              replies: (comment.replies || []).filter(reply => reply.id !== commentId)
            };
          }
          return comment;
        });

        setComments(updatedComments);

        // Then delete from server
        await deleteDoc(doc(db, 'clothingItems', itemId, 'comments', parentId, 'replies', commentId));
      } else {
        // Remove comment from local state
        const updatedComments = comments.filter(comment => comment.id !== commentId);
        setComments(updatedComments);

        // Then delete from server
        const commentRef = doc(db, 'clothingItems', itemId, 'comments', commentId);

        // Delete all replies first
        const repliesRef = collection(db, 'clothingItems', itemId, 'comments', commentId, 'replies');
        const repliesSnapshot = await getDocs(repliesRef);

        const deletePromises = repliesSnapshot.docs.map(replyDoc =>
          deleteDoc(doc(db, 'clothingItems', itemId, 'comments', commentId, 'replies', replyDoc.id))
        );

        await Promise.all(deletePromises);

        // Then delete the comment itself
        await deleteDoc(commentRef);
      }

      // Fetch updated comments in the background to ensure consistency without showing loading indicator
      fetchComments(false);
    } catch (error) {
      console.error('Error deleting comment:', error);
      Alert.alert('Error', 'Failed to delete comment');

      // Restore previous state on error
      setComments(previousComments);
    }
  };

  const likeComment = async (commentId, isReply = false, parentId = null) => {
    if (!currentUserId) {
      navigation.navigate('Auth');
      return;
    }

    // Store the current comments state for potential rollback
    const previousComments = [...comments];

    try {
      // Find the comment in local state
      let commentToUpdate;
      let isLiked = false;

      if (isReply && parentId) {
        // It's a reply
        const parentComment = comments.find(c => c.id === parentId);
        if (parentComment && parentComment.replies) {
          commentToUpdate = parentComment.replies.find(r => r.id === commentId);
        }
      } else {
        // It's a top-level comment
        commentToUpdate = comments.find(c => c.id === commentId);
      }

      if (!commentToUpdate) {
        console.error('Comment not found in local state');
        // Fallback to server-side update
        let commentRef;
        if (isReply && parentId) {
          commentRef = doc(db, 'clothingItems', itemId, 'comments', parentId, 'replies', commentId);
        } else {
          commentRef = doc(db, 'clothingItems', itemId, 'comments', commentId);
        }

        const commentDoc = await getDoc(commentRef);
        if (!commentDoc.exists()) {
          console.error('Comment not found on server');
          return;
        }

        const commentData = commentDoc.data();
        const likes = commentData.likes || [];
        const likeCount = commentData.likeCount || 0;

        if (likes.includes(currentUserId)) {
          await updateDoc(commentRef, {
            likes: arrayRemove(currentUserId),
            likeCount: likeCount - 1
          });
        } else {
          await updateDoc(commentRef, {
            likes: arrayUnion(currentUserId),
            likeCount: likeCount + 1
          });
        }

        fetchComments(false);
        return;
      }

      // Check if user already liked this comment
      const likes = commentToUpdate.likes || [];
      const likeCount = commentToUpdate.likeCount || 0;
      isLiked = likes.includes(currentUserId);

      // Update local state immediately
      if (isReply && parentId) {
        // Update reply in local state
        const updatedComments = comments.map(comment => {
          if (comment.id === parentId) {
            return {
              ...comment,
              replies: (comment.replies || []).map(reply => {
                if (reply.id === commentId) {
                  return {
                    ...reply,
                    likes: isLiked
                      ? likes.filter(id => id !== currentUserId)
                      : [...likes, currentUserId],
                    likeCount: isLiked ? likeCount - 1 : likeCount + 1
                  };
                }
                return reply;
              })
            };
          }
          return comment;
        });

        setComments(updatedComments);
      } else {
        // Update comment in local state
        const updatedComments = comments.map(comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              likes: isLiked
                ? likes.filter(id => id !== currentUserId)
                : [...likes, currentUserId],
              likeCount: isLiked ? likeCount - 1 : likeCount + 1
            };
          }
          return comment;
        });

        setComments(updatedComments);
      }

      // Then update server
      let commentRef;
      if (isReply && parentId) {
        commentRef = doc(db, 'clothingItems', itemId, 'comments', parentId, 'replies', commentId);
      } else {
        commentRef = doc(db, 'clothingItems', itemId, 'comments', commentId);
      }

      if (isLiked) {
        // User already liked this comment, so unlike it
        await updateDoc(commentRef, {
          likes: arrayRemove(currentUserId),
          likeCount: likeCount - 1
        });
      } else {
        // User hasn't liked this comment yet, so like it
        await updateDoc(commentRef, {
          likes: arrayUnion(currentUserId),
          likeCount: likeCount + 1
        });
      }

      // Fetch updated comments in the background to ensure consistency without showing loading indicator
      fetchComments(false);
    } catch (error) {
      console.error('Error liking comment:', error);
      Alert.alert('Error', 'Failed to update like status');

      // Restore previous state on error
      setComments(previousComments);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF6B6B" />
      </SafeAreaView>
    );
  }

  if (!item) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Text style={styles.errorText}>Item not found</Text>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backLink}>Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }
  return (
    <View style={{ flex: 1 }}>
      {Platform.OS === 'android' && keyboardVisible && (
        <TouchableOpacity
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: keyboardHeight,
            backgroundColor: 'transparent',
            zIndex: 1000 // Ensure it's above other elements but below comment input
          }}
          activeOpacity={1}
          onPress={() => {
            // Dismiss keyboard and handle repositioning
            Keyboard.dismiss();
            // Force a layout update after keyboard dismissal
            requestAnimationFrame(() => {
              setKeyboardVisible(false);
              setKeyboardHeight(0);
            });
          }}
        />
      )}

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : null}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 0}
      >
        <ScrollView
          style={[
            styles.container,
            { zIndex: 0 },
            Platform.OS === 'android' && { paddingTop: insets.top }
          ]}
          contentContainerStyle={{
            paddingBottom: Platform.OS === 'android' ? 100 : (60 + (insets.bottom > 0 ? insets.bottom : 0))
          }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          onScrollBeginDrag={() => {
            if (Platform.OS === 'android') {
              Keyboard.dismiss();
            }
          }}
        >
          {/* Image Carousel */}
          {item.imageUrls && item.imageUrls.length > 0 ? (
            <View style={styles.carouselContainer}>
              <FlatList
                ref={flatListRef}
                data={item.imageUrls}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                keyExtractor={(_, index) => `image_${index}`}
                onMomentumScrollEnd={(event) => {
                  const newIndex = Math.floor(event.nativeEvent.contentOffset.x / SCREEN_WIDTH);
                  setActiveImageIndex(newIndex);
                }}
                renderItem={({ item: imageUrl }) => (
                  <Image
                    source={{ uri: getHighQualityImageUrl(imageUrl) || 'https://via.placeholder.com/400x500?text=No+Image' }}
                    style={styles.carouselImage}
                    resizeMode="cover"
                  />
                )}
              />

              {/* Pagination Indicators */}
              {item.imageUrls.length > 1 && (
                <View style={styles.paginationContainer}>
                  {item.imageUrls.map((_, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.paginationDot,
                        index === activeImageIndex && styles.paginationDotActive
                      ]}
                      onPress={() => {
                        flatListRef.current?.scrollToOffset({
                          offset: index * SCREEN_WIDTH,
                          animated: true
                        });
                        setActiveImageIndex(index);
                      }}
                    />
                  ))}
                </View>
              )}
            </View>
          ) : (
            // Fallback to single image if no imageUrls array
            <Image
              source={{ uri: getHighQualityImageUrl(item.imageUrl) || 'https://via.placeholder.com/400x500?text=No+Image' }}
              style={styles.itemImage}
              resizeMode="cover"
            />
          )}

          <View style={styles.contentContainer}>
            {isOwner && (
              <View style={styles.ownerActionsContainer}>
                <TouchableOpacity onPress={handleEditItem} style={styles.ownerActionButton}>
                  <Ionicons name="create-outline" size={20} color="#fff" />
                  <Text style={styles.ownerActionText}>Edit</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={handleDeleteItem} style={[styles.ownerActionButton, styles.deleteButton]}>
                  <Ionicons name="trash-outline" size={20} color="#fff" />
                  <Text style={styles.ownerActionText}>Delete</Text>
                </TouchableOpacity>
              </View>
            )}
            <View style={styles.basicInfoSection}>
              <View style={styles.titlePriceRow}>
                <View style={styles.titleContainer}>
                  <Text style={styles.title}>{item.title || item.category}</Text>
                  {item.price && (
                    <Text style={styles.priceText}>₹{item.price.toFixed(2)}</Text>
                  )}
                </View>
                <TouchableOpacity onPress={shareItem} style={styles.shareButton}>
                  <Ionicons name="share-outline" size={24} color="#FF6B6B" />
                </TouchableOpacity>
              </View>
              <View style={styles.tagsRow}>
                {item.brand && <Text style={styles.tag}>{item.brand}</Text>}
                {item.size && <Text style={styles.tag}>{item.size}</Text>}
                <Text style={styles.tag}>{item.category}</Text>
              </View>
              <View style={styles.likeCountContainer}>
                <Ionicons name="heart" size={16} color="#FF6B6B" />
                <Text style={styles.likeCountText}>{likeCount} Likes</Text>
              </View>
            </View>

            {uploaderInfo && (
              <View style={styles.uploaderSection}>
                <TouchableOpacity style={styles.uploaderTouchable} onPress={handleNavigateToUploaderProfile}>
                  <Image
                    source={{
                      uri: getHighQualityImageUrl(uploaderInfo.profilePictureUrl || uploaderInfo.photoURL) || 'https://via.placeholder.com/150'
                    }}
                    style={styles.uploaderImage}
                  />
                  <View style={styles.uploaderTextContainer}>
                    <Text style={styles.uploaderLabel}>Uploaded by</Text>
                    <Text style={styles.uploaderName}>{uploaderInfo.name || 'User'}</Text>
                    {uploaderInfo.isSeller && (
                      <View style={styles.sellerBadge}>
                        <Ionicons name="storefront-outline" size={14} color="#FF6B6B" />
                        <Text style={styles.sellerBadgeText}>Seller</Text>
                      </View>
                    )}
                  </View>
                  <Ionicons name="chevron-forward" size={20} color="#ccc" />
                </TouchableOpacity>
              </View>
            )}

            <View style={styles.actionButtonsRow}>
              <TouchableOpacity
                style={[styles.actionButton, isLiked ? styles.likedButton : styles.likeButton]}
                onPress={toggleLike}
              >
                <Ionicons
                  name={isLiked ? "heart" : "heart-outline"}
                  size={24}
                  color={isLiked ? "#fff" : "#FF6B6B"}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, isWishlisted ? styles.wishlistedButton : styles.wishlistButton]}
                onPress={toggleWishlist}
              >
                <Ionicons
                  name={isWishlisted ? "bookmark" : "bookmark-outline"}
                  size={24}
                  color={isWishlisted ? "#fff" : "#4A90E2"}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, isInCart ? styles.inCartButton : styles.cartButton]}
                onPress={addToCart}
              >
                <Ionicons
                  name={isInCart ? "cart" : "cart-outline"}
                  size={24}
                  color={isInCart ? "#fff" : "#4CAF50"}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Description</Text>
              <Text style={styles.description}>{item.description || 'No description provided.'}</Text>
            </View>

            {item.purchaseUrl && (
              <View style={styles.buySection}>
                <TouchableOpacity
                  style={styles.buyButton}
                  onPress={handleBuyNow}
                >
                  <Text style={styles.buyButtonText}>View on Store</Text>
                  <Ionicons name="open-outline" size={20} color="#fff" style={{ marginLeft: 8 }} />
                </TouchableOpacity>
              </View>
            )}

            {/* Comments Section */}
            <View style={styles.commentsSection}>
              <View style={styles.commentsSectionHeader}>
                <Text style={styles.sectionTitle}>Comments</Text>

                {/* Sort options dropdown */}
                <TouchableOpacity
                  style={styles.sortButton}
                  onPress={() => setShowSortOptions(!showSortOptions)}
                >
                  <Text style={styles.sortButtonText}>
                    {commentSortOption === 'newest' ? 'Newest' :
                      commentSortOption === 'oldest' ? 'Oldest' : 'Most Liked'}
                  </Text>
                  <Ionicons name="chevron-down" size={16} color="#666" />
                </TouchableOpacity>
              </View>

              {/* Sort options dropdown menu */}
              {showSortOptions && (
                <View style={styles.sortOptionsContainer}>
                  <TouchableOpacity
                    style={[styles.sortOption, commentSortOption === 'newest' && styles.activeSortOption]}
                    onPress={() => {
                      setCommentSortOption('newest');
                      setShowSortOptions(false);
                      fetchComments(true); // Show loading when sorting changes
                    }}
                  >
                    <Text style={[styles.sortOptionText, commentSortOption === 'newest' && styles.activeSortOptionText]}>
                      Newest
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.sortOption, commentSortOption === 'oldest' && styles.activeSortOption]}
                    onPress={() => {
                      setCommentSortOption('oldest');
                      setShowSortOptions(false);
                      fetchComments(true); // Show loading when sorting changes
                    }}
                  >
                    <Text style={[styles.sortOptionText, commentSortOption === 'oldest' && styles.activeSortOptionText]}>
                      Oldest
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.sortOption, commentSortOption === 'mostLiked' && styles.activeSortOption]}
                    onPress={() => {
                      setCommentSortOption('mostLiked');
                      setShowSortOptions(false);
                      fetchComments(true); // Show loading when sorting changes
                    }}
                  >
                    <Text style={[styles.sortOptionText, commentSortOption === 'mostLiked' && styles.activeSortOptionText]}>
                      Most Liked
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              {loadingComments ? (
                <ActivityIndicator size="small" color="#FF6B6B" style={{ marginVertical: 20 }} />
              ) : comments.length === 0 ? (
                <Text style={styles.noCommentsText}>No comments yet. Be the first to comment!</Text>
              ) : (
                <FlatList
                  data={comments}
                  keyExtractor={(item) => item.id}
                  key="comments-list" // Add a key to ensure proper rendering
                  scrollEnabled={false}
                  renderItem={({ item }) => (
                    <View>
                      <View style={styles.commentItem}>
                        <TouchableOpacity
                          onPress={() => item.user?.id && navigation.navigate('UserProfile', { userId: item.user.id })}
                        >
                          <Image
                            source={{
                              uri: getHighQualityImageUrl(item.user?.profilePictureUrl || item.user?.photoURL) || 'https://via.placeholder.com/150'
                            }}
                            style={styles.commentUserImage}
                          />
                        </TouchableOpacity>
                        <View style={styles.commentContent}>
                          <View style={styles.commentUsernameContainer}>
                            <TouchableOpacity
                              onPress={() => item.user?.id && navigation.navigate('UserProfile', { userId: item.user.id })}
                            >
                              <Text style={styles.commentUsername}>{item.user?.name || 'User'}</Text>
                            </TouchableOpacity>
                            {item.user?.isSeller && (
                              <View style={styles.commentSellerBadge}>
                                <Ionicons name="storefront-outline" size={12} color="#FF6B6B" />
                                <Text style={styles.commentSellerBadgeText}>Seller</Text>
                              </View>
                            )}
                          </View>

                          {editingComment && editingComment.id === item.id && !editingComment.parentId ? (
                            <View style={styles.editCommentContainer}>
                              <TextInput
                                style={styles.editCommentInput}
                                value={editCommentText}
                                onChangeText={setEditCommentText}
                                multiline={true}
                                autoFocus={true}
                                maxLength={500}
                              />
                              <View style={styles.editCommentActions}>
                                <TouchableOpacity
                                  style={styles.editCommentCancel}
                                  onPress={() => {
                                    setEditingComment(null);
                                    setEditCommentText('');
                                    Keyboard.dismiss();
                                  }}
                                >
                                  <Text style={styles.editCommentCancelText}>Cancel</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                  style={styles.editCommentSave}
                                  onPress={editComment}
                                >
                                  <Text style={styles.editCommentSaveText}>Save</Text>
                                </TouchableOpacity>
                              </View>
                            </View>
                          ) : (
                            <>
                              <Text style={styles.commentText}>{item.text}</Text>
                              {item.edited && (
                                <Text style={styles.editedText}>(edited)</Text>
                              )}
                            </>
                          )}

                          <View style={styles.commentMetaContainer}>
                            <Text style={styles.commentTime}>
                              {item.timestamp && typeof item.timestamp.toDate === 'function'
                                ? new Date(item.timestamp.toDate()).toLocaleDateString()
                                : 'Just now'}
                            </Text>

                            <View style={styles.commentActions}>
                              {/* Like button */}
                              <TouchableOpacity
                                style={styles.commentAction}
                                onPress={() => likeComment(item.id)}
                              >
                                <Ionicons
                                  name={item.likes && item.likes.includes(currentUserId) ? "heart" : "heart-outline"}
                                  size={16}
                                  color={item.likes && item.likes.includes(currentUserId) ? "#FF6B6B" : "#666"}
                                />
                                {item.likeCount > 0 && (
                                  <Text style={styles.commentActionText}>{item.likeCount}</Text>
                                )}
                              </TouchableOpacity>

                              {/* Reply button */}
                              <TouchableOpacity
                                style={styles.commentAction}
                                onPress={() => {
                                  setReplyingTo(item.id);
                                  setNewComment(`@${item.user?.name || item.user?.displayName || 'User'} `);
                                  commentInputRef.current?.focus();
                                }}
                              >
                                <Ionicons name="return-down-forward-outline" size={16} color="#666" />
                                <Text style={styles.commentActionText}>Reply</Text>
                              </TouchableOpacity>

                              {/* Edit/Delete buttons (only for comment owner) */}
                              {currentUserId === item.userId && (
                                <View style={styles.ownerActions}>
                                  <TouchableOpacity
                                    style={styles.commentAction}
                                    onPress={() => {
                                      setEditingComment(item);
                                      setEditCommentText(item.text);
                                    }}
                                  >
                                    <Ionicons name="create-outline" size={16} color="#666" />
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                    style={styles.commentAction}
                                    onPress={() => {
                                      Alert.alert(
                                        'Delete Comment',
                                        'Are you sure you want to delete this comment?',
                                        [
                                          { text: 'Cancel', style: 'cancel' },
                                          {
                                            text: 'Delete',
                                            style: 'destructive',
                                            onPress: () => deleteComment(item.id)
                                          }
                                        ]
                                      );
                                    }}
                                  >
                                    <Ionicons name="trash-outline" size={16} color="#666" />
                                  </TouchableOpacity>
                                </View>
                              )}
                            </View>
                          </View>
                        </View>
                      </View>

                      {/* Replies section */}
                      {item.replies && item.replies.length > 0 && (
                        <View style={styles.repliesContainer}>
                          {item.replies.map(reply => (
                            <View key={reply.id} style={styles.replyItem}>
                              <TouchableOpacity
                                onPress={() => reply.user?.id && navigation.navigate('UserProfile', { userId: reply.user.id })}
                              >
                                <Image
                                  source={{
                                    uri: reply.user?.profilePictureUrl || reply.user?.photoURL || 'https://via.placeholder.com/150'
                                  }}
                                  style={styles.replyUserImage}
                                />
                              </TouchableOpacity>
                              <View style={styles.replyContent}>
                                <View style={styles.commentUsernameContainer}>
                                  <TouchableOpacity
                                    onPress={() => reply.user?.id && navigation.navigate('UserProfile', { userId: reply.user.id })}
                                  >
                                    <Text style={styles.commentUsername}>{reply.user?.name || 'User'}</Text>
                                  </TouchableOpacity>
                                  {reply.user?.isSeller && (
                                    <View style={styles.commentSellerBadge}>
                                      <Ionicons name="storefront-outline" size={12} color="#FF6B6B" />
                                      <Text style={styles.commentSellerBadgeText}>Seller</Text>
                                    </View>
                                  )}
                                </View>

                                {editingComment && editingComment.id === reply.id && editingComment.parentId === item.id ? (
                                  <View style={styles.editCommentContainer}>
                                    <TextInput
                                      style={styles.editCommentInput}
                                      value={editCommentText}
                                      onChangeText={setEditCommentText}
                                      multiline={true}
                                      autoFocus={true}
                                      maxLength={500}
                                    />
                                    <View style={styles.editCommentActions}>
                                      <TouchableOpacity
                                        style={styles.editCommentCancel}
                                        onPress={() => {
                                          setEditingComment(null);
                                          setEditCommentText('');
                                          Keyboard.dismiss();
                                        }}
                                      >
                                        <Text style={styles.editCommentCancelText}>Cancel</Text>
                                      </TouchableOpacity>
                                      <TouchableOpacity
                                        style={styles.editCommentSave}
                                        onPress={editComment}
                                      >
                                        <Text style={styles.editCommentSaveText}>Save</Text>
                                      </TouchableOpacity>
                                    </View>
                                  </View>
                                ) : (
                                  <>
                                    <Text style={styles.commentText}>{reply.text}</Text>
                                    {reply.edited && (
                                      <Text style={styles.editedText}>(edited)</Text>
                                    )}
                                  </>
                                )}

                                <View style={styles.commentMetaContainer}>
                                  <Text style={styles.commentTime}>
                                    {reply.timestamp && typeof reply.timestamp.toDate === 'function'
                                      ? new Date(reply.timestamp.toDate()).toLocaleDateString()
                                      : 'Just now'}
                                  </Text>

                                  <View style={styles.commentActions}>
                                    {/* Like button */}
                                    <TouchableOpacity
                                      style={styles.commentAction}
                                      onPress={() => likeComment(reply.id, true, item.id)}
                                    >
                                      <Ionicons
                                        name={reply.likes && reply.likes.includes(currentUserId) ? "heart" : "heart-outline"}
                                        size={16}
                                        color={reply.likes && reply.likes.includes(currentUserId) ? "#FF6B6B" : "#666"}
                                      />
                                      {reply.likeCount > 0 && (
                                        <Text style={styles.commentActionText}>{reply.likeCount}</Text>
                                      )}
                                    </TouchableOpacity>

                                    {/* Edit/Delete buttons (only for reply owner) */}
                                    {currentUserId === reply.userId && (
                                      <View style={styles.ownerActions}>
                                        <TouchableOpacity
                                          style={styles.commentAction}
                                          onPress={() => {
                                            setEditingComment({ ...reply, parentId: item.id });
                                            setEditCommentText(reply.text);
                                          }}
                                        >
                                          <Ionicons name="create-outline" size={16} color="#666" />
                                        </TouchableOpacity>
                                        <TouchableOpacity
                                          style={styles.commentAction}
                                          onPress={() => {
                                            Alert.alert(
                                              'Delete Reply',
                                              'Are you sure you want to delete this reply?',
                                              [
                                                { text: 'Cancel', style: 'cancel' },
                                                {
                                                  text: 'Delete',
                                                  style: 'destructive',
                                                  onPress: () => deleteComment(reply.id, true, item.id)
                                                }
                                              ]
                                            );
                                          }}
                                        >
                                          <Ionicons name="trash-outline" size={16} color="#666" />
                                        </TouchableOpacity>
                                      </View>
                                    )}
                                  </View>
                                </View>
                              </View>
                            </View>
                          ))}
                        </View>
                      )}

                      {/* Show a visual indicator when replying to this comment */}
                      {replyingTo === item.id && (
                        <View style={styles.replyingIndicator}>
                          <Text style={styles.replyingText}>
                            Replying to {item.user?.name || item.user?.displayName || 'User'}
                          </Text>
                          <TouchableOpacity onPress={() => setReplyingTo(null)}>
                            <Ionicons name="close-circle" size={18} color="#666" />
                          </TouchableOpacity>
                        </View>
                      )}
                    </View>
                  )}
                />
              )}
            </View>
          </View>
        </ScrollView>
        {/* Comment Input - Outside ScrollView but inside KeyboardAvoidingView */}
        {!editingComment && (
          <View style={[
            styles.commentInputContainer,
            { paddingBottom: Math.max(5, insets.bottom > 0 ? insets.bottom - 15 : 5) },
            Platform.OS === 'android' && keyboardVisible && {
              position: 'absolute',
              bottom: keyboardHeight, // Adjust based on keyboard height
              left: 0,
              right: 0,
              backgroundColor: '#fff',
              elevation: 5,
              borderTopWidth: 1,
              borderTopColor: '#ddd',
              zIndex: 2000, // Ensure it's always on top
            },
            Platform.OS === 'android' && !keyboardVisible && {
              position: 'absolute',
              bottom: 0, // Stick to bottom when keyboard is not visible
              left: 0,
              right: 0,
              backgroundColor: '#fff',
              elevation: 5,
              borderTopWidth: 1,
              borderTopColor: '#ddd',
              zIndex: 2000,
            }
          ]}>
            {replyingTo && (
              <View style={styles.replyingBadge}>
                <Text style={styles.replyingBadgeText}>Replying</Text>
                <TouchableOpacity
                  style={styles.cancelReplyButton}
                  onPress={() => {
                    setReplyingTo(null);
                    setNewComment('');
                  }}
                >
                  <Ionicons name="close-circle" size={16} color="#FF6B6B" />
                </TouchableOpacity>
              </View>
            )}
            <View style={styles.inputWrapper}>
              <TextInput
                ref={commentInputRef}
                style={[styles.commentInput, replyingTo && styles.replyingCommentInput]}
                placeholder={replyingTo ? "Write a reply..." : "Add a comment..."}
                value={newComment}
                onChangeText={setNewComment}
                multiline={true}
                numberOfLines={3}
                maxLength={500}
                autoCorrect={true}
                textAlignVertical="top"
                textAlign="left"
                scrollEnabled={true}
                showsVerticalScrollIndicator={true}
                onFocus={() => {
                  if (Platform.OS === 'android') {
                    requestAnimationFrame(() => {
                      // Ensure layout updates for Android
                    });
                  }
                }}
                onBlur={() => {
                  if (Platform.OS === 'android') {
                    requestAnimationFrame(() => {
                      // Ensure layout updates for Android
                    });
                  }
                }}
              />
            </View>
            <TouchableOpacity
              style={[styles.postButton, !newComment.trim() && styles.postButtonDisabled]}
              onPress={() => {
                addComment();
                if (Platform.OS === 'android') {
                  Keyboard.dismiss();
                  requestAnimationFrame(() => {
                    // Ensure UI updates after posting
                  });
                }
              }}
              disabled={!newComment.trim()}
            >
              <Text style={[styles.postButtonText, !newComment.trim() && styles.postButtonTextDisabled]}>
                {replyingTo ? 'Reply' : 'Post'}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </KeyboardAvoidingView>

      {/* Collection Selection Modal */}
      <CollectionSelectionModal
        visible={showCollectionModal}
        onClose={() => setShowCollectionModal(false)}
        onSelectCollection={handleSelectCollection}
        itemId={itemId}
        itemData={item}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  errorText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 15,
  },
  backLink: {
    fontSize: 16,
    color: '#FF6B6B',
    fontWeight: 'bold',
  },
  headerButton: {
    paddingHorizontal: 15,
    paddingBottom: 10,
    justifyContent: 'flex-end',
  },
  headerBackground: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.2)',
  },
  itemImage: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT * 0.6,
  },
  carouselContainer: {
    position: 'relative',
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT * 0.6,
  },
  carouselImage: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT * 0.6,
  },
  paginationContainer: {
    position: 'absolute',
    bottom: 30, // Increased from 15 to 30 to make dots more visible
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    width: '100%',
    zIndex: 10, // Added to ensure dots appear above other elements
  },
  paginationDot: {
    width: 10, // Increased from 8 to 10
    height: 10, // Increased from 8 to 10
    borderRadius: 5, // Adjusted to match new size
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 5, // Increased from 4 to 5 for better spacing
  },
  paginationDotActive: {
    backgroundColor: '#fff',
    width: 12, // Increased from 10 to 12
    height: 12, // Increased from 10 to 12
    borderRadius: 6, // Adjusted to match new size
  },
  contentContainer: {
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: '#fff',
    marginTop: -20,
    minHeight: SCREEN_HEIGHT * 0.4,
  },
  basicInfoSection: {
    marginBottom: 20,
  },
  titlePriceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  title: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    marginRight: 10,
  },
  priceText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginTop: 5,
  },

  shareButton: {
    marginLeft: 10,
    padding: 6,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.7)',
    alignSelf: 'flex-start',
  },
  tagsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  tag: {
    backgroundColor: '#eee',
    color: '#555',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    marginRight: 8,
    marginBottom: 8,
    fontSize: 13,
    textTransform: 'capitalize',
  },
  likeCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  likeCountText: {
    marginLeft: 5,
    fontSize: 14,
    color: '#666',
  },
  actionButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 25,
    marginTop: 10,
  },
  actionButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 4,
    borderWidth: 1,
  },
  likeButton: {
    borderColor: '#FF6B6B',
  },
  likedButton: {
    backgroundColor: '#FF6B6B',
    borderColor: '#FF6B6B',
  },
  wishlistButton: {
    borderColor: '#4A90E2',
  },
  wishlistedButton: {
    backgroundColor: '#4A90E2',
    borderColor: '#4A90E2',
  },
  cartButton: {
    borderColor: '#4CAF50',
  },
  inCartButton: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  messageButton: {
    borderColor: '#007AFF',
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#444',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    color: '#555',
  },
  uploaderSection: {
    marginTop: 20,
    paddingVertical: 15,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  uploaderTouchable: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  uploaderImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    backgroundColor: '#eee',
    borderWidth: 2,
    borderColor: '#FFC0CB',
  },
  uploaderTextContainer: {
    flex: 1,
  },
  uploaderLabel: {
    fontSize: 12,
    color: '#888',
    marginBottom: 2,
  },
  uploaderName: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
  },
  sellerBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF0F0',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    marginTop: 4,
  },
  sellerBadgeText: {
    fontSize: 12,
    color: '#FF6B6B',
    marginLeft: 4,
    fontWeight: '500',
  },
  buySection: {
    marginTop: 10,
    marginBottom: 30,
  },
  buyButton: {
    flexDirection: 'row',
    backgroundColor: '#FF6B6B',
    paddingVertical: 15,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
  },
  buyButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  itemTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  ownerActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 15,
  },
  ownerActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4A90E2',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginLeft: 10,
  },
  deleteButton: {
    backgroundColor: '#FF6B6B',
  },
  ownerActionText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  // Comments section styles
  commentsSection: {
    marginTop: 20,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  commentsSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  sortButtonText: {
    fontSize: 12,
    color: '#666',
    marginRight: 5,
  },
  sortOptionsContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 15,
    padding: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#eee',
  },
  sortOption: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
  },
  activeSortOption: {
    backgroundColor: '#FFF0F0',
  },
  sortOptionText: {
    fontSize: 14,
    color: '#666',
  },
  activeSortOptionText: {
    color: '#FF6B6B',
    fontWeight: '600',
  },
  noCommentsText: {
    color: '#888',
    fontStyle: 'italic',
    textAlign: 'center',
    marginVertical: 20,
  },
  commentItem: {
    flexDirection: 'row',
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  commentUserImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#eee',
    borderWidth: 2,
    borderColor: '#FFC0CB',
  },
  commentContent: {
    flex: 1,
  },
  commentUsernameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginBottom: 2,
  },
  commentUsername: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#333',
    marginRight: 6,
  },
  commentSellerBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF0F0',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  commentSellerBadgeText: {
    fontSize: 10,
    color: '#FF6B6B',
    marginLeft: 3,
    fontWeight: '500',
  },
  commentText: {
    fontSize: 14,
    color: '#444',
    lineHeight: 20,
  },
  editedText: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
    marginTop: 2,
  },
  commentMetaContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5,
  },
  commentTime: {
    fontSize: 12,
    color: '#999',
  },
  commentActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  commentAction: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
  },
  commentActionText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  ownerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editCommentContainer: {
    marginVertical: 5,
  },
  editCommentInput: {
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    padding: 10,
    fontSize: 14,
    color: '#333',
    minHeight: 60,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  editCommentActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  editCommentCancel: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    marginRight: 10,
  },
  editCommentCancelText: {
    color: '#666',
    fontSize: 12,
  },
  editCommentSave: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  editCommentSaveText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  repliesContainer: {
    marginLeft: 50,
    marginTop: 5,
    marginBottom: 10,
  },
  replyItem: {
    flexDirection: 'row',
    marginBottom: 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  replyUserImage: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 8,
    backgroundColor: '#eee',
    borderWidth: 1,
    borderColor: '#FFC0CB',
  },
  replyContent: {
    flex: 1,
  },
  replyingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 10,
    marginBottom: 10,
  },
  replyingText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  replyingBadge: {
    position: 'absolute',
    top: -35, // Moved higher to avoid overlap
    left: 15,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF0F0',
    paddingHorizontal: 12,
    paddingVertical: 5,
    borderRadius: 15,
    zIndex: 1001,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#FFCDD2',
  },
  replyingBadgeText: {
    fontSize: 13,
    color: '#FF6B6B',
    fontWeight: '600',
    marginRight: 5,
  },
  cancelReplyButton: {
    padding: 2,
  },
  replyingCommentInput: {
    borderColor: '#FF6B6B',
    borderWidth: 1,
  },
  inputWrapper: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 20,
    marginRight: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
    justifyContent: 'flex-start', // Changed from 'center' to 'flex-start'
    minHeight: 50, // Changed from fixed height to minHeight
    maxHeight: 80, // Added maxHeight to match TextInput
    overflow: 'hidden', // Added to contain the scroll indicator
  },
  commentInputContainer: {
    flexDirection: 'row',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#ddd',
    backgroundColor: '#fff',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    minHeight: 60,
    width: '100%',
    marginBottom: 0,
    ...(Platform.OS === 'android' && {
      position: 'relative',
      zIndex: 1000,
      borderTopWidth: 1,
      borderTopColor: '#ddd',
    }),
  },
  commentInput: {
    flex: 1,
    minHeight: 40,
    maxHeight: 80,
    backgroundColor: '#ffffff',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
    textAlignVertical: 'top', // Changed from 'center' to 'top' for better scrolling
    borderWidth: 1,
    borderColor: '#aaa',
    justifyContent: 'flex-start', // Changed from 'center' to 'flex-start'
    alignItems: 'flex-start', // Changed from 'center' to 'flex-start'
    overflow: 'hidden', // Added to contain the scroll indicator
  },
  postButton: {
    marginLeft: 8,
    paddingHorizontal: 15,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF6B6B',
    borderRadius: 20,
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 3,
  },
  postButtonDisabled: {
    backgroundColor: '#f0f0f0',
    shadowOpacity: 0,
    elevation: 0,
  },
  postButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  postButtonTextDisabled: {
    color: '#999',
  },
  keyboardAvoidView: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
  },
});

export default ItemDetailsScreen;