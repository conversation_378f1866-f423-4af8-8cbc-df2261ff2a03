import { useState, useEffect } from 'react';
import { doc, onSnapshot } from 'firebase/firestore';
import { onAuthStateChanged } from 'firebase/auth';
import { db, auth } from '../firebase.config';
import { addCacheBuster } from '../utils/feedHelpers';

export const useUserProfile = () => {
  const [currentUserPhotoURL, setCurrentUserPhotoURL] = useState(null);
  const [currentUserId, setCurrentUserId] = useState(null);

  // Listen to authentication state changes
  useEffect(() => {
    const unsubscribeAuth = onAuthStateChanged(auth, (user) => {
      if (user) {
        setCurrentUserId(user.uid);
      } else {
        setCurrentUserId(null);
        setCurrentUserPhotoURL(null); // Clear photo when user logs out
      }
    });

    return () => unsubscribeAuth();
  }, []);

  useEffect(() => {
    let unsubscribe = () => { };

    const setupUserProfileListener = async () => {
      if (!currentUserId) {
        console.log("[UserProfile] No authenticated user, skipping profile listener");
        setCurrentUserPhotoURL(null);
        return;
      }

      try {
        // Verify authentication token is valid before setting up listener
        if (!auth.currentUser) {
          console.log("[UserProfile] No current user in auth, skipping profile setup");
          setCurrentUserPhotoURL(null);
          return;
        }

        try {
          await auth.currentUser.getIdToken(true);
        } catch (tokenError) {
          console.error("[UserProfile] Failed to get auth token:", tokenError);
          setCurrentUserPhotoURL(null);
          return;
        }

        const userDocRef = doc(db, 'users', currentUserId);

        // Use onSnapshot to listen for real-time updates to the user document
        unsubscribe = onSnapshot(userDocRef, (docSnap) => {
          // Double-check that user is still authenticated
          if (!auth.currentUser || auth.currentUser.uid !== currentUserId) {
            console.log("[UserProfile] User no longer authenticated, ignoring profile update");
            return;
          }

          try {
            if (docSnap.exists()) {
              const newPhotoURL = docSnap.data().profilePictureUrl;

              if (newPhotoURL) {
                const urlWithCacheBuster = addCacheBuster(newPhotoURL);
                setCurrentUserPhotoURL(urlWithCacheBuster);
                console.log("[UserProfile] Updated user profile picture from listener");
              } else {
                setCurrentUserPhotoURL(null);
              }
            }
          } catch (dataError) {
            console.error("[UserProfile] Error processing profile data:", dataError);
            setCurrentUserPhotoURL(null);
          }
        }, (error) => {
          console.error("[UserProfile] Error in user profile listener:", error);
          // If we get a permission error, the user might not be properly authenticated
          if (error.code === 'permission-denied') {
            console.log("[UserProfile] Permission denied, user might need to re-authenticate");
            // Clear photo on permission error
            setCurrentUserPhotoURL(null);
          }
        });
      } catch (error) {
        console.error("[UserProfile] Error setting up user profile listener:", error);
        setCurrentUserPhotoURL(null);
      }
    };

    if (currentUserId) {
      setupUserProfileListener();
    }

    // Clean up the listener when the component unmounts or currentUserId changes
    return () => {
      unsubscribe();
    };
  }, [currentUserId]);

  return {
    currentUserPhotoURL,
    currentUserId
  };
};
