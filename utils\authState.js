// Global state manager for authentication events
class AuthStateManager {
  constructor() {
    this.userTypeMismatchInProgress = false;
    this.lastMismatchTime = 0;
    this.blockedUserIds = new Set(); // Track users that should be blocked
  }

  setUserTypeMismatch(userId = null) {
    this.userTypeMismatchInProgress = true;
    this.lastMismatchTime = Date.now();

    if (userId) {
      this.blockedUserIds.add(userId);
      console.log(`AuthStateManager: User ${userId} blocked due to type mismatch`);
    }

    console.log('AuthStateManager: User type mismatch flag set');

    // Auto-reset after 10 seconds to prevent permanent blocking
    setTimeout(() => {
      this.clearUserTypeMismatch();
    }, 10000);
  }

  clearUserTypeMismatch() {
    this.userTypeMismatchInProgress = false;
    this.blockedUserIds.clear();
    console.log('AuthStateManager: User type mismatch flag cleared');
  }

  isUserBlocked(userId) {
    return this.blockedUserIds.has(userId);
  }

  isUserTypeMismatchInProgress() {
    // Also check if the mismatch was recent (within last 10 seconds)
    const timeSinceMismatch = Date.now() - this.lastMismatchTime;
    const isRecent = timeSinceMismatch < 10000;

    return this.userTypeMismatchInProgress || isRecent;
  }

  shouldIgnoreAuthEvent(userId = null) {
    const shouldIgnore = this.isUserTypeMismatchInProgress() || (userId && this.isUserBlocked(userId));
    if (shouldIgnore) {
      console.log(`AuthStateManager: Ignoring auth event${userId ? ` for user ${userId}` : ''} due to user type mismatch`);
    }
    return shouldIgnore;
  }
}

// Create a singleton instance
const authStateManager = new AuthStateManager();

export default authStateManager;
