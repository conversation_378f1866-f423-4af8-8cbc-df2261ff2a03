import { useState, useEffect, useCallback, useRef } from 'react';
import { collection, query, getDocs, where, orderBy, limit, doc, updateDoc, increment } from 'firebase/firestore';
import { onAuthStateChanged } from 'firebase/auth';
import { db, auth } from '../firebase.config';
import { useUserProfile } from './useUserProfile';
import { useCartManager } from './useCartManager';
import { useWishlistManager } from './useWishlistManager';
import { useImagePreloader } from './useImagePreloader';
import {
  defaultCategories,
  QUERY_LIMIT,
  PRELOAD_THRESHOLD,
  LOADING_TIMEOUT
} from '../utils/feedConstants';
import { shuffleArray, filterItems, removeDuplicates, isValidItem } from '../utils/feedHelpers';
import { RecommendationEngine } from '../utils/recommendationEngine';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useClothingFeed = (navigation) => {
  // State management
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [backgroundLoading, setBackgroundLoading] = useState(false);
  const [error, setError] = useState(null);
  // Enhanced filtering state
  const [activeCategoryType, setActiveCategoryType] = useState('All');
  const [activeSpecificCategories, setActiveSpecificCategories] = useState([]);
  const [activeColor, setActiveColor] = useState(null);

  // Legacy support
  const [activeCategory, setActiveCategory] = useState('All');
  const [noMoreItems, setNoMoreItems] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [interactedItemIds, setInteractedItemIds] = useState(new Set());
  const [initialLikedIdsLoaded, setInitialLikedIdsLoaded] = useState(false);
  const [categories, setCategories] = useState(defaultCategories);
  const [filteredCategories, setFilteredCategories] = useState(defaultCategories);
  const [filteredItems, setFilteredItems] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchQueryHeader, setSearchQueryHeader] = useState('');
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [showCollectionModal, setShowCollectionModal] = useState(false);
  const [isLoadingMoreCategories, setIsLoadingMoreCategories] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);
  const [isSearchMode, setIsSearchMode] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [suggestedCategory, setSuggestedCategory] = useState(null);

  // Recommendation system state
  const [useRecommendations, setUseRecommendations] = useState(true);
  const [recommendationThreshold, setRecommendationThreshold] = useState(0.3);
  const [recommendationEngine] = useState(new RecommendationEngine());

  // Refs
  const hasLoadedRef = useRef(false);
  const previousCategoryRef = useRef('All');
  const categoryCacheRef = useRef(new Map());

  // Load recommendation settings from AsyncStorage
  useEffect(() => {
    const loadRecommendationSettings = async () => {
      try {
        const useRecommendationsStored = await AsyncStorage.getItem('useRecommendations');
        const thresholdStored = await AsyncStorage.getItem('recommendationThreshold');

        if (useRecommendationsStored !== null) {
          setUseRecommendations(JSON.parse(useRecommendationsStored));
        }

        if (thresholdStored !== null) {
          setRecommendationThreshold(parseFloat(thresholdStored));
        }
      } catch (error) {
        console.error('Error loading recommendation settings:', error);
      }
    };

    loadRecommendationSettings();
  }, []);

  // Save recommendation settings to AsyncStorage
  const updateRecommendationSettings = useCallback(async (newUseRecommendations, newThreshold) => {
    try {
      if (newUseRecommendations !== undefined) {
        setUseRecommendations(newUseRecommendations);
        await AsyncStorage.setItem('useRecommendations', JSON.stringify(newUseRecommendations));
      }

      if (newThreshold !== undefined) {
        setRecommendationThreshold(newThreshold);
        await AsyncStorage.setItem('recommendationThreshold', newThreshold.toString());
      }
    } catch (error) {
      console.error('Error saving recommendation settings:', error);
    }
  }, []);

  // Custom hooks
  const { currentUserPhotoURL } = useUserProfile();
  const { cartItems, showCartAnimation, addToCart } = useCartManager();
  const { isCurrentItemWishlisted, setIsCurrentItemWishlisted, toggleWishlist, checkWishlistStatus } = useWishlistManager(
    items[currentIndex]?.id
  );
  const { preloadImages } = useImagePreloader();

  // Listen to authentication state changes
  useEffect(() => {
    const unsubscribeAuth = onAuthStateChanged(auth, (user) => {
      if (user) {
        setCurrentUserId(user.uid);
      } else {
        setCurrentUserId(null);
        // Clear all data when user logs out
        setItems([]);
        setInteractedItemIds(new Set());
        setInitialLikedIdsLoaded(false);
        hasLoadedRef.current = false;
      }
    });

    return () => unsubscribeAuth();
  }, []);

  // Fetch interacted item IDs
  const fetchInteractedIds = useCallback(async () => {
    if (!currentUserId) {
      console.log('[ClothingFeed] No authenticated user, skipping interacted IDs fetch');
      setInteractedItemIds(new Set());
      setInitialLikedIdsLoaded(true);
      return;
    }

    // Double-check authentication
    if (!auth.currentUser || auth.currentUser.uid !== currentUserId) {
      console.log('[ClothingFeed] User authentication mismatch, skipping interacted IDs fetch');
      setInteractedItemIds(new Set());
      setInitialLikedIdsLoaded(true);
      return;
    }

    console.log(`[ClothingFeed] Fetching interacted IDs for user: ${currentUserId}`);

    const interactedIdsTimeoutId = setTimeout(() => {
      console.log('[ClothingFeed] Interacted IDs fetch timeout - proceeding anyway');
      setInteractedItemIds(new Set());
      setInitialLikedIdsLoaded(true);
    }, LOADING_TIMEOUT);

    try {
      // Verify authentication token is valid
      const idToken = await auth.currentUser.getIdToken(true);
      if (!idToken) {
        throw new Error('Failed to get authentication token');
      }

      // Fetch liked items
      const likesCollectionRef = collection(db, 'users', currentUserId, 'likes');
      const likesSnapshot = await getDocs(likesCollectionRef);
      const likedIds = new Set(likesSnapshot.docs.map(doc => doc.data().itemId));

      // Fetch disliked items
      const dislikesCollectionRef = collection(db, 'users', currentUserId, 'dislikes');
      const dislikesSnapshot = await getDocs(dislikesCollectionRef);
      const dislikedIds = new Set(dislikesSnapshot.docs.map(doc => doc.data().itemId));

      // Fetch items in cart
      const cartCollectionRef = collection(db, 'users', currentUserId, 'cart');
      const cartSnapshot = await getDocs(cartCollectionRef);
      const cartItems = cartSnapshot.docs.map(doc => doc.data());
      const cartIds = new Set(cartItems.map(item => item.itemId));

      // Combine all interacted IDs
      const allInteractedIds = new Set([
        ...Array.from(likedIds),
        ...Array.from(dislikedIds),
        ...Array.from(cartIds)
      ]);

      clearTimeout(interactedIdsTimeoutId);
      setInteractedItemIds(allInteractedIds);
      console.log(`[ClothingFeed] Loaded ${allInteractedIds.size} interacted item IDs.`);
    } catch (error) {
      console.error("[ClothingFeed] Error fetching interacted IDs:", error);
      clearTimeout(interactedIdsTimeoutId);
      // Clear data on error
      setInteractedItemIds(new Set());
    } finally {
      setInitialLikedIdsLoaded(true);
    }
  }, [currentUserId]);

  // Fetch all categories
  const fetchAllCategories = useCallback(async () => {
    try {
      console.log('[ClothingFeed] Fetching categories');

      // Set default categories immediately
      setCategories(defaultCategories);
      setFilteredCategories(defaultCategories);

      if (!currentUserId) {
        return;
      }

      // Fetch custom categories
      const customCategoriesRef = collection(db, 'customCategories');
      const customSnapshot = await getDocs(customCategoriesRef);
      const customCategories = customSnapshot.docs.map(doc => doc.data().name);

      // Combine with default categories
      const allCategories = [...new Set([...defaultCategories, ...customCategories])];
      setCategories(allCategories);
      setFilteredCategories(allCategories);

      console.log(`[ClothingFeed] Loaded ${allCategories.length} categories`);
    } catch (error) {
      console.error('[ClothingFeed] Error fetching categories:', error);
    }
  }, [currentUserId]);

  // Function to detect the most relevant category from search terms
  const detectRelevantCategory = useCallback((searchQuery, searchResults) => {
    if (!searchQuery || !searchResults || searchResults.length === 0) {
      return null;
    }

    const searchTerms = searchQuery.toLowerCase().split(' ').filter(term => term.length > 0);

    // Category mapping for common search terms
    const categoryMappings = {
      // Clothing types
      'shirt': 'Shirts',
      'shirts': 'Shirts',
      'blouse': 'Shirts',
      'top': 'Tops',
      'tops': 'Tops',
      'dress': 'Dresses',
      'dresses': 'Dresses',
      'gown': 'Dresses',
      'pant': 'Pants',
      'pants': 'Pants',
      'trouser': 'Pants',
      'trousers': 'Pants',
      'jean': 'Jeans',
      'jeans': 'Jeans',
      'denim': 'Jeans',
      'short': 'Shorts',
      'shorts': 'Shorts',
      'skirt': 'Skirts',
      'skirts': 'Skirts',
      'shoe': 'Shoes',
      'shoes': 'Shoes',
      'sneaker': 'Shoes',
      'sneakers': 'Shoes',
      'boot': 'Shoes',
      'boots': 'Shoes',
      'sandal': 'Shoes',
      'sandals': 'Shoes',
      'jacket': 'Jackets',
      'jackets': 'Jackets',
      'coat': 'Jackets',
      'coats': 'Jackets',
      'blazer': 'Jackets',
      'blazers': 'Jackets',
      'sweater': 'Sweaters',
      'sweaters': 'Sweaters',
      'hoodie': 'Sweaters',
      'hoodies': 'Sweaters',
      'cardigan': 'Sweaters',
      'cardigans': 'Sweaters',
      'accessory': 'Accessories',
      'accessories': 'Accessories',
      'bag': 'Accessories',
      'bags': 'Accessories',
      'hat': 'Accessories',
      'hats': 'Accessories',
      'belt': 'Accessories',
      'belts': 'Accessories',
      'jewelry': 'Accessories',
      'jewellery': 'Accessories',
      'watch': 'Accessories',
      'watches': 'Accessories'
    };

    // First, check if any search term directly maps to a category
    for (const term of searchTerms) {
      if (categoryMappings[term]) {
        const mappedCategory = categoryMappings[term];
        // Verify this category exists in our categories list
        if (categories.includes(mappedCategory)) {
          console.log(`[ClothingFeed] Direct category mapping: "${term}" -> "${mappedCategory}"`);
          return mappedCategory;
        }
      }
    }

    // If no direct mapping, analyze the search results to find the most common category
    const categoryCounts = {};
    searchResults.forEach(item => {
      const itemCategory = item.category;
      if (itemCategory && categories.includes(itemCategory)) {
        categoryCounts[itemCategory] = (categoryCounts[itemCategory] || 0) + 1;
      }
    });

    // Find the category with the most items
    let mostCommonCategory = null;
    let maxCount = 0;
    Object.entries(categoryCounts).forEach(([category, count]) => {
      if (count > maxCount) {
        maxCount = count;
        mostCommonCategory = category;
      }
    });

    if (mostCommonCategory && maxCount > 0) {
      console.log(`[ClothingFeed] Most common category in results: "${mostCommonCategory}" (${maxCount} items)`);
      return mostCommonCategory;
    }

    // If no specific category is dominant, return null to stay on current category
    return null;
  }, [categories]);

  // Search items by text query
  const searchItems = useCallback(async (searchQuery) => {
    if (!initialLikedIdsLoaded) {
      console.log("[ClothingFeed] Waiting for initial liked IDs to load...");
      return;
    }

    if (!searchQuery.trim()) {
      // If empty query, return to normal feed
      setIsSearchMode(false);
      setSearchResults([]);
      setSearchQueryHeader('');
      // Clear items to trigger normal fetch in component
      setItems([]);
      setCurrentIndex(0);
      return;
    }

    setLoading(true);
    setError(null);
    setIsSearchMode(true);
    setSearchQueryHeader(searchQuery);

    const loadingTimeoutId = setTimeout(() => {
      setLoading(false);
      console.log(`[ClothingFeed] Search timeout triggered - resetting loading state`);
    }, LOADING_TIMEOUT);

    try {
      console.log(`[ClothingFeed] Starting search for: "${searchQuery}"`);

      const itemsCollection = collection(db, 'clothingItems');
      const queryLimit = QUERY_LIMIT * 2; // Get more items for search to have better results

      // Search in multiple fields: title, category, brand, description
      const searchTerms = searchQuery.toLowerCase().split(' ').filter(term => term.length > 0);
      console.log(`[ClothingFeed] Search terms: ${searchTerms.join(', ')}`);

      // Get all items first, then filter client-side for better search results
      // This is more flexible than Firestore's limited text search capabilities
      const baseQuery = query(itemsCollection, orderBy('createdAt', 'desc'), limit(queryLimit));
      const snapshot = await getDocs(baseQuery);

      console.log(`[ClothingFeed] Retrieved ${snapshot.docs.length} items from database for filtering`);

      const allItems = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Filter items based on search terms
      const searchResults = allItems.filter(item => {
        if (!isValidItem(item) || interactedItemIds.has(item.id)) {
          return false;
        }

        const searchableText = [
          item.title || '',
          item.category || '',
          item.brand || '',
          item.description || '',
          item.color || '',
          item.size || ''
        ].join(' ').toLowerCase();

        // Check if all search terms are found in the searchable text
        const matches = searchTerms.every(term => searchableText.includes(term));

        if (matches) {
          console.log(`[ClothingFeed] Match found: ${item.title} (${item.category})`);
        }

        return matches;
      });

      console.log(`[ClothingFeed] Found ${searchResults.length} matching items after filtering`);

      // Remove duplicates and shuffle
      const uniqueResults = removeDuplicates(searchResults);
      const shuffledResults = shuffleArray([...uniqueResults]);

      // Detect the most relevant category for the search results
      const relevantCategory = detectRelevantCategory(searchQuery, shuffledResults);
      setSuggestedCategory(relevantCategory);

      clearTimeout(loadingTimeoutId);

      setSearchResults(shuffledResults);
      setItems(shuffledResults);
      setCurrentIndex(0);
      setNoMoreItems(shuffledResults.length === 0);

      // Preload images for better performance
      if (shuffledResults.length > 0) {
        preloadImages(shuffledResults.slice(0, PRELOAD_THRESHOLD));
      }

      console.log(`[ClothingFeed] Search for "${searchQuery}" returned ${shuffledResults.length} results`);
      if (relevantCategory) {
        console.log(`[ClothingFeed] Suggested category for search: "${relevantCategory}"`);
      }
    } catch (error) {
      console.error('[ClothingFeed] Error searching items:', error);
      setError('Failed to search items. Please try again.');
      clearTimeout(loadingTimeoutId);
    } finally {
      setLoading(false);
    }
  }, [initialLikedIdsLoaded, interactedItemIds, preloadImages]);

  // Clear search and return to normal feed
  const clearSearch = useCallback(() => {
    setIsSearchMode(false);
    setSearchResults([]);
    setSearchQuery('');
    setSearchQueryHeader('');
    setSuggestedCategory(null);
    // We'll call fetchClothingItems in the component when clearSearch is called
  }, []);

  // Fetch clothing items
  const fetchClothingItems = useCallback(async (isRefresh = false, isBackgroundFetch = false) => {
    if (!initialLikedIdsLoaded) {
      console.log("[ClothingFeed] Waiting for initial liked IDs to load...");
      return;
    }

    // If in search mode and not refreshing, don't fetch new items
    if (isSearchMode && !isRefresh) {
      return;
    }

    // Clear search mode when fetching normal items
    if (isRefresh) {
      setIsSearchMode(false);
      setSearchResults([]);
    }

    if (!isRefresh && !isBackgroundFetch) {
      setLoading(true);
    } else if (isBackgroundFetch) {
      setBackgroundLoading(true);
    }

    setError(null);

    const loadingTimeoutId = setTimeout(() => {
      if (!isBackgroundFetch) {
        setLoading(false);
      } else {
        setBackgroundLoading(false);
      }
      console.log(`[ClothingFeed] Loading timeout triggered - resetting loading state`);
    }, LOADING_TIMEOUT);

    try {
      const itemsCollection = collection(db, 'clothingItems');
      const queryLimit = QUERY_LIMIT;
      const orderByField = 'createdAt';

      // Build query based on enhanced filtering
      let baseQuery;
      const queryConstraints = [orderBy(orderByField, 'desc'), limit(queryLimit)];

      // Add category type filter
      if (activeCategoryType && activeCategoryType !== 'All') {
        queryConstraints.unshift(where('broadCategory', '==', activeCategoryType));
      }

      baseQuery = query(itemsCollection, ...queryConstraints);

      const snapshot = await getDocs(baseQuery);
      const fetchedItems = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Filter out invalid items and interacted items
      let validItems = fetchedItems.filter(item =>
        isValidItem(item) && !interactedItemIds.has(item.id)
      );

      // Apply client-side filtering for specific categories
      if (activeSpecificCategories.length > 0) {
        validItems = validItems.filter(item => {
          // Check if item has any of the selected specific categories
          if (Array.isArray(item.detailedCategories)) {
            return item.detailedCategories.some(cat =>
              activeSpecificCategories.includes(cat)
            );
          } else if (item.detailedCategory) {
            return activeSpecificCategories.includes(item.detailedCategory);
          } else if (item.category) {
            return activeSpecificCategories.includes(item.category);
          }
          return false;
        });
      }

      // Apply client-side filtering for colors
      if (activeColor) {
        // Find the color name from the hex value
        const FILTER_COLORS = [
          { name: 'Red', value: '#FF0000' },
          { name: 'Blue', value: '#0000FF' },
          { name: 'Green', value: '#008000' },
          { name: 'Yellow', value: '#FFFF00' },
          { name: 'Black', value: '#000000' },
          { name: 'White', value: '#FFFFFF' },
          { name: 'Pink', value: '#FFC0CB' },
          { name: 'Purple', value: '#800080' },
          { name: 'Orange', value: '#FFA500' },
          { name: 'Brown', value: '#A52A2A' },
          { name: 'Gray', value: '#808080' },
          { name: 'Navy', value: '#000080' },
          { name: 'Beige', value: '#F5F5DC' },
        ];

        const selectedColorObj = FILTER_COLORS.find(c => c.value === activeColor);
        const colorName = selectedColorObj ? selectedColorObj.name : null;

        if (colorName) {
          validItems = validItems.filter(item => {
            if (Array.isArray(item.colors)) {
              return item.colors.some(color =>
                color.toLowerCase() === colorName.toLowerCase()
              );
            }
            return false;
          });
        }
      }

      // Remove duplicates
      const uniqueItems = removeDuplicates(validItems);

      // Apply recommendations if enabled and user is logged in
      let finalItems;
      if (useRecommendations && currentUserId && uniqueItems.length > 0) {
        console.log('[ClothingFeed] Applying recommendations for', uniqueItems.length, 'items');
        try {
          finalItems = await recommendationEngine.getRecommendedItems(
            currentUserId,
            uniqueItems,
            recommendationThreshold
          );
          console.log('[ClothingFeed] Recommendations applied, got', finalItems?.length || 0, 'items');
        } catch (error) {
          console.error('[ClothingFeed] Error applying recommendations:', error);
          finalItems = shuffleArray([...uniqueItems]); // Fallback to shuffled items
        }
      } else {
        finalItems = shuffleArray([...uniqueItems]);
      }

      clearTimeout(loadingTimeoutId);

      if (isRefresh || activeCategory !== previousCategoryRef.current) {
        setItems(finalItems);
        setCurrentIndex(0);
        setNoMoreItems(finalItems.length === 0);
        previousCategoryRef.current = activeCategory;
      } else {
        setItems(prev => {
          const combined = [...prev, ...finalItems];
          return removeDuplicates(combined);
        });
        setNoMoreItems(finalItems.length === 0);
      }

      // Preload images for better performance
      if (finalItems.length > 0) {
        preloadImages(finalItems.slice(0, PRELOAD_THRESHOLD));
      }

      console.log('[ClothingFeed] Fetched', finalItems?.length || 0, 'items for category:', activeCategory);
    } catch (error) {
      console.error('[ClothingFeed] Error fetching items:', error);
      setError('Failed to load items. Please try again.');
      clearTimeout(loadingTimeoutId);
    } finally {
      if (!isBackgroundFetch) {
        setLoading(false);
      } else {
        setBackgroundLoading(false);
      }
    }
  }, [activeCategoryType, activeSpecificCategories, activeColor, activeCategory, interactedItemIds, preloadImages, initialLikedIdsLoaded, useRecommendations, currentUserId, recommendationThreshold, recommendationEngine]);

  // Initialize feed when user is authenticated
  useEffect(() => {
    if (currentUserId) {
      setLoading(true);
      fetchInteractedIds();
    } else {
      // Reset state when user logs out
      setLoading(false);
      setInitialLikedIdsLoaded(false);
      hasLoadedRef.current = false;
    }
  }, [currentUserId, fetchInteractedIds]);

  useEffect(() => {
    if (currentUserId && initialLikedIdsLoaded && !hasLoadedRef.current) {
      console.log('[ClothingFeed] Initial load - fetching categories and items');
      hasLoadedRef.current = true;
      fetchAllCategories();
      fetchClothingItems(true);
    }
  }, [currentUserId, initialLikedIdsLoaded, fetchClothingItems, fetchAllCategories]);

  // Update wishlist status when current item changes
  useEffect(() => {
    if (items.length > 0 && currentIndex < items.length) {
      const currentItem = items[currentIndex];
      if (currentItem) {
        checkWishlistStatus(currentItem.id);
      }
    }
  }, [currentIndex, items, checkWishlistStatus]);

  return {
    // State
    items,
    loading,
    backgroundLoading,
    error,
    activeCategory,
    noMoreItems,
    currentIndex,
    categories,
    filteredCategories,
    filteredItems,
    searchQuery,
    searchQueryHeader,
    searchModalVisible,
    showCollectionModal,
    isLoadingMoreCategories,
    currentUserPhotoURL,
    cartItems,
    showCartAnimation,
    isCurrentItemWishlisted,
    isSearchMode,
    searchResults,
    suggestedCategory,

    // Enhanced filtering state
    activeCategoryType,
    activeSpecificCategories,
    activeColor,

    // Recommendation state
    useRecommendations,
    recommendationThreshold,

    // Actions
    setActiveCategory,
    setActiveCategoryType,
    setActiveSpecificCategories,
    setActiveColor,
    setCurrentIndex,
    setSearchQuery,
    setSearchQueryHeader,
    setSearchModalVisible,
    setShowCollectionModal,
    fetchClothingItems,
    fetchAllCategories,
    addToCart,
    toggleWishlist,
    setInteractedItemIds,
    checkWishlistStatus,
    setIsCurrentItemWishlisted,
    searchItems,
    clearSearch,
    updateRecommendationSettings,

    // Computed
    currentUserId
  };
};
