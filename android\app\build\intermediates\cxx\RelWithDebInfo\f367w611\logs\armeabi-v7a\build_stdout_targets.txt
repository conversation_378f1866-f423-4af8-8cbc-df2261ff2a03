ninja: Entering directory `D:\app\StyleApp\android\app\.cxx\RelWithDebInfo\f367w611\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[3/78] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o
[4/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[5/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[6/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[7/78] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o
[8/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[9/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[10/78] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o
[11/78] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o
[12/78] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o
[13/78] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[14/78] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o
[15/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o
[16/78] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o
[17/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o
[18/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o
[19/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/c4d7ad40e364722459916eeab18d92df/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o
[20/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o
[21/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/024900284a0067438a91cc6cc40252aa/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o
[22/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/024900284a0067438a91cc6cc40252aa/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:16:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   16 | void RNCAndroidDialogPickerEventEmitter::onSelect(OnSelect $event) const {
      |                                                            ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:17:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   17 |   dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                               ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:17:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   17 |   dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:18:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   18 |     auto $payload = jsi::Object(runtime);
      |          ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:19:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   19 |     $payload.setProperty(runtime, "position", $event.position);
      |     ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:19:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   19 |     $payload.setProperty(runtime, "position", $event.position);
      |                                               ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:20:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   20 |     return $payload;
      |            ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:25:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   25 | void RNCAndroidDialogPickerEventEmitter::onFocus(OnFocus $event) const {
      |                                                          ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:27:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   27 |     auto $payload = jsi::Object(runtime);
      |          ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:29:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   29 |     return $payload;
      |            ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:34:56: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   34 | void RNCAndroidDialogPickerEventEmitter::onBlur(OnBlur $event) const {
      |                                                        ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:36:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   36 |     auto $payload = jsi::Object(runtime);
      |          ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:38:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   38 |     return $payload;
      |            ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:43:62: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   43 | void RNCAndroidDropdownPickerEventEmitter::onSelect(OnSelect $event) const {
      |                                                              ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:44:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   44 |   dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                               ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:44:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   44 |   dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:45:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   45 |     auto $payload = jsi::Object(runtime);
      |          ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:46:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   46 |     $payload.setProperty(runtime, "position", $event.position);
      |     ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:46:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   46 |     $payload.setProperty(runtime, "position", $event.position);
      |                                               ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:47:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   47 |     return $payload;
      |            ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:52:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   52 | void RNCAndroidDropdownPickerEventEmitter::onFocus(OnFocus $event) const {
      |                                                            ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:54:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   54 |     auto $payload = jsi::Object(runtime);
      |          ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:56:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   56 |     return $payload;
      |            ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:61:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   61 | void RNCAndroidDropdownPickerEventEmitter::onBlur(OnBlur $event) const {
      |                                                          ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:63:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   63 |     auto $payload = jsi::Object(runtime);
      |          ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:65:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   65 |     return $payload;
      |            ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:70:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   70 | void RNCPickerEventEmitter::onChange(OnChange $event) const {
      |                                               ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:71:28: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   71 |   dispatchEvent("change", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                            ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:71:45: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   71 |   dispatchEvent("change", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                             ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:72:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   72 |     auto $payload = jsi::Object(runtime);
      |          ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:73:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   73 |     $payload.setProperty(runtime, "newValue", jsi::valueFromDynamic(runtime, $event.newValue));
      |     ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:73:78: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   73 |     $payload.setProperty(runtime, "newValue", jsi::valueFromDynamic(runtime, $event.newValue));
      |                                                                              ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:74:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   74 | $payload.setProperty(runtime, "newIndex", $event.newIndex);
      | ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:74:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   74 | $payload.setProperty(runtime, "newIndex", $event.newIndex);
      |                                           ^
D:/app/StyleApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:75:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   75 |     return $payload;
      |            ^
35 warnings generated.
[23/78] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o
[24/78] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o
[25/78] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o
[26/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o
[27/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o
[28/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/024900284a0067438a91cc6cc40252aa/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o
[29/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/6262661d85b07a4516ae598c40c16cbd/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o
[30/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o
[31/78] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp.o
[32/78] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o
[33/78] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o
[34/78] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o
[35/78] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o
[36/78] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o
[37/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[38/78] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/c4d7ad40e364722459916eeab18d92df/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o
[39/78] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o
[40/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[41/78] Linking CXX shared library D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libreact_codegen_rnpicker.so
[42/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[43/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[44/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[45/78] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o
[46/78] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[47/78] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[48/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/39895e341ebcc71f66b592787c6abfab/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o
[49/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[50/78] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o
[51/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/10910abdbbee2f1568aa12fbb1bc7d89/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o
[52/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7e298eca2726e4de430824781ad7074a/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o
[53/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[54/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bdb2e07ed73abd4b5d69484b8e280914/components/safeareacontext/safeareacontextJSI-generated.cpp.o
[55/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8e3ab5c9eff1f489bcd59e39c91888bb/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o
[56/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/489f1c3c785b29855feac2224d0527f7/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[57/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/10910abdbbee2f1568aa12fbb1bc7d89/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o
[58/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7e298eca2726e4de430824781ad7074a/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o
[59/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fc3da0873a65ab96a91ee3840a26004c/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[60/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fc3da0873a65ab96a91ee3840a26004c/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[61/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ff1f2489bcf3b2e31e49aad29d8fea76/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o
[62/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/dda1087935bd9f01d500ac250b456a92/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[63/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/dda1087935bd9f01d500ac250b456a92/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[64/78] Linking CXX shared library D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libreact_codegen_safeareacontext.so
[65/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fc3da0873a65ab96a91ee3840a26004c/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[66/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e53b248386f4a6ece72c11d87e9982a/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[67/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/36e762b3b0beee47724fe080fbf32c71/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[68/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/36e762b3b0beee47724fe080fbf32c71/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[69/78] Building CXX object CMakeFiles/appmodules.dir/D_/app/StyleApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[70/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0a1c7c95b09bc78e080e6bb6d373f810/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[71/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[72/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/dda1087935bd9f01d500ac250b456a92/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[73/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/941a2d13243008312d94b735874a3ab8/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[74/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/941a2d13243008312d94b735874a3ab8/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[75/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0a1c7c95b09bc78e080e6bb6d373f810/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[76/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e53b248386f4a6ece72c11d87e9982a/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[77/78] Linking CXX shared library D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libreact_codegen_rnscreens.so
[78/78] Linking CXX shared library D:\app\StyleApp\android\app\build\intermediates\cxx\RelWithDebInfo\f367w611\obj\armeabi-v7a\libappmodules.so
