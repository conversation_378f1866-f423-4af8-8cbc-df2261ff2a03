{"name": "SwipeSense", "slug": "swipesense", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.swipesense.swipesense", "googleServicesFile": "./GoogleService-Info.plist", "infoPlist": {"NSPhotoLibraryUsageDescription": "This app needs access to your photo library to upload clothing images.", "NSCameraUsageDescription": "This app needs access to your camera to take photos of clothing items."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.swipesense.swipesense", "googleServicesFile": "./google-services.json", "permissions": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.CAMERA", "android.permission.READ_MEDIA_IMAGES", "android.permission.RECORD_AUDIO"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-dev-client", "@react-native-google-signin/google-signin", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you upload clothing images.", "cameraPermission": "The app accesses your camera to let you take photos of clothing items."}]], "newArchEnabled": true, "scheme": "swipesense", "extra": {"eas": {"projectId": "bb715f1c-0331-4e77-a9b2-35efc24bc54c"}}, "owner": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "updates": {}, "runtimeVersion": "exposdk:53.0.0", "sdkVersion": "53.0.0", "platforms": ["ios", "android"]}