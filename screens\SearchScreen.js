import React, { useState, useEffect } from 'react';
import { View, TextInput, FlatList, Text, TouchableOpacity, StyleSheet, Image, ActivityIndicator, SafeAreaView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { db } from '../firebase.config';
import { collection, query, where, getDocs, limit } from 'firebase/firestore';
import { Ionicons } from '@expo/vector-icons';

const SearchScreen = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const insets = useSafeAreaInsets();

  const handleSearch = async (text) => {
    const searchText = text.trim(); // Trim whitespace
    setSearchQuery(text); // Keep original case in input field

    if (searchText.length < 2) { // Only search if query is at least 2 chars
      setResults([]);
      return;
    }

    const searchTextLower = searchText.toLowerCase(); // Convert search text to lowercase
    setLoading(true);
    try {
      const usersRef = collection(db, 'users');
      // Case-insensitive prefix search on name_lowercase
      const q = query(
        usersRef,
        where('name_lowercase', '>=', searchTextLower), // Query lowercase field
        where('name_lowercase', '<=', searchTextLower + '\uf8ff'), // Firestore prefix search trick
        limit(10) // Limit results
      );
      const querySnapshot = await getDocs(q);
      const users = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      setResults(users);
    } catch (error) {
      console.error("Error searching users:", error);
      // Check console for Firestore index errors!
      // Example error: "The query requires an index. You can create it here: <link>"
      setResults([]); // Clear results on error
    } finally {
      setLoading(false);
    }
  };

  const renderItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.resultItem} 
      onPress={() => navigation.navigate('UserProfile', { userId: item.id })} // Navigate to UserProfileScreen
    >
      <Image 
        source={{ uri: item.profilePictureUrl || 'https://via.placeholder.com/50' }} 
        style={styles.avatar} 
      />
      <Text style={styles.displayName}>{item.name || 'User'}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.safeArea, { paddingTop: insets.top }]}> 
      <View style={styles.container}>
        <View style={styles.searchContainer}>
          <Ionicons name="search-outline" size={20} color="#888" style={styles.searchIcon} />
          <TextInput
            style={styles.input}
            placeholder="Search users..."
            value={searchQuery}
            onChangeText={handleSearch}
            autoCapitalize="none"
            placeholderTextColor="#888"
          />
        </View>
        {loading ? (
          <ActivityIndicator style={{ marginTop: 20 }} size="large" color="#FF6B6B" />
        ) : (
          <FlatList
            data={results}
            renderItem={renderItem}
            keyExtractor={item => item.id}
            ListEmptyComponent={<Text style={styles.emptyText}>No users found.</Text>}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 10,
    margin: 15,
    paddingHorizontal: 10,
  },
  searchIcon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    height: 45,
    fontSize: 16,
    color: '#333',
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    backgroundColor: '#eee', // Placeholder background
  },
  displayName: {
    fontSize: 16,
    color: '#333',
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 30,
    fontSize: 16,
    color: '#888',
  },
});

export default SearchScreen;
