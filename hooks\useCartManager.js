import { useState, useEffect, useCallback } from 'react';
import { collection, onSnapshot, addDoc, doc, updateDoc, increment } from 'firebase/firestore';
import { onAuthStateChanged } from 'firebase/auth';
import { db, auth } from '../firebase.config';

export const useCartManager = () => {
  const [cartItems, setCartItems] = useState([]);
  const [showCartAnimation, setShowCartAnimation] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);

  // Listen to authentication state changes
  useEffect(() => {
    const unsubscribeAuth = onAuthStateChanged(auth, (user) => {
      if (user) {
        setCurrentUserId(user.uid);
      } else {
        setCurrentUserId(null);
        setCartItems([]); // Clear cart items when user logs out
      }
    });

    return () => unsubscribeAuth();
  }, []);

  // Setup cart listener for real-time updates
  useEffect(() => {
    let unsubscribeCart = () => { };

    const setupCartListener = async () => {
      if (!currentUserId) {
        console.log("[CartManager] No authenticated user, skipping cart items fetch");
        setCartItems([]);
        return;
      }

      try {
        // Verify authentication token is valid before fetching
        if (!auth.currentUser) {
          console.log("[CartManager] No current user in auth, skipping cart setup");
          setCartItems([]);
          return;
        }

        try {
          await auth.currentUser.getIdToken(true);
        } catch (tokenError) {
          console.error("[CartManager] Failed to get auth token for cart fetch:", tokenError);
          setCartItems([]);
          return;
        }

        const cartCollectionRef = collection(db, 'users', currentUserId, 'cart');

        // Use onSnapshot to listen for real-time updates to the cart
        unsubscribeCart = onSnapshot(cartCollectionRef, (snapshot) => {
          // Double-check that user is still authenticated
          if (!auth.currentUser || auth.currentUser.uid !== currentUserId) {
            console.log("[CartManager] User no longer authenticated, ignoring cart update");
            return;
          }

          try {
            // Get unique item IDs by using itemId field instead of document ID
            const cartItemsData = snapshot.docs.map(doc => doc.data());
            const uniqueItemIds = [...new Set(cartItemsData.map(item => item.itemId))];
            setCartItems(uniqueItemIds);
            console.log(`[CartManager] Cart updated: ${uniqueItemIds.length} items`);
          } catch (dataError) {
            console.error("[CartManager] Error processing cart data:", dataError);
            setCartItems([]);
          }
        }, (error) => {
          console.error("[CartManager] Error in cart listener:", error);
          if (error.code === 'permission-denied') {
            console.log("[CartManager] Permission denied for cart, user might need to re-authenticate");
            // Clear cart items on permission error
            setCartItems([]);
          }
        });
      } catch (error) {
        console.error("[CartManager] Error setting up cart listener:", error);
        setCartItems([]);
      }
    };

    if (currentUserId) {
      setupCartListener();
    }

    // Clean up the listener when the component unmounts or currentUserId changes
    return () => {
      unsubscribeCart();
    };
  }, [currentUserId]);

  // Add item to cart - Optimized for performance
  const addToCart = useCallback(async (item) => {
    if (!currentUserId || !item) {
      console.log("[CartManager] Cannot add to cart: missing user or item");
      return;
    }

    console.log(`[CartManager] Adding item ${item.id} to cart`);

    // Optimistic update: Update local state immediately for instant UI feedback
    setCartItems(prev => {
      if (!prev.includes(item.id)) {
        return [...prev, item.id];
      }
      return prev;
    });

    // Show cart animation immediately
    setShowCartAnimation(true);
    setTimeout(() => setShowCartAnimation(false), 1500);

    // Perform database operations in the background (non-blocking)
    Promise.allSettled([
      // Operation 1: Add to cart collection
      (async () => {
        try {
          const cartCollectionRef = collection(db, 'users', currentUserId, 'cart');
          await addDoc(cartCollectionRef, {
            itemId: item.id,
            title: item.title || 'Untitled',
            brand: item.brand || '',
            price: item.price || 0,
            imageUrl: item.imageUrl || '',
            category: item.category || '',
            addedAt: new Date(),
            size: item.size || '',
            color: item.color || ''
          });
          console.log(`[CartManager] Item ${item.id} added to cart collection`);
        } catch (error) {
          console.error('[CartManager] Error adding to cart collection:', error);
          // Rollback optimistic update on failure
          setCartItems(prev => prev.filter(id => id !== item.id));
          throw error;
        }
      })(),

      // Operation 2: Update view count
      (async () => {
        try {
          const itemRef = doc(db, 'clothingItems', item.id);
          await updateDoc(itemRef, {
            viewCount: increment(1)
          });
          console.log(`[CartManager] Updated viewCount for item ${item.id}`);
        } catch (error) {
          console.error('[CartManager] Error updating view count:', error);
          // View count update failure is not critical, don't rollback
        }
      })()
    ]).then((results) => {
      // Check if cart addition failed and handle rollback
      const cartResult = results[0];
      if (cartResult.status === 'rejected') {
        console.error('[CartManager] Cart addition failed, rolling back optimistic update');
        // The rollback is already handled in the catch block above
      } else {
        console.log(`[CartManager] Item ${item.id} successfully added to cart`);
      }

      // Log view count update result
      const viewCountResult = results[1];
      if (viewCountResult.status === 'rejected') {
        console.error('[CartManager] View count update failed:', viewCountResult.reason);
      }
    });
  }, [currentUserId]);

  // Check if item is in cart
  const isInCart = useCallback((itemId) => {
    return cartItems.includes(itemId);
  }, [cartItems]);

  // Get cart count
  const getCartCount = useCallback(() => {
    return cartItems.length;
  }, [cartItems]);

  return {
    cartItems,
    showCartAnimation,
    addToCart,
    isInCart,
    getCartCount,
    setShowCartAnimation
  };
};
