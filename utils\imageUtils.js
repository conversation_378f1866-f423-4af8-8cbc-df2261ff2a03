import * as ImageManipulator from 'expo-image-manipulator';

/**
 * Image quality utility functions for optimizing image loading
 * 
 * This utility provides functions to:
 * 1. Generate low-quality image URLs for card views
 * 2. Generate high-quality image URLs for detail views
 * 3. Transform existing image URLs to different quality levels
 */

// Constants for image quality
const IMAGE_QUALITY = {
  LOW: 'low',
  HIGH: 'high'
};

// Width constants for different views
const IMAGE_WIDTHS = {
  CARD: 400,    // Lower resolution for cards
  DETAIL: 1080  // Higher resolution for detail view
};

// Quality settings for compression
const COMPRESSION_QUALITY = {
  LOW: 0.3,     // More compression for cards
  HIGH: 0.7     // Less compression for detail view
};

/**
 * Get a modified URL for an image with quality parameters
 * 
 * This function adds quality parameters to Firebase Storage URLs
 * or returns the original URL for other sources
 * 
 * @param {string} imageUrl - The original image URL
 * @param {string} quality - The desired quality (low or high)
 * @returns {string} - The modified URL with quality parameters
 */
export const getOptimizedImageUrl = (imageUrl, quality = IMAGE_QUALITY.HIGH) => {
  if (!imageUrl) return imageUrl;
  
  try {
    // Check if this is a Firebase Storage URL
    if (imageUrl.includes('firebasestorage.googleapis.com')) {
      // Firebase Storage URLs can accept a '_sz' parameter for resizing
      const separator = imageUrl.includes('?') ? '&' : '?';
      
      // Determine the size based on quality
      const size = quality === IMAGE_QUALITY.LOW 
        ? IMAGE_WIDTHS.CARD 
        : IMAGE_WIDTHS.HIGH;
      
      // Add the size parameter to the URL
      return `${imageUrl}${separator}_sz=${size}`;
    }
    
    // For non-Firebase URLs, return the original
    return imageUrl;
  } catch (error) {
    console.error('Error optimizing image URL:', error);
    return imageUrl; // Return original URL if there's an error
  }
};

/**
 * Get a low-quality version of an image URL for card views
 * 
 * @param {string} imageUrl - The original image URL
 * @returns {string} - URL optimized for card views
 */
export const getLowQualityImageUrl = (imageUrl) => {
  return getOptimizedImageUrl(imageUrl, IMAGE_QUALITY.LOW);
};

/**
 * Get a high-quality version of an image URL for detail views
 * 
 * @param {string} imageUrl - The original image URL
 * @returns {string} - URL optimized for detail views
 */
export const getHighQualityImageUrl = (imageUrl) => {
  return getOptimizedImageUrl(imageUrl, IMAGE_QUALITY.HIGH);
};

/**
 * Compress an image in memory before uploading
 * 
 * @param {string} uri - The local URI of the image
 * @param {string} quality - The desired quality (low or high)
 * @returns {Promise<string>} - The URI of the compressed image
 */
export const compressImage = async (uri, quality = IMAGE_QUALITY.HIGH) => {
  if (!uri) return null;
  
  try {
    // Determine resize width based on quality
    const resizeWidth = quality === IMAGE_QUALITY.LOW 
      ? IMAGE_WIDTHS.CARD 
      : IMAGE_WIDTHS.HIGH;
    
    // Determine compression quality
    const compressionQuality = quality === IMAGE_QUALITY.LOW 
      ? COMPRESSION_QUALITY.LOW 
      : COMPRESSION_QUALITY.HIGH;
    
    // Use ImageManipulator to resize and compress the image
    const manipResult = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width: resizeWidth } }],
      { compress: compressionQuality, format: ImageManipulator.SaveFormat.JPEG }
    );
    
    return manipResult.uri;
  } catch (err) {
    console.error('Image compression failed:', err);
    return uri; // Return original URI if compression fails
  }
};

export default {
  IMAGE_QUALITY,
  IMAGE_WIDTHS,
  COMPRESSION_QUALITY,
  getOptimizedImageUrl,
  getLowQualityImageUrl,
  getHighQualityImageUrl,
  compressImage
};
