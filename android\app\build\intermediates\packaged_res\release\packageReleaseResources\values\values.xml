<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="colorPrimary">#023c69</color>
    <color name="colorPrimaryDark">#ffffff</color>
    <color name="iconBackground">#ffffff</color>
    <color name="splashscreen_background">#ffffff</color>
    <integer name="react_native_dev_server_port">8081</integer>
    <string name="app_name">SwipeSense</string>
    <string name="default_web_client_id" translatable="false">900945998905-nk13kuphejntned3ijhtbkm9o25i6tj7.apps.googleusercontent.com</string>
    <string name="expo_runtime_version">exposdk:53.0.0</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <string name="gcm_defaultSenderId" translatable="false">900945998905</string>
    <string name="google_api_key" translatable="false">AIzaSyDetCH2HxnF4mNBNIoLF2caqC0UI_8IbfE</string>
    <string name="google_app_id" translatable="false">1:900945998905:android:5d82969b0911af703a49e8</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyDetCH2HxnF4mNBNIoLF2caqC0UI_8IbfE</string>
    <string name="google_storage_bucket" translatable="false">styleswipe-67cb3.firebasestorage.app</string>
    <string name="project_id" translatable="false">styleswipe-67cb3</string>
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="android:windowOptOutEdgeToEdgeEnforcement" ns1:targetApi="35">true</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="AppTheme">
    <item name="android:windowBackground">@drawable/ic_launcher_background</item>
  </style>
</resources>