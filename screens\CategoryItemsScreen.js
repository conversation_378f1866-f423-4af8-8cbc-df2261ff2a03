import React from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getLowQualityImageUrl } from '../utils/imageUtils';

const CategoryItemsScreen = ({ route, navigation }) => {
  const { category, items, userId, onRemoveItem } = route.params;

  const renderItem = ({ item }) => (
    <View style={styles.itemRow}>
      <TouchableOpacity
        style={styles.itemContent}
        onPress={() => navigation.navigate('ItemDetails', { itemId: item.id })}
        activeOpacity={0.7}
      >
        <Image source={{ uri: getLowQualityImageUrl(item.imageUrl) }} style={styles.image} />
        <View style={{ flex: 1 }}>
          <Text style={styles.title}>{item.title}</Text>
          <Text style={styles.brand}>{item.brand || 'No brand'}</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => {
          // Call the removeFromWishlist function passed from the parent screen
          if (onRemoveItem) {
            navigation.setParams({
              items: items.filter(i => i.id !== item.id)
            });
            onRemoveItem(item.id, item.wishlistDocId);
          }
        }}
      >
        <Ionicons name="close-circle" size={24} color="#FF6B6B" />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.headerContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back-outline" size={24} color="#FF6B6B" />
          </TouchableOpacity>
          <Text style={styles.header}>{category}</Text>
          <View style={styles.placeholderButton} />
        </View>

        <FlatList
          data={items}
          keyExtractor={item => item.uniqueKey || `${item.id}_${item.wishlistDocId}`}
          renderItem={renderItem}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="folder-open-outline" size={60} color="#ccc" style={styles.emptyIcon} />
              <Text style={styles.empty}>No items in this category</Text>
            </View>
          }
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f8f8f8',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  header: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    width: 40,
  },
  placeholderButton: {
    width: 40,
  },
  listContent: {
    padding: 16,
    paddingBottom: 30,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    marginBottom: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    elevation: 1,
  },
  itemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  removeButton: {
    padding: 8,
    marginLeft: 8,
  },
  image: {
    width: 70,
    height: 70,
    borderRadius: 8,
    marginRight: 14,
    backgroundColor: '#eee',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 4,
  },
  brand: {
    fontSize: 14,
    color: '#888',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 40,
  },
  emptyIcon: {
    marginBottom: 15,
  },
  empty: {
    textAlign: 'center',
    color: '#888',
    fontSize: 16,
  },
});

export default CategoryItemsScreen;
