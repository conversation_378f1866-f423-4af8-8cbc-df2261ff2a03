import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  ScrollView,
  Keyboard
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db, functions } from '../firebase.config';
import { collection, query, where, getDocs, doc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import VerificationCodeInput from '../components/VerificationCodeInput';
import userStateManager from '../utils/userStateManager';

const SellerCodeVerificationScreen = ({ navigation, route }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [verificationComplete, setVerificationComplete] = useState(false);
  const user = auth.currentUser;

  useEffect(() => {
    // Check if user is already verified
    const checkVerificationStatus = async () => {
      console.log('Checking verification status, user:', user ? 'exists' : 'null');

      if (!user) {
        // Navigate to Welcome screen if no user is logged in
        console.log('No user found, redirecting to Welcome screen');
        navigation.reset({
          index: 0,
          routes: [{ name: 'Welcome' }],
        });
        return;
      }

      try {
        const userDocRef = doc(db, 'users', user.uid);
        const userDocSnapshot = await getDoc(userDocRef); // Changed from getDocs to getDoc

        console.log('User document exists:', userDocSnapshot.exists());

        if (userDocSnapshot.exists()) {
          const userData = userDocSnapshot.data();
          console.log('User verification status:', userData.sellerVerificationStatus);

          if (userData.sellerVerificationStatus === 'verified') {
            // User is already verified, redirect to main app
            console.log('User is already verified, redirecting to MainApp');
            navigation.reset({
              index: 0,
              routes: [{ name: 'MainApp' }],
            });
          }
        }
      } catch (error) {
        console.error('Error checking verification status:', error);
      }
    };

    checkVerificationStatus();
  }, [user, navigation]);

  // We no longer need to send the verification code from this screen
  // It's now sent automatically from the VerificationPendingScreen

  const handleVerifyCode = async (code) => {
    if (!code || code.length !== 6) {
      setError('Please enter a valid 6-character verification code');
      return;
    }

    setLoading(true);
    setError('');
    Keyboard.dismiss();

    try {
      if (!user) {
        Alert.alert('Error', 'User not found. Please log in again.');
        navigation.reset({
          index: 0,
          routes: [{ name: 'Welcome' }],
        });
        return;
      }

      console.log('Verifying code for user:', user.uid);
      console.log('Verification code entered:', code);

      // Query the sellerVerifications collection to find the matching verification
      const verificationQuery = query(
        collection(db, 'sellerVerifications'),
        where('userId', '==', user.uid),
        where('verificationCode', '==', code)
      );

      const querySnapshot = await getDocs(verificationQuery);
      console.log('Verification query results:', querySnapshot.empty ? 'No matches' : `${querySnapshot.size} matches found`);

      if (querySnapshot.empty) {
        // Try a more lenient query just to check if there are any verifications for this user
        const userVerificationsQuery = query(
          collection(db, 'sellerVerifications'),
          where('userId', '==', user.uid)
        );

        const userVerifications = await getDocs(userVerificationsQuery);
        console.log('User has verification documents:', userVerifications.empty ? 'No' : `Yes (${userVerifications.size})`);

        if (!userVerifications.empty) {
          // Show the actual verification code for debugging purposes
          const actualCodes = userVerifications.docs.map(doc => doc.data().verificationCode);
          console.log('Actual verification codes for user:', actualCodes);
        }

        setError('Invalid verification code. Please check and try again.');
        setLoading(false);
        return;
      }

      // Get the verification document
      const verificationDoc = querySnapshot.docs[0];
      const verificationData = verificationDoc.data();
      console.log('Verification document found:', verificationDoc.id);

      // Update the user document to mark as verified AND ensure isSeller is set
      const userDocRef = doc(db, 'users', user.uid);
      await updateDoc(userDocRef, {
        sellerVerificationStatus: 'verified',
        isSeller: true, // Explicitly ensure isSeller is set to true
        verifiedAt: serverTimestamp()
      });
      console.log('User document updated with verified status and isSeller: true');

      // Update the verification document
      await updateDoc(doc(db, 'sellerVerifications', verificationDoc.id), {
        status: 'verified',
        verifiedAt: serverTimestamp()
      });
      console.log('Verification document updated');

      // Force refresh the user state using the userStateManager
      console.log('Forcing user state refresh using userStateManager');
      const refreshedUserData = await userStateManager.forceRefreshUserData();

      if (refreshedUserData) {
        console.log('User state refreshed successfully, refreshed data:', refreshedUserData);

        // Show success message
        setVerificationComplete(true);
        console.log('Verification complete, will navigate to MainApp in 2 seconds');

        // After a short delay, navigate to the main app
        setTimeout(() => {
          // Navigate to main app
          console.log('Navigating to MainApp');
          navigation.reset({
            index: 0,
            routes: [{ name: 'MainApp' }],
          });
        }, 2000);
      } else {
        console.error('Failed to refresh user state, falling back to reload approach');
        // Fallback to the old approach
        await user.getIdToken(true);
        await user.reload();

        setVerificationComplete(true);
        setTimeout(() => {
          navigation.reset({
            index: 0,
            routes: [{ name: 'MainApp' }],
          });
        }, 2000);
      }
    } catch (error) {
      console.error('Error verifying code:', error);
      setError('Failed to verify code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.iconContainer}>
          <Ionicons name="shield-checkmark-outline" size={80} color="#FF6B6B" />
        </View>

        <Text style={styles.title}>Verify Your Seller Account</Text>

        <Text style={styles.message}>
          Enter the verification code sent to your email by the admin.
          This code is required to verify your seller account.
        </Text>

        <VerificationCodeInput
          onCodeComplete={handleVerifyCode}
          error={error}
          loading={loading}
        />

        {loading && (
          <ActivityIndicator size="large" color="#FF6B6B" style={styles.loader} />
        )}

        {verificationComplete && (
          <View style={styles.successContainer}>
            <Ionicons name="checkmark-circle" size={60} color="#4CAF50" />
            <Text style={styles.successText}>Verification Complete!</Text>
            <Text style={styles.successSubtext}>Redirecting to your seller dashboard...</Text>
          </View>
        )}

        <TouchableOpacity
          style={styles.supportButton}
          onPress={() => {
            Alert.alert(
              'Need Help?',
              'If you haven\'t received your verification code or are having issues, please contact <NAME_EMAIL>',
              [{ text: 'OK' }]
            );
          }}
        >
          <Ionicons name="help-circle-outline" size={20} color="#FF6B6B" />
          <Text style={styles.supportButtonText}>Need Help?</Text>
        </TouchableOpacity>

        {/* Resend button removed - verification code is now sent from the VerificationPendingScreen */}

        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            console.log('Navigating to Welcome screen');
            // Simply navigate to the Welcome screen
            navigation.navigate('Welcome');
          }}
        >
          <Text style={styles.backButtonText}>
            Back to Welcome Screen
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    marginTop: 40,
    marginBottom: 20,
    padding: 20,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  loader: {
    marginVertical: 20,
  },
  successContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  successText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginTop: 10,
  },
  successSubtext: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  supportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 10,
    padding: 15,
    borderWidth: 1,
    borderColor: '#FF6B6B',
    borderRadius: 8,
    backgroundColor: 'white',
    width: '100%',
  },
  supportButtonText: {
    color: '#FF6B6B',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  backButton: {
    width: '100%',
    padding: 15,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  backButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  resendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    marginBottom: 10,
    padding: 15,
    borderWidth: 1,
    borderColor: '#4285F4',
    borderRadius: 8,
    backgroundColor: 'white',
    width: '100%',
  },
  resendButtonText: {
    color: '#4285F4',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default SellerCodeVerificationScreen;
