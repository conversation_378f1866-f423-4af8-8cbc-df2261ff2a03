import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Text,
  TouchableOpacity,
  Keyboard
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const VerificationCodeInput = ({ onCodeComplete, error, loading }) => {
  const [code, setCode] = useState('');
  const [focused, setFocused] = useState(false);
  const inputRef = useRef(null);

  // Format the code for display (add spaces)
  const formattedCode = code.replace(/(.{3})/g, '$1 ').trim();

  // Handle code changes
  const handleCodeChange = (text) => {
    // Remove any non-alphanumeric characters
    const cleanText = text.replace(/[^A-Z0-9]/g, '').toUpperCase();
    
    // Limit to 6 characters
    const limitedText = cleanText.slice(0, 6);
    
    setCode(limitedText);
    
    // If code is complete (6 characters), call the callback
    if (limitedText.length === 6) {
      onCodeComplete(limitedText);
      Keyboard.dismiss();
    }
  };

  // Focus the input when the component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.codeContainer,
          focused && styles.focusedCodeContainer,
          error && styles.errorCodeContainer
        ]}
        onPress={() => inputRef.current?.focus()}
        activeOpacity={0.8}
      >
        <TextInput
          ref={inputRef}
          style={styles.hiddenInput}
          value={code}
          onChangeText={handleCodeChange}
          maxLength={6}
          autoCapitalize="characters"
          keyboardType="default"
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          editable={!loading}
        />
        
        {/* Display the formatted code */}
        <View style={styles.codeDisplay}>
          {Array(6).fill(0).map((_, index) => (
            <View 
              key={index} 
              style={[
                styles.codeBox,
                focused && index === code.length && styles.activeCodeBox,
                error && styles.errorCodeBox
              ]}
            >
              <Text style={styles.codeText}>
                {code[index] || ''}
              </Text>
            </View>
          ))}
        </View>
      </TouchableOpacity>
      
      {error && (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={16} color="#FF3B30" />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      <TouchableOpacity 
        style={styles.clearButton} 
        onPress={() => {
          setCode('');
          inputRef.current?.focus();
        }}
        disabled={code.length === 0 || loading}
      >
        <Text style={[styles.clearButtonText, (code.length === 0 || loading) && styles.disabledText]}>
          Clear
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginBottom: 20,
  },
  codeContainer: {
    width: '100%',
    height: 60,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#f9f9f9',
  },
  focusedCodeContainer: {
    borderColor: '#FF6B6B',
  },
  errorCodeContainer: {
    borderColor: '#FF3B30',
  },
  hiddenInput: {
    position: 'absolute',
    width: 1,
    height: 1,
    opacity: 0,
  },
  codeDisplay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
    height: '100%',
  },
  codeBox: {
    width: 40,
    height: 40,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  activeCodeBox: {
    borderColor: '#FF6B6B',
    borderWidth: 2,
  },
  errorCodeBox: {
    borderColor: '#FF3B30',
  },
  codeText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 14,
    marginLeft: 4,
  },
  clearButton: {
    alignSelf: 'flex-end',
    marginTop: 8,
    padding: 4,
  },
  clearButtonText: {
    color: '#FF6B6B',
    fontSize: 14,
  },
  disabledText: {
    color: '#ccc',
  },
});

export default VerificationCodeInput;
