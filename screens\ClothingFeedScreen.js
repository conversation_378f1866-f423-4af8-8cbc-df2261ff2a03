import React, { useCallback, useState, useEffect, useRef } from 'react';
import { SafeAreaView, View, Alert } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { collection, query, where, getDocs, addDoc, doc, updateDoc, increment, deleteDoc, getDoc } from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import CollectionSelectionModal from '../components/CollectionSelectionModal';
import FeedHeader from '../components/FeedHeader';
import EnhancedCategoryFilter from '../components/EnhancedCategoryFilter';
import CardStack from '../components/CardStack';
import FeedAnimations from '../components/FeedAnimations';
import SearchModal from '../components/SearchModal';
import { useClothingFeed } from '../hooks/useClothingFeed';
import { useOptimizedSwipeActions } from '../hooks/useOptimizedSwipeActions';
import { useSwipeAnimations } from '../hooks/useSwipeAnimations';
import { styles } from './ClothingFeedScreen.styles';

const ClothingFeedScreen = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const categoryFilterRef = useRef(null);

  // Local state for wishlist status (overrides the hook-based one)
  const [localIsCurrentItemWishlisted, setLocalIsCurrentItemWishlisted] = useState(false);

  const {
    // State
    items,
    loading,
    backgroundLoading,
    error,
    activeCategory,
    noMoreItems,
    currentIndex,
    categories,
    filteredCategories,
    searchQuery,
    searchQueryHeader,
    searchModalVisible,
    showCollectionModal,
    isLoadingMoreCategories,
    currentUserPhotoURL,
    cartItems,
    showCartAnimation,
    isCurrentItemWishlisted,
    isSearchMode,
    searchResults,
    suggestedCategory,

    // Enhanced filtering state
    activeCategoryType,
    activeSpecificCategories,
    activeColor,

    // Actions
    setActiveCategory,
    setActiveCategoryType,
    setActiveSpecificCategories,
    setActiveColor,
    setCurrentIndex,
    setSearchQuery,
    setSearchQueryHeader,
    setSearchModalVisible,
    setShowCollectionModal,
    fetchClothingItems,
    addToCart,
    toggleWishlist,
    setInteractedItemIds,
    checkWishlistStatus,
    setIsCurrentItemWishlisted,
    searchItems,
    clearSearch,

    // Computed
    currentUserId
  } = useClothingFeed(navigation);

  // Initialize swipe animations
  const {
    likeAnimationOpacity,
    dislikeAnimationOpacity,
    likeAnimationScale,
    dislikeAnimationScale,
    triggerLikeAnimation,
    triggerDislikeAnimation
  } = useSwipeAnimations();

  // Initialize optimized swipe actions
  const { processSwipeCompletion } = useOptimizedSwipeActions({
    currentUserId,
    setInteractedItemIds,
    addToCart,
    triggerLikeAnimation,
    triggerDislikeAnimation
  });

  const handleSelectCollection = async (selectedCollection, itemId, itemData) => {
    console.log(`[ClothingFeed] handleSelectCollection called for item ${itemId} in collection ${selectedCollection.name}`);

    // The CollectionSelectionModal has already handled the database operations
    // We just need to update the local UI state
    setShowCollectionModal(false);

    // Directly update the local wishlist status for immediate UI feedback
    const currentItem = items[currentIndex];
    if (currentItem && currentItem.id === itemId) {
      console.log(`[ClothingFeed] Setting local wishlist status to true for current item ${itemId}`);
      setLocalIsCurrentItemWishlisted(true);
    }

    console.log(`[ClothingFeed] UI updated for item ${itemId} added to collection ${selectedCollection.name}`);
  };

  // Local function to check wishlist status
  const checkLocalWishlistStatus = useCallback(async (itemIdToCheck) => {
    if (!currentUserId || !itemIdToCheck) {
      setLocalIsCurrentItemWishlisted(false);
      return;
    }
    try {
      const wishlistQuery = query(
        collection(db, 'users', currentUserId, 'wishlist'),
        where('itemId', '==', itemIdToCheck)
      );
      const querySnapshot = await getDocs(wishlistQuery);
      setLocalIsCurrentItemWishlisted(!querySnapshot.empty);
      console.log(`[ClothingFeed] Local wishlist status for item ${itemIdToCheck}: ${!querySnapshot.empty}`);
    } catch (error) {
      console.error('[ClothingFeed] Error checking local wishlist status:', error);
      setLocalIsCurrentItemWishlisted(false);
    }
  }, [currentUserId]);

  // Monitor current item changes and update wishlist status
  useEffect(() => {
    if (items.length > 0 && currentIndex < items.length) {
      const currentItem = items[currentIndex];
      if (currentItem) {
        checkLocalWishlistStatus(currentItem.id);
      }
    } else {
      setLocalIsCurrentItemWishlisted(false);
    }
  }, [currentIndex, items, checkLocalWishlistStatus]);

  // Handle suggested category auto-scrolling and selection
  useEffect(() => {
    if (suggestedCategory && suggestedCategory !== activeCategory) {
      console.log(`[ClothingFeed] Auto-selecting suggested category: ${suggestedCategory}`);

      // Set the active category
      setActiveCategory(suggestedCategory);

      // Scroll to the category in the filter
      if (categoryFilterRef.current) {
        categoryFilterRef.current.scrollToCategory(suggestedCategory);
      }
    }
  }, [suggestedCategory, activeCategory, setActiveCategory]);

  const handleCategorySelect = (category) => {
    setActiveCategory(category);
    fetchClothingItems(true);
  };

  const handleSearchSubmit = async (query) => {
    console.log(`[ClothingFeed] Search submitted: "${query}"`);
    await searchItems(query);
  };

  const handleClearSearch = () => {
    console.log('[ClothingFeed] Clearing search');
    clearSearch();
    fetchClothingItems(true);
  };

  const handleItemPress = (item) => {
    navigation.navigate('ItemDetails', { itemId: item.id });
  };

  // Override the toggleWishlist function to show collection modal for adding items
  const handleWishlistToggle = async () => {
    if (!currentUserId) {
      console.log("[ClothingFeed] User not logged in, cannot wishlist.");
      return;
    }

    const currentItem = items[currentIndex];
    if (!currentItem) {
      console.log("[ClothingFeed] No current item to wishlist");
      return;
    }

    console.log(`[ClothingFeed] Bookmark button pressed for item ${currentItem.id}, isWishlisted: ${localIsCurrentItemWishlisted}`);

    if (localIsCurrentItemWishlisted) {
      // If already wishlisted, remove from all collections
      console.log(`[ClothingFeed] Removing item ${currentItem.id} from all collections`);
      await handleRemoveFromAllCollections(currentItem.id);
    } else {
      // If not wishlisted, show collection selection modal
      console.log(`[ClothingFeed] Showing collection modal for item ${currentItem.id}`);
      setShowCollectionModal(true);
    }
  };

  // Function to remove item from all collections
  const handleRemoveFromAllCollections = async (itemId) => {
    try {
      const userWishlistRef = collection(db, 'users', currentUserId, 'wishlist');
      const wishlistQuery = query(userWishlistRef, where('itemId', '==', itemId));
      const querySnapshot = await getDocs(wishlistQuery);

      if (!querySnapshot.empty) {
        // Remove from all collections and update counts
        await Promise.all(querySnapshot.docs.map(async (wishlistDoc) => {
          const collectionId = wishlistDoc.data().collectionId;

          // Delete the wishlist document
          await deleteDoc(doc(db, 'users', currentUserId, 'wishlist', wishlistDoc.id));
          console.log(`[ClothingFeed] Removed item ${itemId} from collection ${collectionId}`);

          // Update collection item count
          if (collectionId) {
            const collectionRef = doc(db, 'users', currentUserId, 'collections', collectionId);
            try {
              // Get the current collection to check if this was the last item
              const collectionDoc = await getDoc(collectionRef);

              if (collectionDoc.exists()) {
                const currentItemCount = collectionDoc.data().itemCount || 0;

                // If this was the last item, clear the latestItemImageUrl
                if (currentItemCount <= 1) {
                  await updateDoc(collectionRef, {
                    itemCount: 0,
                    latestItemImageUrl: null
                  });
                } else {
                  // Otherwise just decrement the count
                  await updateDoc(collectionRef, {
                    itemCount: increment(-1)
                  });

                  // If this was the item used as the collection thumbnail, update it
                  const currentItemData = items.find(item => item.id === itemId);
                  if (collectionDoc.data().latestItemImageUrl === currentItemData?.imageUrl) {
                    // We need to find another item in this collection to use as thumbnail
                    const otherItemsQuery = query(
                      collection(db, 'users', currentUserId, 'wishlist'),
                      where('collectionId', '==', collectionId),
                      where('itemId', '!=', itemId)
                    );
                    const otherItemsSnapshot = await getDocs(otherItemsQuery);

                    if (!otherItemsSnapshot.empty) {
                      // Get the first other item's details to use its image
                      const firstOtherItem = otherItemsSnapshot.docs[0].data();
                      const otherItemRef = doc(db, 'clothingItems', firstOtherItem.itemId);
                      const otherItemDoc = await getDoc(otherItemRef);

                      if (otherItemDoc.exists()) {
                        await updateDoc(collectionRef, {
                          latestItemImageUrl: otherItemDoc.data().imageUrl
                        });
                      }
                    }
                  }
                }
              }

              console.log(`[ClothingFeed] Updated collection ${collectionId} after item removal`);
            } catch (e) {
              console.error(`[ClothingFeed] Error updating collection ${collectionId}:`, e);
            }
          }
        }));

        // Decrement saveCount on the clothingItem
        const itemRef = doc(db, 'clothingItems', itemId);
        try {
          await updateDoc(itemRef, { saveCount: increment(-1) });
          console.log(`[ClothingFeed] Decremented saveCount for item ${itemId}`);
        } catch (e) {
          console.error(`[ClothingFeed] Error decrementing saveCount for item ${itemId}:`, e);
        }

        // Directly update the local wishlist status for immediate UI feedback
        const currentItem = items[currentIndex];
        if (currentItem && currentItem.id === itemId) {
          console.log(`[ClothingFeed] Directly setting local wishlist status to false for current item ${itemId}`);
          setLocalIsCurrentItemWishlisted(false);
        }

        console.log(`[ClothingFeed] Item ${itemId} removed from all collections`);
      }
    } catch (error) {
      console.error('[ClothingFeed] Error removing from collections:', error);
      Alert.alert('Error', 'Failed to remove from collections');
    }
  };

  const handleEndReached = () => {
    if (!noMoreItems && !backgroundLoading) {
      fetchClothingItems(false, true);
    }
  };

  return (
    <SafeAreaView style={[styles.safeArea, { paddingTop: insets.top }]}>
      <View style={styles.container}>
        {/* Header */}
        <FeedHeader
          currentUserPhotoURL={currentUserPhotoURL}
          currentUserId={currentUserId}
          searchQueryHeader={searchQueryHeader}
          setSearchQueryHeader={setSearchQueryHeader}
          setSearchModalVisible={setSearchModalVisible}
          cartItems={cartItems}
          navigation={navigation}
          onClearSearch={handleClearSearch}
          isSearchMode={isSearchMode}
          searchResultsCount={items.length}
        />

        {/* Enhanced Category Filter */}
        <EnhancedCategoryFilter
          activeCategoryType={activeCategoryType}
          activeSpecificCategories={activeSpecificCategories}
          activeColor={activeColor}
          onCategoryTypeSelect={(categoryType) => {
            setActiveCategoryType(categoryType);
            fetchClothingItems(true);
          }}
          onSpecificCategoriesChange={(categories) => {
            setActiveSpecificCategories(categories);
            fetchClothingItems(true);
          }}
          onColorSelect={(color) => {
            setActiveColor(color);
            fetchClothingItems(true);
          }}
          isLoadingMoreCategories={isLoadingMoreCategories}
          onEndReached={handleEndReached}
          autoScrollToCategory={suggestedCategory}
        />

        {/* Main Feed Area */}
        <View style={styles.feedArea}>
          <CardStack
            items={items}
            loading={loading}
            error={error}
            noMoreItems={noMoreItems}
            currentIndex={currentIndex}
            setCurrentIndex={setCurrentIndex}
            isCurrentItemWishlisted={localIsCurrentItemWishlisted}
            currentUserId={currentUserId}
            onItemPress={handleItemPress}
            onWishlistToggle={handleWishlistToggle}
            onAddToCart={addToCart}
            onRefresh={() => fetchClothingItems(true)}
            activeCategory={activeCategory}
            setActiveCategory={setActiveCategory}
            onSwipeComplete={processSwipeCompletion}
            fetchClothingItems={fetchClothingItems}
            backgroundLoading={backgroundLoading}
            triggerLikeAnimation={triggerLikeAnimation}
            triggerDislikeAnimation={triggerDislikeAnimation}
          />
        </View>

        {/* Animations */}
        <FeedAnimations
          likeAnimationOpacity={likeAnimationOpacity}
          dislikeAnimationOpacity={dislikeAnimationOpacity}
          likeAnimationScale={likeAnimationScale}
          dislikeAnimationScale={dislikeAnimationScale}
          showCartAnimation={showCartAnimation}
        />

        {/* Search Modal */}
        <SearchModal
          visible={searchModalVisible}
          onClose={() => setSearchModalVisible(false)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          categories={filteredCategories}
          onCategorySelect={handleCategorySelect}
          onSearchSubmit={handleSearchSubmit}
        />

        {/* Collection Selection Modal */}
        <CollectionSelectionModal
          visible={showCollectionModal}
          onClose={() => setShowCollectionModal(false)}
          onSelectCollection={handleSelectCollection}
          itemId={items[currentIndex]?.id}
          itemData={items[currentIndex]}
        />
      </View>
    </SafeAreaView>
  );
};

export default ClothingFeedScreen;
