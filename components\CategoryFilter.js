import React, { useRef, useEffect, useImperativeHandle, forwardRef } from 'react';
import { View, Text, TouchableOpacity, FlatList, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './CategoryFilter.styles';

const CategoryFilter = forwardRef(({
  categories,
  activeCategory,
  onCategorySelect,
  isLoadingMoreCategories,
  onEndReached,
  autoScrollToCategory
}, ref) => {
  const flatListRef = useRef(null);

  // Function to scroll to a specific category
  const scrollToCategory = (categoryToFind) => {
    if (!flatListRef.current || !categories || categories.length === 0) {
      return;
    }

    const categoryIndex = categories.findIndex(cat =>
      cat.toLowerCase() === categoryToFind.toLowerCase()
    );

    if (categoryIndex !== -1) {
      // Small delay to ensure the FlatList is ready
      setTimeout(() => {
        flatListRef.current?.scrollToIndex({
          index: categoryIndex,
          animated: true,
          viewPosition: 0.5, // Center the item in view
        });
      }, 100);
    }
  };

  // Expose scrollToCategory function to parent components
  useImperativeHandle(ref, () => ({
    scrollToCategory
  }), [categories]);

  // Auto-scroll when autoScrollToCategory prop changes
  useEffect(() => {
    if (autoScrollToCategory) {
      scrollToCategory(autoScrollToCategory);
    }
  }, [autoScrollToCategory, categories]);

  const renderCategoryItem = ({ item: category }) => {
    const isActive = activeCategory === category;

    return (
      <TouchableOpacity
        style={[
          styles.filterButton,
          isActive && styles.activeFilterButton
        ]}
        onPress={() => onCategorySelect(category)}
      >
        <Text style={[
          styles.filterButtonText,
          isActive && styles.activeFilterText
        ]}>
          {category}
        </Text>
        {isActive && (
          <Ionicons
            name="checkmark-circle"
            size={16}
            color="#fff"
            style={styles.activeIcon}
          />
        )}
      </TouchableOpacity>
    );
  };

  const renderFooter = () => {
    if (!isLoadingMoreCategories) return null;

    return (
      <View style={styles.categoryLoadingIndicator}>
        <ActivityIndicator size="small" color="#FF6B6B" />
      </View>
    );
  };

  return (
    <View style={styles.filterContainer}>
      <FlatList
        ref={flatListRef}
        data={categories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filterScrollContent}
        onEndReached={onEndReached}
        onEndReachedThreshold={0.5}
        windowSize={5}
        initialNumToRender={10}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        removeClippedSubviews={true}
        ListFooterComponent={renderFooter}
        onScrollToIndexFailed={(info) => {
          // Handle scroll to index failure gracefully
          console.log('Scroll to index failed:', info);
          setTimeout(() => {
            flatListRef.current?.scrollToOffset({
              offset: info.averageItemLength * info.index,
              animated: true,
            });
          }, 100);
        }}
      />
    </View>
  );
});

export default CategoryFilter;
