import { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  StyleSheet,
  ActivityIndicator,
  SafeAreaView,
  Platform,
  StatusBar,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  RefreshControl
} from 'react-native';
import {
  collection,
  getDocs,
  doc,
  getDoc,
  deleteDoc,
  addDoc,
  serverTimestamp,
  updateDoc,
  increment,
  query,
  where
} from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';

const UserWishlist = ({ route, navigation }) => {
  // Get userId from route params or fallback to current user
  const routeUserId = route.params?.userId;
  const [userId, setUserId] = useState(routeUserId || auth.currentUser?.uid);
  const [items, setItems] = useState([]);
  const [itemsByCategory, setItemsByCategory] = useState({});
  const [categories, setCategories] = useState([]);
  const [collections, setCollections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [collectionsLoading, setCollectionsLoading] = useState(true);
  const [isTabScreen, setIsTabScreen] = useState(false);
  const [error, setError] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [viewMode, setViewMode] = useState('collections'); // 'collections' or 'all'
  const [optionsModalVisible, setOptionsModalVisible] = useState(false); // New state for custom options modal
  const [selectedCollection, setSelectedCollection] = useState(null); // State to keep track of the selected collection for options

  // Check if this screen is being shown as a tab or as a stack screen
  useEffect(() => {
    // If we're in the tab navigator, there won't be a navigation.canGoBack()
    // or the route.name will be 'Saved'
    const checkIfTabScreen = () => {
      const state = navigation.getState();
      const routes = state.routes;
      const currentRoute = routes[state.index];
      setIsTabScreen(currentRoute.name === 'Saved');
    };

    checkIfTabScreen();
  }, [navigation]);

  // Make sure we have a valid userId
  useEffect(() => {
    if (!userId && auth.currentUser) {
      console.log("Setting userId from auth.currentUser");
      setUserId(auth.currentUser.uid);
    }
  }, [userId]);

  const fetchWishlist = useCallback(async () => {
    if (!userId) {
      console.log("No userId available, cannot fetch wishlist");
      setError("User ID not available");
      setLoading(false);
      return;
    }

    console.log(`Fetching wishlist for user: ${userId}`);
    setLoading(true);
    setError(null);

    try {
      const wishlistSnap = await getDocs(collection(db, 'users', userId, 'wishlist'));
      console.log(`Found ${wishlistSnap.docs.length} wishlist items`);

      const wishlistDocs = wishlistSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      if (wishlistDocs.length === 0) {
        console.log("No items in wishlist");
        setItems([]);
        setItemsByCategory({});
        setCategories([]);
        setLoading(false);
        return;
      }

      const itemIds = wishlistDocs.map(doc => doc.itemId);
      console.log(`Fetching details for ${itemIds.length} items: ${itemIds.join(', ')}`);

      const itemPromises = itemIds.map(id => getDoc(doc(db, 'clothingItems', id)));
      const itemDocs = await Promise.all(itemPromises);

      // Combine the wishlist document IDs with the item data
      const itemsWithWishlistIds = itemDocs
        .filter(d => d.exists())
        .map(d => {
          const itemId = d.id;
          const wishlistDoc = wishlistDocs.find(wd => wd.itemId === itemId);
          return {
            id: d.id,
            wishlistDocId: wishlistDoc?.id, // Store the wishlist document ID
            ...d.data()
          };
        });

      console.log(`Successfully loaded ${itemsWithWishlistIds.length} items for wishlist`);

      // Group items by category
      const groupedItems = {};
      const categoryList = [];

      // Create a map to track duplicate items in categories
      const categoryItemMap = new Map();

      itemsWithWishlistIds.forEach(item => {
        const category = item.category || 'Uncategorized';

        if (!groupedItems[category]) {
          groupedItems[category] = [];
          categoryList.push(category);
          categoryItemMap.set(category, new Set());
        }

        // Create a unique key for this item in this category
        const uniqueKey = `${item.id}_${item.wishlistDocId}`;

        // Check if we've already added this item to this category
        if (!categoryItemMap.get(category).has(uniqueKey)) {
          categoryItemMap.get(category).add(uniqueKey);

          // Add a uniqueKey property to the item for use in the FlatList
          groupedItems[category].push({
            ...item,
            uniqueKey
          });
        }
      });

      // Sort items within each category by most recently added (if we have timestamp data)
      Object.keys(groupedItems).forEach(category => {
        groupedItems[category].sort((a, b) => {
          const aTimestamp = a.addedAt || new Date(0);
          const bTimestamp = b.addedAt || new Date(0);
          return bTimestamp - aTimestamp;
        });
      });

      // Sort categories alphabetically
      categoryList.sort();

      setItems(itemsWithWishlistIds);
      setItemsByCategory(groupedItems);
      setCategories(categoryList);
    } catch (e) {
      console.error("Error fetching wishlist:", e);
      setError("Failed to load saved items");
      setItems([]);
      setItemsByCategory({});
      setCategories([]);
    }
    setLoading(false);
  }, [userId]);

  // Fetch user's collections
  const fetchCollections = useCallback(async () => {
    if (!userId) {
      console.log("No userId available, cannot fetch collections");
      setCollectionsLoading(false);
      return;
    }

    console.log(`Fetching collections for user: ${userId}`);
    setCollectionsLoading(true);

    try {
      const collectionsRef = collection(db, 'users', userId, 'collections');
      const collectionsSnap = await getDocs(collectionsRef);

      const collectionsData = collectionsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      }));

      // Sort by creation date (newest first)
      collectionsData.sort((a, b) => b.createdAt - a.createdAt);

      // Add "All Items" as the first option with the correct unique item count
      // First, get all unique item IDs from the wishlist
      const wishlistRef = collection(db, 'users', userId, 'wishlist');
      const wishlistSnap = await getDocs(wishlistRef);
      const uniqueItemIds = new Set();

      wishlistSnap.docs.forEach(doc => {
        const itemId = doc.data().itemId;
        if (itemId) {
          uniqueItemIds.add(itemId);
        }
      });

      const uniqueItemCount = uniqueItemIds.size;
      console.log(`Found ${uniqueItemCount} unique items across all collections`);

      collectionsData.unshift({
        id: 'all',
        name: 'All Saved Items',
        isDefault: true,
        uniqueItemCount: uniqueItemCount
      });

      console.log(`Found ${collectionsData.length - 1} collections`);
      setCollections(collectionsData);
    } catch (error) {
      console.error("Error fetching collections:", error);
    } finally {
      setCollectionsLoading(false);
    }
  }, [userId]);

  // Create a new collection
  const createCollection = async () => {
    if (!newCollectionName.trim()) {
      Alert.alert('Error', 'Please enter a collection name');
      return;
    }

    if (!userId) return;

    try {
      // Check if collection with this name already exists
      if (collections.some(c => c.name.toLowerCase() === newCollectionName.trim().toLowerCase())) {
        Alert.alert('Error', 'A collection with this name already exists');
        return;
      }

      // Create new collection
      const collectionsRef = collection(db, 'users', userId, 'collections');
      const newCollection = {
        name: newCollectionName.trim(),
        createdAt: serverTimestamp(),
        itemCount: 0
      };

      await addDoc(collectionsRef, newCollection);

      // Refresh collections
      fetchCollections();
      setNewCollectionName('');
      setShowCreateModal(false);
    } catch (error) {
      console.error('Error creating collection:', error);
      Alert.alert('Error', 'Failed to create new collection');
    }
  };

  // Delete a collection
  const deleteCollection = async (collectionId, collectionName) => {
    if (!userId || !collectionId) return;

    // Don't allow deleting the "All Saved Items" collection
    if (collectionId === 'all') {
      Alert.alert('Cannot Delete', 'The "All Saved Items" collection cannot be deleted.');
      return;
    }

    Alert.alert(
      "Delete Collection",
      `Are you sure you want to delete "${collectionName}"? All items in this collection will be completely removed from your saved items.`,
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              setCollectionsLoading(true);
              setLoading(true); // Also set main loading state to true

              // 1. Get all wishlist items associated with this collection
              const wishlistRef = collection(db, 'users', userId, 'wishlist');
              const wishlistQuery = query(wishlistRef, where('collectionId', '==', collectionId));
              const wishlistSnap = await getDocs(wishlistQuery);

              // Store the items to remove from the UI
              const itemsToRemove = wishlistSnap.docs.map(doc => ({
                itemId: doc.data().itemId,
                wishlistDocId: doc.id
              }));

              // 2. Delete the collection document
              await deleteDoc(doc(db, 'users', userId, 'collections', collectionId));

              // 3. Delete all wishlist items associated with this collection
              const deletePromises = wishlistSnap.docs.map(doc =>
                deleteDoc(doc.ref)
              );

              await Promise.all(deletePromises);

              // 4. Update the UI by removing the deleted items
              setItems(prevItems => {
                // Filter out the items that were in the deleted collection
                const updatedItems = prevItems.filter(item =>
                  !itemsToRemove.some(i => i.itemId === item.id && i.wishlistDocId === item.wishlistDocId)
                );

                // 5. Check if we have any items left after deletion
                // Get a list of unique item IDs that remain
                const remainingItemIds = new Set();
                updatedItems.forEach(item => {
                  remainingItemIds.add(item.id);
                });

                // Initial count based on remaining items in memory
                const uniqueItemCount = remainingItemIds.size;
                console.log(`After deletion: ${updatedItems.length} total items, ${uniqueItemCount} unique items`);

                // Update the collections state with initial count
                setCollections(prevCollections => {
                  // Remove the deleted collection
                  const updatedCollections = prevCollections.filter(c => c.id !== collectionId);

                  // Update the "All Saved Items" collection count
                  const allItemsIndex = updatedCollections.findIndex(c => c.id === 'all');
                  if (allItemsIndex !== -1) {
                    updatedCollections[allItemsIndex] = {
                      ...updatedCollections[allItemsIndex],
                      uniqueItemCount: uniqueItemCount
                    };
                  }

                  return updatedCollections;
                });

                // Then get a more accurate count by querying all wishlist items
                (async () => {
                  try {
                    const wishlistRef = collection(db, 'users', userId, 'wishlist');
                    const wishlistSnap = await getDocs(wishlistRef);
                    const allUniqueItemIds = new Set();

                    wishlistSnap.docs.forEach(doc => {
                      const itemId = doc.data().itemId;
                      if (itemId) {
                        allUniqueItemIds.add(itemId);
                      }
                    });

                    const accurateUniqueItemCount = allUniqueItemIds.size;
                    console.log(`Accurate count after collection deletion: ${accurateUniqueItemCount} unique items across all collections`);

                    // Update with the accurate count
                    setCollections(prevCollections => {
                      return prevCollections.map(c => {
                        if (c.id === 'all') {
                          return {
                            ...c,
                            uniqueItemCount: accurateUniqueItemCount
                          };
                        }
                        return c;
                      });
                    });
                  } catch (error) {
                    console.error("Error updating accurate item count after collection deletion:", error);
                  }
                })();

                // Also update the categories state if we've removed all items
                if (updatedItems.length === 0) {
                  setCategories([]);
                  setItemsByCategory({});
                }

                return updatedItems;
              });

              // 6. Perform a full refresh of data to ensure everything is in sync
              await fetchWishlist();
              await fetchCollections();

            } catch (error) {
              console.error('Error deleting collection:', error);
              Alert.alert('Error', 'Failed to delete collection');
            } finally {
              setCollectionsLoading(false);
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  // Rename a collection
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [collectionToRename, setCollectionToRename] = useState(null);
  const [newCollectionRename, setNewCollectionRename] = useState('');

  const openRenameModal = (collection) => {
    // Don't allow renaming the "All Saved Items" collection
    if (collection.id === 'all') {
      Alert.alert('Cannot Rename', 'The "All Saved Items" collection cannot be renamed.');
      return;
    }

    setCollectionToRename(collection);
    setNewCollectionRename(collection.name);
    setShowRenameModal(true);
  };

  const renameCollection = async () => {
    if (!userId || !collectionToRename || !newCollectionRename.trim()) {
      setShowRenameModal(false);
      return;
    }

    try {
      // Check if collection with this name already exists (excluding the current collection)
      if (collections.some(c =>
        c.id !== collectionToRename.id &&
        c.name.toLowerCase() === newCollectionRename.trim().toLowerCase()
      )) {
        Alert.alert('Error', 'A collection with this name already exists');
        return;
      }

      // Update the collection name
      const collectionRef = doc(db, 'users', userId, 'collections', collectionToRename.id);
      await updateDoc(collectionRef, {
        name: newCollectionRename.trim()
      });

      // Refresh collections
      fetchCollections();
      setShowRenameModal(false);

    } catch (error) {
      console.error('Error renaming collection:', error);
      Alert.alert('Error', 'Failed to rename collection');
    }
  };

  // Use regular useEffect for initial load
  useEffect(() => {
    if (userId) {
      // Initial load: Fetch collections then wishlist
      fetchCollections().then(() => {
        fetchWishlist();
      });
    }
    // Removed fetchWishlist and fetchCollections from dependencies here
    // as useFocusEffect will handle subsequent refreshes.
    // This prevents double-fetching on initial mount if userId is already available.
  }, [userId]);

  // Use useFocusEffect to refresh data when screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log("Wishlist screen focused, refreshing data");
      if (userId) {
        // Fetch collections first, then wishlist items.
        // fetchCollections will update the 'All Saved Items' count based on the latest wishlist.
        // fetchWishlist will update the categorized view if the user switches to it.
        fetchCollections().then(() => {
          fetchWishlist();
        });
      }
      return () => {
        // Cleanup function when screen is unfocused
        console.log("Wishlist screen unfocused");
      };
    }, [userId, fetchCollections, fetchWishlist]) // Dependencies ensure this runs when they change or on focus
  );

  const removeFromWishlist = async (itemId, wishlistDocId) => {
    Alert.alert(
      "Remove Item",
      "Are you sure you want to remove this item from your saved collection?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Remove",
          onPress: async () => {
            try {
              // Get the wishlist document to find its collection
              const wishlistDocRef = doc(db, 'users', userId, 'wishlist', wishlistDocId);
              const wishlistDoc = await getDoc(wishlistDocRef);

              if (wishlistDoc.exists()) {
                const collectionId = wishlistDoc.data().collectionId;

                // Delete the wishlist document
                await deleteDoc(wishlistDocRef);                // Update the collection item count if the collection exists and isn't the "all" collection
                if (collectionId && collectionId !== 'all') {
                  try {
                    // First check if the collection exists
                    const collectionRef = doc(db, 'users', userId, 'collections', collectionId);
                    const collectionDoc = await getDoc(collectionRef);

                    if (collectionDoc.exists()) {
                      const currentItemCount = collectionDoc.data().itemCount || 0;
                      const removedItem = currentItems.find(item => item.id === itemId && item.wishlistDocId === wishlistDocId);

                      // If this was the last item, clear the latestItemImageUrl
                      if (currentItemCount <= 1) {
                        await updateDoc(collectionRef, {
                          itemCount: 0,
                          latestItemImageUrl: null
                        });
                      } else {
                        // Otherwise just decrement the count
                        await updateDoc(collectionRef, {
                          itemCount: increment(-1)
                        });

                        // If this was the item used as the collection thumbnail, update it
                        if (removedItem && collectionDoc.data().latestItemImageUrl === removedItem.imageUrl) {
                          // Find another item in this collection to use as thumbnail
                          const remainingItemsInCollection = currentItems.filter(item => 
                            item.collectionId === collectionId && 
                            !(item.id === itemId && item.wishlistDocId === wishlistDocId)
                          );
                          if (remainingItemsInCollection.length > 0) {
                            await updateDoc(collectionRef, {
                              latestItemImageUrl: remainingItemsInCollection[0].imageUrl
                            });
                          }
                        }
                      }
                    } else {
                      console.log(`Collection ${collectionId} does not exist, skipping update`);
                    }
                  } catch (collectionError) {
                    console.error("Error updating collection count:", collectionError);
                    // Continue with the rest of the function even if updating the collection fails
                  }
                }

                // Update the UI by removing the item
                setItems(currentItems => {
                  const updatedItems = currentItems.filter(item =>
                    !(item.id === itemId && item.wishlistDocId === wishlistDocId)
                  );

                  // Get a list of unique item IDs that remain
                  const remainingItemIds = new Set();
                  updatedItems.forEach(item => {
                    remainingItemIds.add(item.id);
                  });

                  // After removing an item, we need to recalculate the unique items
                  // For now, use the remaining items we have in memory
                  const uniqueItemCount = remainingItemIds.size;
                  console.log(`After item removal: ${updatedItems.length} total items, ${uniqueItemCount} unique items`);

                  // Then update the count asynchronously with a more accurate count
                  // by querying all wishlist items
                  (async () => {
                    try {
                      const wishlistRef = collection(db, 'users', userId, 'wishlist');
                      const wishlistSnap = await getDocs(wishlistRef);
                      const allUniqueItemIds = new Set();

                      wishlistSnap.docs.forEach(doc => {
                        const itemId = doc.data().itemId;
                        if (itemId) {
                          allUniqueItemIds.add(itemId);
                        }
                      });

                      const accurateUniqueItemCount = allUniqueItemIds.size;
                      console.log(`Accurate count after item removal: ${accurateUniqueItemCount} unique items across all collections`);

                      // Update with the accurate count
                      setCollections(prevCollections => {
                        return prevCollections.map(c => {
                          if (c.id === 'all') {
                            return {
                              ...c,
                              uniqueItemCount: accurateUniqueItemCount
                            };
                          }
                          return c;
                        });
                      });
                    } catch (error) {
                      console.error("Error updating accurate item count:", error);
                    }
                  })();

                  // Initial update with the local count
                  setCollections(prevCollections => {
                    return prevCollections.map(c => {
                      if (c.id === 'all') {
                        return {
                          ...c,
                          uniqueItemCount: uniqueItemCount
                        };
                      }
                      return c;
                    });
                  });

                  // Also update the categories state if we've removed all items
                  if (updatedItems.length === 0) {
                    setCategories([]);
                    setItemsByCategory({});
                  }

                  return updatedItems;
                });

                // Refresh collections to update counts
                fetchCollections();
              }
            } catch (error) {
              console.error("Error removing from wishlist:", error);
              Alert.alert("Error", "Failed to remove item from saved collection");
            }
          },
          style: "destructive"
        }
      ]
    );
  };

  if (loading && collectionsLoading) return <ActivityIndicator style={{ flex: 1 }} size="large" color="#FF6B6B" />;

  // Navigate to collection detail screen
  const navigateToCollection = (collection) => {
    if (collection.id === 'all') {
      setViewMode('all');
    } else {
      navigation.navigate('CollectionDetail', {
        collectionId: collection.id,
        collectionName: collection.name
      });
    }
  };
  // Render collection item
  const renderCollectionItem = ({ item }) => {
    // Find the most recent item in this collection to use as cover image
    let coverImageUrl = null;

    if (item.id === 'all' && items.length > 0) {
      // For "All Items", use the first item's image
      coverImageUrl = items[0].imageUrl;
    } else if (item.latestItemImageUrl && item.itemCount > 0) {
      // Only use the latest item's image URL if the collection actually has items
      coverImageUrl = item.latestItemImageUrl;
    }

    // Show options menu for custom collections only (not for "All Saved Items")
    const showOptionsMenu = () => {
      if (item.id === 'all') return;

      setSelectedCollection(item); // Keep this to know which collection is being acted upon
      setOptionsModalVisible(true); // Show custom modal instead of Alert.alert
    };

    return (
      <TouchableOpacity
        style={styles.collectionCard}
        onPress={() => navigateToCollection(item)}
        onLongPress={showOptionsMenu}
        activeOpacity={0.7}
        delayLongPress={500}
      >
        <View style={styles.collectionImageContainer}>
          {coverImageUrl ? (
            <Image source={{ uri: coverImageUrl }} style={styles.collectionImage} />
          ) : (
            <View style={styles.collectionImagePlaceholder}>
              <Ionicons name="bookmark" size={30} color="#FF6B6B" />
            </View>
          )}

          {/* Options button for custom collections */}
          {!item.isDefault && (
            <TouchableOpacity
              style={styles.collectionOptionsButton}
              onPress={showOptionsMenu}
            >
              <Ionicons name="ellipsis-vertical" size={18} color="#fff" />
            </TouchableOpacity>
          )}
        </View>
        <Text style={styles.collectionName}>{item.name}</Text>
        {item.itemCount !== undefined && !item.isDefault && (
          <Text style={styles.collectionItemCount}>{item.itemCount} items</Text>
        )}
        {item.id === 'all' && (
          <Text style={styles.collectionItemCount}>
            {item.uniqueItemCount || 0} items
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  // Note: This renderItem function is used in CategoryItemsScreen.js
  // We're keeping it here for reference and potential future use

  // Render a category section
  const renderCategorySection = (category) => {
    const categoryItems = itemsByCategory[category] || [];
    if (categoryItems.length === 0) return null;

    const latestItem = categoryItems[0]; // First item is the latest due to sorting

    return (
      <View key={category} style={styles.categorySection}>
        <TouchableOpacity
          style={styles.categoryHeader}
          onPress={() => navigation.navigate('CategoryItems', {
            category,
            items: categoryItems,
            userId,
            onRemoveItem: removeFromWishlist
          })}
        >
          <View style={styles.categoryTitleContainer}>
            <Text style={styles.categoryTitle}>{category}</Text>
            <Text style={styles.categoryItemCount}>{categoryItems.length} items</Text>
          </View>
          <Ionicons name="chevron-forward" size={24} color="#FF6B6B" />
        </TouchableOpacity>

        <View style={styles.categoryPreview}>
          <TouchableOpacity
            style={styles.categoryPreviewImage}
            onPress={() => navigation.navigate('ItemDetails', { itemId: latestItem.id })}
          >
            <Image source={{ uri: latestItem.imageUrl }} style={styles.categoryImage} />
          </TouchableOpacity>
          <View style={styles.categoryPreviewInfo}>
            <Text style={styles.categoryPreviewTitle} numberOfLines={2}>{latestItem.title}</Text>
            <Text style={styles.categoryPreviewSubtitle}>Latest saved item</Text>
          </View>
        </View>
      </View>
    );
  };

  // Render all items view grouped by category
  const renderAllItems = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
          <Text style={styles.loadingText}>Loading saved items...</Text>
        </View>
      );
    }

    if (categories.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="bookmark-outline" size={60} color="#ccc" style={styles.emptyIcon} />
          <Text style={styles.empty}>No saved items found.</Text>
          <Text style={styles.emptySubText}>Tap the bookmark icon on items you like to save them here!</Text>
        </View>
      );
    }

    return (
      <ScrollView
        style={styles.categoriesContainer}
        contentContainerStyle={styles.categoriesContent}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={fetchWishlist} />
        }
      >
        {categories.map(category => renderCategorySection(category))}
      </ScrollView>
    );
  };

  // Render collections grid
  const renderCollections = () => (
    <FlatList
      data={collections}
      keyExtractor={item => item.id}
      renderItem={renderCollectionItem}
      numColumns={2}
      key="two-column-list" // Add a key to force re-render when numColumns changes
      columnWrapperStyle={styles.collectionRow}
      ListEmptyComponent={
        collectionsLoading ? (
          <ActivityIndicator style={{ marginTop: 30 }} size="large" color="#FF6B6B" />
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="folder-outline" size={60} color="#ccc" style={styles.emptyIcon} />
            <Text style={styles.empty}>No collections found.</Text>
            <Text style={styles.emptySubText}>Create your first collection by tapping the + button above!</Text>
          </View>
        )
      }
      contentContainerStyle={styles.collectionsGrid}
      refreshing={collectionsLoading}
      onRefresh={fetchCollections}
    />
  );

  const handleOptions = (collection) => {
    setSelectedCollection(collection); // Keep this to know which collection is being acted upon
    setOptionsModalVisible(true); // Show custom modal instead of Alert.alert
  };

  // Helper functions for the custom options modal buttons
  const onRenamePress = () => {
    setOptionsModalVisible(false);
    if (selectedCollection) {
      setNewCollectionName(selectedCollection.name); // Pre-fill rename input
      setShowRenameModal(true);
    }
  };

  const onDeletePress = () => {
    if (selectedCollection) {
      deleteCollection(selectedCollection.id);
    }
    setOptionsModalVisible(false);
  };

  const onCancelPress = () => {
    setOptionsModalVisible(false);
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.headerContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => {
              if (viewMode === 'all') {
                setViewMode('collections');
              } else {
                isTabScreen ? navigation.navigate('ClothingFeed') : navigation.goBack();
              }
            }}
          >
            <Ionicons name="arrow-back-outline" size={24} color="#FF6B6B" />
          </TouchableOpacity>
          <Text style={styles.header}>
            {viewMode === 'collections' ? 'Saved Collections' : 'All Saved Items'}
          </Text>
          {viewMode === 'collections' ? (
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowCreateModal(true)}
            >
              <Ionicons name="add" size={24} color="#FF6B6B" />
            </TouchableOpacity>
          ) : (
            <View style={styles.placeholderButton} />
          )}
        </View>

        {error ? (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle-outline" size={60} color="#FF6B6B" />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => {
                fetchWishlist();
                fetchCollections();
              }}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          viewMode === 'collections' ? renderCollections() : renderAllItems()
        )}
      </View>

      {/* Create Collection Modal */}
      <Modal
        visible={showCreateModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Create New Collection</Text>
              <TouchableOpacity
                onPress={() => setShowCreateModal(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#555" />
              </TouchableOpacity>
            </View>

            <TextInput
              style={styles.collectionNameInput}
              placeholder="Collection name"
              value={newCollectionName}
              onChangeText={setNewCollectionName}
              maxLength={30}
              autoFocus
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowCreateModal(false);
                  setNewCollectionName('');
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.createButton]}
                onPress={createCollection}
              >
                <Text style={styles.createButtonText}>Create</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Rename Collection Modal */}
      <Modal
        visible={showRenameModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowRenameModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Rename Collection</Text>
              <TouchableOpacity
                onPress={() => setShowRenameModal(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#555" />
              </TouchableOpacity>
            </View>

            <TextInput
              style={styles.collectionNameInput}
              placeholder="Collection name"
              value={newCollectionRename}
              onChangeText={setNewCollectionRename}
              maxLength={30}
              autoFocus
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowRenameModal(false);
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.createButton]}
                onPress={renameCollection}
              >
                <Text style={styles.createButtonText}>Rename</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Custom Modal for Collection Options */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={optionsModalVisible}
        onRequestClose={onCancelPress}>
        <TouchableOpacity
          style={styles.optionsModalOverlay}
          activeOpacity={1}
          onPressOut={onCancelPress} // Close when tapping outside the modal content
        >
          <View
            style={styles.optionsModalContent}
            onStartShouldSetResponder={() => true} // Prevents taps inside from propagating to overlay
          >
            <Text style={styles.optionsModalTitle}>{selectedCollection?.name}</Text>
            <Text style={styles.optionsModalSubtitle}>Collection options</Text>

            <View style={styles.optionsModalActionsRow}>
              <TouchableOpacity style={styles.actionButton} onPress={onRenamePress}>
                <Text style={styles.actionButtonText}>RENAME</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton} onPress={onDeletePress}>
                <Text style={[styles.actionButtonText, styles.destructiveButtonText]}>DELETE</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton} onPress={onCancelPress}>
                <Text style={styles.actionButtonText}>CANCEL</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f8f8f8',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  container: {
    flex: 1,
    paddingTop: 20,
    paddingHorizontal: 16,
    backgroundColor: '#f8f8f8',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 18,
    width: '100%',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B6B',
    letterSpacing: 1,
    flex: 1,
    textAlign: 'center',
  },
  backButton: {
    padding: 10,
    borderRadius: 20,
    width: 40,
  },
  placeholderButton: {
    width: 40,
  },
  addButton: {
    padding: 10,
    borderRadius: 20,
    width: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  listContent: {
    paddingBottom: 30,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    marginBottom: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    elevation: 1,
  },
  itemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  removeButton: {
    padding: 8,
    marginLeft: 8,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 14,
    backgroundColor: '#eee',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 4,
  },
  category: {
    fontSize: 14,
    color: '#888',
  },
  // Collection styles
  collectionsGrid: {
    paddingTop: 10,
    paddingBottom: 30,
  },
  collectionRow: {
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  collectionCard: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    alignItems: 'center',
  },
  collectionImageContainer: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 10,
  },
  collectionImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  collectionImagePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  collectionOptionsButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  collectionName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 4,
  },
  collectionItemCount: {
    fontSize: 12,
    color: '#888',
    textAlign: 'center',
  },
  // Category styles
  categoriesContainer: {
    flex: 1,
  },
  categoriesContent: {
    paddingBottom: 30,
  },
  categorySection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  categoryTitleContainer: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  categoryItemCount: {
    fontSize: 14,
    color: '#888',
  },
  categoryPreview: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  categoryPreviewImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: 16,
  },
  categoryImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  categoryPreviewInfo: {
    flex: 1,
  },
  categoryPreviewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  categoryPreviewSubtitle: {
    fontSize: 14,
    color: '#888',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '85%',
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  collectionNameInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  modalButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    marginLeft: 10,
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
  },
  cancelButtonText: {
    color: '#666',
    fontWeight: '500',
  },
  createButton: {
    backgroundColor: '#FF6B6B',
  },
  createButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  // Custom Modal styles
  optionsModalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Slightly darker overlay for more focus
  },
  optionsModalContent: { 
    width: '85%', 
    maxWidth: 340, // Slightly wider for better text flow
    backgroundColor: '#FFFFFF', // Explicit white background
    borderRadius: 12, // More rounded corners
    paddingVertical: 24, // Increased vertical padding
    paddingHorizontal: 20, // Horizontal padding
    alignItems: 'center', // Center align title and subtitle
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 }, // Softer shadow
    shadowOpacity: 0.15, // Softer shadow
    shadowRadius: 10, // Softer shadow
    elevation: 8, // Adjusted elevation for Android
  },
  optionsModalTitle: {
    fontSize: 20, // Larger title
    fontWeight: '600', // Semi-bold
    marginBottom: 8, 
    color: '#2C3E50', // Darker, more neutral color
    textAlign: 'center',
  },
  optionsModalSubtitle: {
    fontSize: 15, // Slightly larger subtitle
    marginBottom: 24, // Increased margin for separation
    color: '#7F8C8D', // Softer color for subtitle
    textAlign: 'center',
    paddingHorizontal: 10, // Add padding if subtitle is long
  },
  optionsModalActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around', // Distribute buttons more evenly
    width: '100%',
    marginTop: 16, 
    paddingHorizontal: 10, // Add some padding to the row itself
  },
  actionButton: {
    paddingVertical: 12, // More touch surface
    paddingHorizontal: 16, // More touch surface
    borderRadius: 8, // Rounded buttons
    minWidth: 90, // Minimum width for buttons
    alignItems: 'center', // Center text in button
    marginHorizontal: 5, // Add small margin between buttons if needed
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500', // Medium weight
    color: '#3498DB', // A pleasant blue for primary actions
    textTransform: 'uppercase',
  },
  destructiveButtonText: {
    color: '#E74C3C', // A clear red for destructive actions
  },
  // Empty and error states
  empty: {
    textAlign: 'center',
    color: '#888',
    marginTop: 10,
    fontSize: 16,
  },
  emptySubText: {
    textAlign: 'center',
    color: '#aaa',
    marginTop: 5,
    fontSize: 14,
    paddingHorizontal: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 40,
  },
  emptyIcon: {
    marginBottom: 15,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF6B6B',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default UserWishlist;
