/**
 * TINDER-STYLE SWIPE CONFIGURATION
 * 
 * This file contains all configurable parameters for the Tinder-style
 * swiping animation system. Modify these values to customize the behavior.
 */

import { Dimensions } from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export const TINDER_SWIPE_CONFIG = {
  // ===== GESTURE THRESHOLDS =====
  
  /**
   * Distance threshold for swipe completion (as percentage of screen width)
   * 0.3 = 30% of screen width
   * Range: 0.2 - 0.5 (20% - 50%)
   */
  SWIPE_THRESHOLD_PERCENTAGE: 0.3,
  
  /**
   * Minimum velocity for auto-complete swipe (pixels per second)
   * Higher values require faster swipes
   * Range: 500 - 1500
   */
  VELOCITY_THRESHOLD: 800,
  
  /**
   * Minimum vertical velocity for upward swipe (add to cart)
   * Range: 300 - 800
   */
  VERTICAL_VELOCITY_THRESHOLD: 500,
  
  /**
   * Minimum vertical distance for upward swipe
   * Range: 50 - 150
   */
  VERTICAL_DISTANCE_THRESHOLD: 100,

  // ===== VISUAL EFFECTS =====
  
  /**
   * Maximum rotation angle during swipe (degrees)
   * Range: 10 - 25
   */
  MAX_ROTATION: 15,
  
  /**
   * Rotation intensity factor
   * Higher values = more rotation for same distance
   * Range: 0.05 - 0.2
   */
  ROTATION_FACTOR: 0.1,
  
  /**
   * Card scale factor during drag
   * 1.0 = no scaling, 0.95 = scales down to 95%
   * Range: 0.9 - 1.0
   */
  SCALE_FACTOR: 0.95,
  
  /**
   * Color overlay opacity for swipe feedback
   * Range: 0.3 - 0.8
   */
  OVERLAY_OPACITY: 0.7,

  // ===== ANIMATION TIMING =====
  
  /**
   * Spring animation configuration for smooth movements
   * damping: Controls bounce (higher = less bounce)
   * stiffness: Controls speed (higher = faster)
   * mass: Controls weight feeling (higher = heavier)
   */
  SPRING_CONFIG: {
    damping: 20,
    stiffness: 300,
    mass: 1,
  },
  
  /**
   * Fast spring for quick snappy movements
   */
  FAST_SPRING_CONFIG: {
    damping: 30,
    stiffness: 400,
    mass: 0.8,
  },

  // ===== STACK BEHAVIOR =====
  
  /**
   * Scale difference between stacked cards
   * 0.05 = each card behind is 5% smaller
   * Range: 0.03 - 0.08
   */
  STACK_SCALE_FACTOR: 0.05,
  
  /**
   * Vertical offset between stacked cards (pixels)
   * Range: 15 - 30
   */
  STACK_OFFSET: 20,
  
  /**
   * Number of visible cards in stack
   * Range: 2 - 4
   */
  VISIBLE_CARDS: 3,

  // ===== COLORS =====
  
  /**
   * Color for "like" swipe feedback
   */
  LIKE_COLOR: 'rgba(76, 175, 80, 0.8)', // Green
  
  /**
   * Color for "dislike" swipe feedback
   */
  DISLIKE_COLOR: 'rgba(244, 67, 54, 0.8)', // Red
  
  /**
   * Color for swipe indicator background
   */
  INDICATOR_BACKGROUND: 'rgba(255, 255, 255, 0.9)',

  // ===== PERFORMANCE =====
  
  /**
   * Minimum movement to trigger direction detection (pixels)
   * Prevents jittery feedback on small movements
   * Range: 10 - 30
   */
  MIN_MOVEMENT_THRESHOLD: 20,
  
  /**
   * Debounce time for gesture completion (milliseconds)
   * Prevents multiple rapid swipes
   * Range: 100 - 300
   */
  COMPLETION_DELAY: 100,

  // ===== ACCESSIBILITY =====
  
  /**
   * Enable haptic feedback on swipe completion
   */
  HAPTIC_FEEDBACK_ENABLED: true,
  
  /**
   * Enable sound feedback on swipe completion
   */
  SOUND_FEEDBACK_ENABLED: false,
};

// ===== COMPUTED VALUES =====
// These are calculated based on screen dimensions and config

export const COMPUTED_CONFIG = {
  SWIPE_THRESHOLD: SCREEN_WIDTH * TINDER_SWIPE_CONFIG.SWIPE_THRESHOLD_PERCENTAGE,
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
};

// ===== PRESET CONFIGURATIONS =====

export const PRESET_CONFIGS = {
  /**
   * Sensitive: Easy to swipe, good for casual browsing
   */
  SENSITIVE: {
    ...TINDER_SWIPE_CONFIG,
    SWIPE_THRESHOLD_PERCENTAGE: 0.2,
    VELOCITY_THRESHOLD: 600,
    MAX_ROTATION: 20,
  },
  
  /**
   * Standard: Balanced settings, good for most users
   */
  STANDARD: TINDER_SWIPE_CONFIG,
  
  /**
   * Precise: Requires deliberate swipes, good for careful selection
   */
  PRECISE: {
    ...TINDER_SWIPE_CONFIG,
    SWIPE_THRESHOLD_PERCENTAGE: 0.4,
    VELOCITY_THRESHOLD: 1000,
    MAX_ROTATION: 10,
  },
};

/**
 * Get configuration based on preset name
 * @param {string} preset - 'sensitive', 'standard', or 'precise'
 * @returns {object} Configuration object
 */
export const getPresetConfig = (preset = 'standard') => {
  const presetKey = preset.toUpperCase();
  return PRESET_CONFIGS[presetKey] || PRESET_CONFIGS.STANDARD;
};
