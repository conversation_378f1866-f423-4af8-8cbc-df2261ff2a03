import React, { useState, useEffect } from 'react';
import { View, Text, Button, StyleSheet, ScrollView } from 'react-native';
import { auth, db } from './firebase.config';
import { onAuthStateChanged, signOut } from 'firebase/auth';
import { collection, collectionGroup, getDocs, doc, getDoc } from 'firebase/firestore';

const FirebaseTest = () => {
  const [user, setUser] = useState(null);
  const [testResults, setTestResults] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      if (currentUser) {
        addTestResult('Authentication', 'Success', `Logged in as ${currentUser.email}`);
      } else {
        addTestResult('Authentication', 'Warning', 'Not logged in');
      }
    });

    return () => unsubscribe();
  }, []);

  const addTestResult = (test, status, message) => {
    setTestResults(prev => [...prev, { test, status, message, timestamp: new Date().toISOString() }]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testUserCollection = async () => {
    if (!user) {
      addTestResult('User Collection', 'Error', 'Not logged in');
      return;
    }

    setLoading(true);
    try {
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      if (userDoc.exists()) {
        addTestResult('User Collection', 'Success', `User document found: ${JSON.stringify(userDoc.data().name || 'No name')}`);
      } else {
        addTestResult('User Collection', 'Warning', 'User document not found');
      }
    } catch (error) {
      addTestResult('User Collection', 'Error', `Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testClothingCollection = async () => {
    setLoading(true);
    try {
      const querySnapshot = await getDocs(collection(db, 'clothingItems'));
      addTestResult('Clothing Collection', 'Success', `Found ${querySnapshot.docs.length} items`);
    } catch (error) {
      addTestResult('Clothing Collection', 'Error', `Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testCartCollection = async () => {
    if (!user) {
      addTestResult('Cart Collection', 'Error', 'Not logged in');
      return;
    }

    setLoading(true);
    try {
      const querySnapshot = await getDocs(collection(db, 'users', user.uid, 'cart'));
      addTestResult('Cart Collection', 'Success', `Found ${querySnapshot.docs.length} items in cart`);
    } catch (error) {
      addTestResult('Cart Collection', 'Error', `Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testCategoriesCollection = async () => {
    setLoading(true);
    try {
      const querySnapshot = await getDocs(collection(db, 'categories'));
      addTestResult('Categories Collection', 'Success', `Found ${querySnapshot.docs.length} categories`);
    } catch (error) {
      addTestResult('Categories Collection', 'Error', `Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testGlobalCustomCategoriesCollection = async () => {
    setLoading(true);
    try {
      const querySnapshot = await getDocs(collection(db, 'customCategories'));
      addTestResult('Global Custom Categories', 'Success', `Found ${querySnapshot.docs.length} global custom categories`);
    } catch (error) {
      addTestResult('Global Custom Categories', 'Error', `Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testCollectionGroupCategories = async () => {
    setLoading(true);
    try {
      const querySnapshot = await getDocs(collectionGroup(db, 'customCategories'));
      addTestResult('Collection Group Categories', 'Success', `Found ${querySnapshot.docs.length} categories across all users`);
    } catch (error) {
      addTestResult('Collection Group Categories', 'Error', `Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testCustomCategoriesCollection = async () => {
    if (!user) {
      addTestResult('Custom Categories', 'Error', 'Not logged in');
      return;
    }

    setLoading(true);
    try {
      const querySnapshot = await getDocs(collection(db, 'users', user.uid, 'customCategories'));
      addTestResult('Custom Categories', 'Success', `Found ${querySnapshot.docs.length} custom categories`);
    } catch (error) {
      addTestResult('Custom Categories', 'Error', `Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testUserCollections = async () => {
    if (!user) {
      addTestResult('User Collections', 'Error', 'Not logged in');
      return;
    }

    setLoading(true);
    try {
      const querySnapshot = await getDocs(collection(db, 'users', user.uid, 'collections'));
      addTestResult('User Collections', 'Success', `Found ${querySnapshot.docs.length} collections`);
    } catch (error) {
      addTestResult('User Collections', 'Error', `Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const runAllTests = async () => {
    clearResults();
    await testUserCollection();
    await testClothingCollection();
    await testCartCollection();
    await testCategoriesCollection();
    await testGlobalCustomCategoriesCollection();
    await testCollectionGroupCategories();
    await testCustomCategoriesCollection();
    await testUserCollections();
  };

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      addTestResult('Sign Out', 'Success', 'Signed out successfully');
    } catch (error) {
      addTestResult('Sign Out', 'Error', `Error: ${error.message}`);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Firebase Connection Test</Text>

      <View style={styles.userInfo}>
        <Text style={styles.userInfoText}>
          {user ? `Logged in as: ${user.email}` : 'Not logged in'}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button title="Run All Tests" onPress={runAllTests} disabled={loading} />
        <Button title="Clear Results" onPress={clearResults} disabled={loading} />
        {user && <Button title="Sign Out" onPress={handleSignOut} disabled={loading} />}
      </View>

      <ScrollView style={styles.resultsContainer}>
        {testResults.map((result, index) => (
          <View
            key={index}
            style={[
              styles.resultItem,
              result.status === 'Success' ? styles.successResult :
              result.status === 'Warning' ? styles.warningResult :
              styles.errorResult
            ]}
          >
            <Text style={styles.resultTest}>{result.test}</Text>
            <Text style={styles.resultStatus}>{result.status}</Text>
            <Text style={styles.resultMessage}>{result.message}</Text>
          </View>
        ))}
        {loading && <Text style={styles.loadingText}>Running tests...</Text>}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  userInfo: {
    backgroundColor: '#e0e0e0',
    padding: 10,
    borderRadius: 5,
    marginBottom: 20,
  },
  userInfoText: {
    fontSize: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  resultsContainer: {
    flex: 1,
  },
  resultItem: {
    padding: 15,
    borderRadius: 5,
    marginBottom: 10,
  },
  successResult: {
    backgroundColor: '#d4edda',
  },
  warningResult: {
    backgroundColor: '#fff3cd',
  },
  errorResult: {
    backgroundColor: '#f8d7da',
  },
  resultTest: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 5,
  },
  resultStatus: {
    fontStyle: 'italic',
    marginBottom: 5,
  },
  resultMessage: {
    fontSize: 14,
  },
  loadingText: {
    textAlign: 'center',
    marginTop: 10,
    fontStyle: 'italic',
  },
});

export default FirebaseTest;
