import { db, auth } from '../firebase.config';
import { collection, query, where, getDocs, updateDoc, doc, deleteDoc } from 'firebase/firestore';

/**
 * Set admin status for a user by email
 * @param {string} userEmail - The email of the user to make admin
 * @param {boolean} isAdmin - Whether to grant or revoke admin status
 * @returns {Promise<boolean>} Success status
 */
export const setUserAdminStatus = async (userEmail, isAdmin = true) => {
  try {
    // Check if user is authenticated
    if (!auth.currentUser) {
      console.log('No authenticated user, cannot set admin status');
      return false;
    }

    // Find user by email
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('email', '==', userEmail));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      console.error('User not found with email:', userEmail);
      return false;
    }

    // Update the first matching user (should be unique)
    const userDoc = querySnapshot.docs[0];
    await updateDoc(doc(db, 'users', userDoc.id), {
      isAdmin: isAdmin
    });

    console.log(`Successfully ${isAdmin ? 'granted' : 'revoked'} admin status for:`, userEmail);
    return true;
  } catch (error) {
    console.error('Error setting admin status:', error);
    return false;
  }
};

/**
 * Check if a user is an admin
 * @param {string} userId - The user ID to check
 * @returns {Promise<boolean>} Whether the user is an admin
 */
export const checkAdminStatus = async (userId) => {
  try {
    // Check if user is authenticated and userId is provided
    if (!userId || !auth.currentUser) {
      console.log('No authenticated user or userId provided, returning false for admin status');
      return false;
    }

    // Additional check to ensure the current user matches the userId being checked
    if (auth.currentUser.uid !== userId) {
      console.log('User ID mismatch, returning false for admin status');
      return false;
    }

    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('uid', '==', userId));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return false;
    }

    const userData = querySnapshot.docs[0].data();
    return userData.isAdmin === true;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
};

/**
 * Get all admin users
 * @returns {Promise<Array>} List of admin users
 */
export const getAllAdminUsers = async () => {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('isAdmin', '==', true));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting admin users:', error);
    return [];
  }
};

/**
 * Get platform statistics for admin dashboard
 * @returns {Promise<Object>} Platform statistics
 */
export const getPlatformStatistics = async () => {
  try {
    const [
      usersSnapshot,
      sellersSnapshot,
      buyersSnapshot,
      listingsSnapshot,
      ordersSnapshot
    ] = await Promise.all([
      getDocs(collection(db, 'users')),
      getDocs(query(collection(db, 'users'), where('isSeller', '==', true))),
      getDocs(query(collection(db, 'users'), where('isSeller', '==', false))),
      getDocs(collection(db, 'items')),
      getDocs(collection(db, 'orders'))
    ]);

    return {
      totalUsers: usersSnapshot.size,
      totalSellers: sellersSnapshot.size,
      totalBuyers: buyersSnapshot.size,
      totalListings: listingsSnapshot.size,
      totalOrders: ordersSnapshot.size
    };
  } catch (error) {
    console.error('Error getting platform statistics:', error);
    return {
      totalUsers: 0,
      totalSellers: 0,
      totalBuyers: 0,
      totalListings: 0,
      totalOrders: 0
    };
  }
};

/**
 * Suspend or unsuspend a user
 * @param {string} userId - The user ID to suspend/unsuspend
 * @param {boolean} suspend - Whether to suspend (true) or unsuspend (false)
 * @param {string} reason - Reason for suspension
 * @returns {Promise<boolean>} Success status
 */
export const suspendUser = async (userId, suspend = true, reason = '') => {
  try {
    await updateDoc(doc(db, 'users', userId), {
      isSuspended: suspend,
      suspendedAt: suspend ? new Date() : null,
      suspensionReason: suspend ? reason : null,
      suspendedBy: suspend ? auth.currentUser.uid : null
    });

    console.log(`User ${suspend ? 'suspended' : 'unsuspended'} successfully`);
    return true;
  } catch (error) {
    console.error('Error suspending/unsuspending user:', error);
    return false;
  }
};

/**
 * Delete a user account (admin only)
 * @param {string} userId - The user ID to delete
 * @returns {Promise<boolean>} Success status
 */
export const deleteUserAccount = async (userId) => {
  try {
    // Note: This only deletes the Firestore document
    // You would need Firebase Admin SDK to delete the actual auth account
    await deleteDoc(doc(db, 'users', userId));

    console.log('User account deleted successfully');
    return true;
  } catch (error) {
    console.error('Error deleting user account:', error);
    return false;
  }
};

// Example usage:
// To make a user admin, call this in your app:
// import { setUserAdminStatus } from './utils/adminUtils';
// await setUserAdminStatus('<EMAIL>', true);
