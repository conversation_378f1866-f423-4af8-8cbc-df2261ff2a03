/**
 * Utility functions for the clothing feed
 */

/**
 * Shuffles an array using <PERSON><PERSON><PERSON> algorithm
 * @param {Array} array - Array to shuffle
 * @returns {Array} - Shuffled array
 */
export function shuffleArray(array) {
  let currentIndex = array.length, randomIndex;
  while (currentIndex !== 0) {
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;
    [array[currentIndex], array[randomIndex]] = [
      array[randomIndex], array[currentIndex]];
  }
  return array;
}

/**
 * Filters items based on search query and category
 * @param {Array} items - Items to filter
 * @param {string} searchQuery - Search query
 * @param {string} activeCategory - Active category
 * @returns {Array} - Filtered items
 */
export function filterItems(items, searchQuery, activeCategory) {
  let filtered = items;

  // Filter by category
  if (activeCategory && activeCategory !== 'All') {
    filtered = filtered.filter(item =>
      item.category && item.category.toLowerCase() === activeCategory.toLowerCase()
    );
  }

  // Filter by search query
  if (searchQuery && searchQuery.trim()) {
    const query = searchQuery.toLowerCase().trim();
    filtered = filtered.filter(item =>
      (item.title && item.title.toLowerCase().includes(query)) ||
      (item.brand && item.brand.toLowerCase().includes(query)) ||
      (item.category && item.category.toLowerCase().includes(query))
    );
  }

  return filtered;
}

/**
 * Removes duplicates from an array based on a key
 * @param {Array} array - Array to deduplicate
 * @param {string} key - Key to use for deduplication
 * @returns {Array} - Deduplicated array
 */
export function removeDuplicates(array, key = 'id') {
  const seen = new Set();
  return array.filter(item => {
    const keyValue = item[key];
    if (seen.has(keyValue)) {
      return false;
    }
    seen.add(keyValue);
    return true;
  });
}

/**
 * Validates if an item has required fields
 * @param {Object} item - Item to validate
 * @returns {boolean} - Whether item is valid
 */
export function isValidItem(item) {
  return item &&
    item.id &&
    item.imageUrl &&
    (item.title || item.brand);
}

/**
 * Formats price for display
 * @param {number} price - Price to format
 * @returns {string} - Formatted price
 */
export function formatPrice(price) {
  if (!price || isNaN(price)) return '';
  return `₹${Math.round(price)}`;
}

/**
 * Checks if a category is custom (not in default categories)
 * @param {string} category - Category to check
 * @param {Array} defaultCategories - Default categories array
 * @returns {boolean} - Whether category is custom
 */
export function isCustomCategory(category, defaultCategories) {
  return category &&
    category !== 'All' &&
    !defaultCategories.includes(category);
}

/**
 * Generates a cache buster timestamp
 * @returns {number} - Timestamp for cache busting
 */
export function generateCacheBuster() {
  return Math.floor(Date.now() / 10000) * 10000; // Changes every ~10 seconds
}

/**
 * Adds cache buster to URL
 * @param {string} url - Original URL
 * @returns {string} - URL with cache buster
 */
export function addCacheBuster(url) {
  if (!url) return null;

  const timestamp = generateCacheBuster();
  const urlWithCacheBuster = url.includes('?')
    ? `${url}&_cb=${timestamp}`
    : `${url}?_cb=${timestamp}`;

  return urlWithCacheBuster;
}

/**
 * Debounces a function
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
