import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Dimensions
} from 'react-native';

const { width } = Dimensions.get('window');

const QuantitySelectionModal = ({ visible, onClose, onSelect }) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={styles.modalContainer} onStartShouldSetResponder={() => true}>
          <Text style={styles.title}>Select Quantity</Text>
          <Text style={styles.subtitle}>Choose a quantity or enter custom</Text>
          
          <View style={styles.optionsRow}>
            <TouchableOpacity 
              style={styles.quantityButton} 
              onPress={() => {
                onSelect('1');
                onClose();
              }}
            >
              <Text style={styles.quantityText}>1</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.quantityButton}
              onPress={() => {
                onSelect('2');
                onClose();
              }}
            >
              <Text style={styles.quantityText}>2</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.quantityButton}
              onPress={() => {
                onSelect('3');
                onClose();
              }}
            >
              <Text style={styles.quantityText}>3</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity 
              style={styles.customButton}
              onPress={() => {
                onSelect('custom');
                onClose();
              }}
            >
              <Text style={styles.customButtonText}>Enter Custom</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.removeButton}
              onPress={() => {
                onSelect('remove');
                onClose();
              }}
            >
              <Text style={styles.removeButtonText}>Remove Item</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.85,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    ...Platform.select({
      android: {
        elevation: 5,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
    }),
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
  },
  optionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 20,
  },
  quantityButton: {
    width: 60,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  quantityText: {
    fontSize: 18,
    color: '#007AFF', // iOS blue color
    fontWeight: '500',
  },
  actionButtonsContainer: {
    width: '100%',
  },
  customButton: {
    width: '100%',
    padding: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  customButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  removeButton: {
    width: '100%',
    padding: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  removeButtonText: {
    fontSize: 16,
    color: '#FF3B30', // iOS red color for destructive actions
    fontWeight: '500',
  },
  cancelButton: {
    width: '100%',
    padding: 14,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  }
});

export default QuantitySelectionModal;
