import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, FlatList, KeyboardAvoidingView, Platform, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { collection, query, orderBy, onSnapshot, addDoc, serverTimestamp } from 'firebase/firestore';
import { db, auth } from '../firebase.config';

const MessageScreen = ({ route, navigation }) => {
  const { receiverId, receiverName, conversationId: propConversationId } = route.params || {};
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [conversationId, setConversationId] = useState(propConversationId);
  const flatListRef = useRef();
  const currentUserId = auth.currentUser?.uid;

  useEffect(() => {
    if (!currentUserId || !receiverId) {
      console.warn("User or receiver ID missing");
      setLoading(false);
      return;
    }

    const generatedId = [currentUserId, receiverId].sort().join('_');
    if (!conversationId) {
      setConversationId(generatedId);
    }
    const finalConversationId = conversationId || generatedId;

    const messagesQuery = query(
      collection(db, 'conversations', finalConversationId, 'messages'),
      orderBy('timestamp', 'asc')
    );

    const unsubscribe = onSnapshot(messagesQuery, (querySnapshot) => {
      const fetchedMessages = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate()
      }));
      setMessages(fetchedMessages);
      setLoading(false);
    }, (error) => {
      console.error("Error fetching messages: ", error);
      setLoading(false);
      Alert.alert("Error", "Could not load messages.");
    });

    return () => unsubscribe();
  }, [currentUserId, receiverId, conversationId]);

  const handleSend = async () => {
    if (newMessage.trim() && currentUserId && conversationId) {
      const messageText = newMessage;
      setNewMessage('');

      try {
        await addDoc(collection(db, 'conversations', conversationId, 'messages'), {
          text: messageText,
          senderId: currentUserId,
          timestamp: serverTimestamp(),
        });
      } catch (error) {
        console.error("Error sending message: ", error);
        setNewMessage(messageText);
        Alert.alert("Error", "Could not send message.");
      }
    }
  };

  const handleBlockUser = () => {
    Alert.alert(
      'Block User',
      `Are you sure you want to block ${receiverName}? You won't see their messages anymore.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Block', onPress: () => console.log('Block user:', receiverId), style: 'destructive' },
      ]
    );
  };

  const handleReportUser = () => {
    Alert.alert(
      'Report User',
      `Are you sure you want to report ${receiverName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Report', onPress: () => console.log('Report user:', receiverId) },
      ]
    );
  };

  const renderItem = ({ item }) => {
    const isMyMessage = item.senderId === currentUserId;
    return (
      <View style={[styles.messageRow, isMyMessage ? styles.myMessageRow : styles.otherMessageRow]}>
        <View style={[
          styles.messageBubble,
          isMyMessage ? styles.myMessageBubble : styles.otherMessageBubble
        ]}>
          <Text style={isMyMessage ? styles.myMessageText : styles.otherMessageText}>{item.text}</Text>
        </View>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
    >
      <View style={styles.header}>
         <TouchableOpacity onPress={() => navigation.goBack()} style={styles.headerButton}>
           <Ionicons name="chevron-back" size={30} color="#FF6B6B" />
         </TouchableOpacity>
         <Text style={styles.headerTitle}>{receiverName || 'Messages'}</Text>
         <View style={styles.headerRightButtons}>
            <TouchableOpacity onPress={handleBlockUser} style={styles.headerIconButton}>
              <Ionicons name="ban-outline" size={24} color="#ccc" />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleReportUser} style={styles.headerIconButton}>
              <Ionicons name="alert-circle-outline" size={24} color="#ccc" />
            </TouchableOpacity>
         </View>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
        </View>
      ) : (
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.messagesListContainer}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: false })}
          onLayout={() => flatListRef.current?.scrollToEnd({ animated: false })}
        />
      )}
      
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          placeholder="Type a message..."
          value={newMessage}
          onChangeText={setNewMessage}
          placeholderTextColor="#999"
          multiline
        />
        <TouchableOpacity style={styles.sendButton} onPress={handleSend} disabled={!newMessage.trim()}>
          <Ionicons name="send" size={24} color={newMessage.trim() ? '#FF6B6B' : '#ccc'} />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 10,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  headerButton: {
    padding: 5,
    width: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerRightButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    minWidth: 80,
  },
  headerIconButton: {
    padding: 5,
    marginLeft: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messagesListContainer: {
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  messageRow: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  myMessageRow: {
    justifyContent: 'flex-end',
  },
  otherMessageRow: {
    justifyContent: 'flex-start',
  },
  messageBubble: {
    maxWidth: '75%',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
  },
  myMessageBubble: {
    backgroundColor: '#FF6B6B',
    borderBottomRightRadius: 5,
  },
  otherMessageBubble: {
    backgroundColor: '#f0f0f0',
    borderBottomLeftRadius: 5,
  },
  myMessageText: {
    color: '#fff',
    fontSize: 16,
  },
  otherMessageText: {
    color: '#333',
    fontSize: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    alignItems: 'flex-end',
  },
  input: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: Platform.OS === 'ios' ? 10 : 8,
    fontSize: 16,
    maxHeight: 100,
    marginRight: 10,
  },
  sendButton: {
    padding: 10,
    marginBottom: Platform.OS === 'ios' ? 5 : 0,
  },
});

export default MessageScreen;