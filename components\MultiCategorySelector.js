import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  TextInput,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  getBroadCategories,
  getDetailedCategories,
  validateCategorySelection,
  searchCategories
} from '../utils/categoryHierarchy';

const MultiCategorySelector = ({
  selectedBroadCategories = [],
  selectedDetailedCategories = [],
  onBroadCategoriesChange,
  onDetailedCategoriesChange,
  disabled = false,
  style
}) => {
  const [broadCategories] = useState(getBroadCategories());
  const [availableDetailedCategories, setAvailableDetailedCategories] = useState([]);
  const [showBroadModal, setShowBroadModal] = useState(false);
  const [showDetailedModal, setShowDetailedModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredBroadCategories, setFilteredBroadCategories] = useState(broadCategories);
  const [filteredDetailedCategories, setFilteredDetailedCategories] = useState([]);

  // Update available detailed categories when broad categories change
  useEffect(() => {
    if (selectedBroadCategories.length > 0) {
      let detailed = [];
      selectedBroadCategories.forEach(broadCat => {
        detailed = [...detailed, ...getDetailedCategories(broadCat)];
      });
      // Remove duplicates
      detailed = [...new Set(detailed)];
      setAvailableDetailedCategories(detailed);
      setFilteredDetailedCategories(detailed);
    } else {
      setAvailableDetailedCategories([]);
      setFilteredDetailedCategories([]);
    }
  }, [selectedBroadCategories]);

  // Filter broad categories based on search
  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = broadCategories.filter(category =>
        category.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredBroadCategories(filtered);
    } else {
      setFilteredBroadCategories(broadCategories);
    }
  }, [searchQuery, broadCategories]);

  // Filter detailed categories based on search
  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = availableDetailedCategories.filter(category =>
        category.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredDetailedCategories(filtered);
    } else {
      setFilteredDetailedCategories(availableDetailedCategories);
    }
  }, [searchQuery, availableDetailedCategories]);

  const handleBroadCategoryToggle = (category) => {
    const newSelected = selectedBroadCategories.includes(category)
      ? selectedBroadCategories.filter(cat => cat !== category)
      : [...selectedBroadCategories, category];

    onBroadCategoriesChange(newSelected);

    // Remove detailed categories that are no longer available
    if (!selectedBroadCategories.includes(category)) {
      const detailedToRemove = getDetailedCategories(category);
      const newDetailedSelected = selectedDetailedCategories.filter(
        detCat => !detailedToRemove.includes(detCat)
      );
      if (newDetailedSelected.length !== selectedDetailedCategories.length) {
        onDetailedCategoriesChange(newDetailedSelected);
      }
    }
  };

  const handleDetailedCategoryToggle = (category) => {
    const newSelected = selectedDetailedCategories.includes(category)
      ? selectedDetailedCategories.filter(cat => cat !== category)
      : [...selectedDetailedCategories, category];

    onDetailedCategoriesChange(newSelected);
  };

  const openBroadModal = () => {
    if (!disabled) {
      setSearchQuery('');
      setShowBroadModal(true);
    }
  };

  const openDetailedModal = () => {
    if (!disabled && selectedBroadCategories.length > 0) {
      setSearchQuery('');
      setShowDetailedModal(true);
    }
  };

  const renderCategoryItem = ({ item: category, isSelected, onToggle, showCheckbox = true }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        isSelected && styles.selectedCategoryItem
      ]}
      onPress={() => onToggle(category)}
    >
      <Text
        style={[
          styles.categoryItemText,
          isSelected && styles.selectedCategoryItemText
        ]}
      >
        {category}
      </Text>
      {showCheckbox && (
        <Ionicons
          name={isSelected ? "checkbox" : "square-outline"}
          size={20}
          color={isSelected ? "#FF6B6B" : "#666"}
        />
      )}
    </TouchableOpacity>
  );

  const renderSelectedTags = (items, onRemove, emptyText) => (
    <View style={styles.selectedTagsContainer}>
      {items.length === 0 ? (
        <Text style={styles.emptyText}>{emptyText}</Text>
      ) : (
        <View style={styles.tagsWrapper}>
          {items.map((item, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{item}</Text>
              <TouchableOpacity
                onPress={() => onRemove(item)}
                style={styles.tagRemoveButton}
                disabled={disabled}
              >
                <Ionicons name="close" size={14} color="#FF6B6B" />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  const renderCategoryModal = (
    visible,
    onClose,
    categories,
    selectedItems,
    onToggle,
    title
  ) => (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{title}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search categories..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
            />
          </View>

          <FlatList
            data={categories}
            keyExtractor={(item) => item}
            renderItem={({ item }) => renderCategoryItem({
              item,
              isSelected: selectedItems.includes(item),
              onToggle: onToggle
            })}
            style={styles.categoriesList}
          />
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={[styles.container, style]}>
      {/* Broad Categories Section */}
      <View style={styles.selectorContainer}>
        <Text style={styles.label}>Category Types</Text>
        <TouchableOpacity
          style={[
            styles.selector,
            disabled && styles.selectorDisabled
          ]}
          onPress={openBroadModal}
          disabled={disabled}
        >
          <Text style={styles.selectorText}>
            {selectedBroadCategories.length > 0
              ? `${selectedBroadCategories.length} selected`
              : 'Select category types'}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#666" />
        </TouchableOpacity>

        {renderSelectedTags(
          selectedBroadCategories,
          (item) => handleBroadCategoryToggle(item),
          'No category types selected'
        )}
      </View>

      {/* Detailed Categories Section */}
      <View style={styles.selectorContainer}>
        <Text style={styles.label}>Specific Categories</Text>
        <TouchableOpacity
          style={[
            styles.selector,
            (disabled || selectedBroadCategories.length === 0) && styles.selectorDisabled
          ]}
          onPress={openDetailedModal}
          disabled={disabled || selectedBroadCategories.length === 0}
        >
          <Text style={styles.selectorText}>
            {selectedDetailedCategories.length > 0
              ? `${selectedDetailedCategories.length} selected`
              : selectedBroadCategories.length > 0
                ? 'Select specific categories'
                : 'Select category types first'}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#666" />
        </TouchableOpacity>

        {renderSelectedTags(
          selectedDetailedCategories,
          (item) => handleDetailedCategoryToggle(item),
          'No specific categories selected'
        )}
      </View>

      {/* Broad Category Modal */}
      {renderCategoryModal(
        showBroadModal,
        () => setShowBroadModal(false),
        filteredBroadCategories,
        selectedBroadCategories,
        handleBroadCategoryToggle,
        'Select Category Types'
      )}

      {/* Detailed Category Modal */}
      {renderCategoryModal(
        showDetailedModal,
        () => setShowDetailedModal(false),
        filteredDetailedCategories,
        selectedDetailedCategories,
        handleDetailedCategoryToggle,
        'Select Specific Categories'
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  selectorContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
    marginBottom: 10,
  },
  selectorDisabled: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
  },
  selectorText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectedTagsContainer: {
    minHeight: 40,
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  tagsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 10,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF6B6B',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
    margin: 3,
  },
  tagText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginRight: 5,
  },
  tagRemoveButton: {
    padding: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginVertical: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  categoriesList: {
    maxHeight: 400,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedCategoryItem: {
    backgroundColor: '#fff5f5',
  },
  categoryItemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectedCategoryItemText: {
    color: '#FF6B6B',
    fontWeight: '500',
  },
});

export default MultiCategorySelector;
